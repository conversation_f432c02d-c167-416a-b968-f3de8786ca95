<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DatabaseUserPermission extends Model
{
    protected $fillable = [
        'database_user_id',
        'database_id',
        'permissions',
        'granted_at',
        'granted_by',
        'is_active',
    ];

    protected $casts = [
        'permissions' => 'array',
        'granted_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    public function databaseUser(): BelongsTo
    {
        return $this->belongsTo(DatabaseUser::class);
    }

    public function database(): BelongsTo
    {
        return $this->belongsTo(Database::class);
    }

    public function grantedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'granted_by');
    }

    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->permissions ?? []);
    }
}
