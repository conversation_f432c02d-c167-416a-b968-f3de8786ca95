<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WebRedirect extends Model
{
    protected $fillable = [
        'domain_id',
        'source_url',
        'destination_url',
        'redirect_type',
        'is_active',
        'match_query_string',
        'is_regex',
        'description',
        'hit_count',
        'last_used_at',
    ];

    protected $casts = [
        'redirect_type' => 'integer',
        'is_active' => 'boolean',
        'match_query_string' => 'boolean',
        'is_regex' => 'boolean',
        'hit_count' => 'integer',
        'last_used_at' => 'datetime',
    ];

    const REDIRECT_TYPES = [
        301 => 'Permanent (301)',
        302 => 'Temporary (302)',
        303 => 'See Other (303)',
        307 => 'Temporary Redirect (307)',
        308 => 'Permanent Redirect (308)',
    ];

    const MATCH_TYPES = [
        'exact' => 'Exact Match',
        'wildcard' => 'Wildcard Match',
        'regex' => 'Regular Expression',
        'starts_with' => 'Starts With',
        'contains' => 'Contains',
    ];

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }

    public function subdomain(): BelongsTo
    {
        return $this->belongsTo(Subdomain::class);
    }

    public function getRedirectTypeNameAttribute(): string
    {
        return self::REDIRECT_TYPES[$this->redirect_type] ?? 'Unknown';
    }

    public function getMatchTypeNameAttribute(): string
    {
        return self::MATCH_TYPES[$this->match_type] ?? 'Unknown';
    }

    public function getFullSourceUrlAttribute(): string
    {
        $domain = $this->subdomain ? $this->subdomain->full_name : $this->domain->name;
        return 'https://' . $domain . $this->source_url;
    }

    public function getStatusAttribute(): string
    {
        return $this->is_active ? 'active' : 'inactive';
    }

    public function isPermanent(): bool
    {
        return in_array($this->redirect_type, [301, 308]);
    }

    public function isTemporary(): bool
    {
        return in_array($this->redirect_type, [302, 303, 307]);
    }

    public function matches(string $url): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $sourcePattern = $this->case_sensitive ? $this->source_url : strtolower($this->source_url);
        $testUrl = $this->case_sensitive ? $url : strtolower($url);

        return match($this->match_type) {
            'exact' => $sourcePattern === $testUrl,
            'wildcard' => fnmatch($sourcePattern, $testUrl),
            'regex' => preg_match($sourcePattern, $testUrl),
            'starts_with' => str_starts_with($testUrl, $sourcePattern),
            'contains' => str_contains($testUrl, $sourcePattern),
            default => false
        };
    }

    public function buildDestinationUrl(string $requestUrl): string
    {
        $destination = $this->destination_url;

        // Handle path preservation
        if ($this->preserve_path && $this->match_type === 'starts_with') {
            $remainingPath = substr($requestUrl, strlen($this->source_url));
            $destination = rtrim($destination, '/') . '/' . ltrim($remainingPath, '/');
        }

        // Handle query string preservation
        if ($this->preserve_query_string) {
            $queryString = parse_url($requestUrl, PHP_URL_QUERY);
            if ($queryString) {
                $separator = str_contains($destination, '?') ? '&' : '?';
                $destination .= $separator . $queryString;
            }
        }

        return $destination;
    }

    public function recordHit(): void
    {
        $this->increment('hit_count');
        $this->update(['last_hit_at' => now()]);
    }

    public function getHitCountTodayAttribute(): int
    {
        // This would require a separate hits tracking table for detailed analytics
        // For now, return 0 as placeholder
        return 0;
    }

    public function getHitCountThisMonthAttribute(): int
    {
        // This would require a separate hits tracking table for detailed analytics
        // For now, return 0 as placeholder
        return 0;
    }

    public function validate(): array
    {
        $errors = [];

        // Validate source URL
        if (empty($this->source_url)) {
            $errors[] = 'Source URL is required';
        } elseif (!str_starts_with($this->source_url, '/')) {
            $errors[] = 'Source URL must start with /';
        }

        // Validate destination URL
        if (empty($this->destination_url)) {
            $errors[] = 'Destination URL is required';
        } elseif (!filter_var($this->destination_url, FILTER_VALIDATE_URL) && !str_starts_with($this->destination_url, '/')) {
            $errors[] = 'Destination URL must be a valid URL or start with /';
        }

        // Validate regex pattern if using regex match
        if ($this->match_type === 'regex') {
            if (@preg_match($this->source_url, '') === false) {
                $errors[] = 'Invalid regular expression pattern';
            }
        }

        // Check for circular redirects
        if ($this->source_url === $this->destination_url) {
            $errors[] = 'Source and destination URLs cannot be the same';
        }

        return $errors;
    }

    public function isValid(): bool
    {
        return empty($this->validate());
    }

    public function test(string $testUrl): array
    {
        $result = [
            'matches' => false,
            'destination' => null,
            'redirect_type' => $this->redirect_type,
            'redirect_type_name' => $this->redirect_type_name,
        ];

        if ($this->matches($testUrl)) {
            $result['matches'] = true;
            $result['destination'] = $this->buildDestinationUrl($testUrl);
        }

        return $result;
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    public function scopePermanent($query)
    {
        return $query->whereIn('redirect_type', [301, 308]);
    }

    public function scopeTemporary($query)
    {
        return $query->whereIn('redirect_type', [302, 303, 307]);
    }

    public function scopePopular($query, int $minHits = 10)
    {
        return $query->where('hit_count', '>=', $minHits)
                    ->orderBy('hit_count', 'desc');
    }

    public function scopeRecentlyUsed($query, int $days = 30)
    {
        return $query->where('last_hit_at', '>=', now()->subDays($days))
                    ->orderBy('last_hit_at', 'desc');
    }

    public function scopeUnused($query, int $days = 90)
    {
        return $query->where(function($q) use ($days) {
            $q->whereNull('last_hit_at')
              ->orWhere('last_hit_at', '<', now()->subDays($days));
        });
    }
}
