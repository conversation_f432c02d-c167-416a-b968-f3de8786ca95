<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AppTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'type',
        'category',
        'description',
        'version',
        'min_php_version',
        'requirements',
        'source_type',
        'source_url',
        'source_branch',
        'install_commands',
        'default_config',
        'icon_url',
        'documentation_url',
        'demo_url',
        'is_active',
        'is_featured',
        'install_count',
        'rating'
    ];

    protected $casts = [
        'requirements' => 'array',
        'install_commands' => 'array',
        'default_config' => 'array',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'rating' => 'float'
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopePopular($query)
    {
        return $query->orderBy('install_count', 'desc');
    }

    public function scopeHighRated($query)
    {
        return $query->orderBy('rating', 'desc');
    }

    // Accessors
    public function getFormattedRatingAttribute()
    {
        return number_format($this->rating, 1);
    }

    public function getInstallCountFormattedAttribute()
    {
        if ($this->install_count >= 1000000) {
            return number_format($this->install_count / 1000000, 1) . 'M';
        } elseif ($this->install_count >= 1000) {
            return number_format($this->install_count / 1000, 1) . 'K';
        }
        return $this->install_count;
    }

    public function getRatingStarsAttribute()
    {
        $fullStars = floor($this->rating);
        $halfStar = ($this->rating - $fullStars) >= 0.5 ? 1 : 0;
        $emptyStars = 5 - $fullStars - $halfStar;

        return [
            'full' => $fullStars,
            'half' => $halfStar,
            'empty' => $emptyStars
        ];
    }

    // Methods
    public function incrementInstallCount()
    {
        $this->increment('install_count');
    }

    public function updateRating($newRating)
    {
        // This would typically involve calculating average from reviews
        // For now, just update directly
        $this->update(['rating' => $newRating]);
    }

    public function canInstall()
    {
        return $this->is_active;
    }

    public function getRequiredPhpVersion()
    {
        return $this->min_php_version ?: '7.4';
    }

    public function hasRequirement($requirement)
    {
        return in_array($requirement, $this->requirements ?: []);
    }

    // Static methods for predefined templates
    public static function getPopularTemplates($limit = 6)
    {
        return static::active()
            ->popular()
            ->limit($limit)
            ->get();
    }

    public static function getFeaturedTemplates($limit = 4)
    {
        return static::active()
            ->featured()
            ->limit($limit)
            ->get();
    }

    public static function getTemplatesByType($type, $limit = null)
    {
        $query = static::active()->byType($type)->popular();
        
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->get();
    }

    public static function searchTemplates($search, $type = null, $category = null)
    {
        $query = static::active();

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($type) {
            $query->byType($type);
        }

        if ($category) {
            $query->byCategory($category);
        }

        return $query->popular()->get();
    }

    // Predefined template data
    public static function getDefaultTemplates()
    {
        return [
            [
                'name' => 'WordPress',
                'slug' => 'wordpress',
                'type' => 'cms',
                'category' => 'PHP',
                'description' => 'The world\'s most popular content management system. Perfect for blogs, business websites, and online stores.',
                'version' => '6.4',
                'min_php_version' => '7.4',
                'requirements' => ['mysql', 'apache_mod_rewrite'],
                'source_type' => 'zip',
                'source_url' => 'https://wordpress.org/latest.tar.gz',
                'install_commands' => [
                    'download_extract',
                    'create_database',
                    'configure_wp_config',
                    'set_permissions',
                    'run_wp_cli_install'
                ],
                'default_config' => [
                    'admin_user' => 'admin',
                    'admin_email' => '',
                    'site_title' => '',
                    'plugins' => ['akismet', 'hello-dolly'],
                    'theme' => 'twentytwentyfour'
                ],
                'icon_url' => '/images/apps/wordpress.png',
                'documentation_url' => 'https://wordpress.org/support/',
                'demo_url' => 'https://wordpress.org/showcase/',
                'is_featured' => true,
                'rating' => 4.8
            ],
            [
                'name' => 'Laravel',
                'slug' => 'laravel',
                'type' => 'framework',
                'category' => 'PHP',
                'description' => 'The PHP framework for web artisans. Build modern, elegant web applications with expressive syntax.',
                'version' => '10.x',
                'min_php_version' => '8.1',
                'requirements' => ['composer', 'mysql', 'php_extensions'],
                'source_type' => 'composer',
                'source_url' => 'laravel/laravel',
                'install_commands' => [
                    'composer_create_project',
                    'create_database',
                    'configure_env',
                    'generate_key',
                    'run_migrations',
                    'set_permissions'
                ],
                'default_config' => [
                    'app_env' => 'production',
                    'app_debug' => false,
                    'cache_driver' => 'file',
                    'session_driver' => 'file',
                    'queue_connection' => 'sync'
                ],
                'icon_url' => '/images/apps/laravel.png',
                'documentation_url' => 'https://laravel.com/docs',
                'demo_url' => 'https://laravel.com',
                'is_featured' => true,
                'rating' => 4.9
            ],
            [
                'name' => 'Drupal',
                'slug' => 'drupal',
                'type' => 'cms',
                'category' => 'PHP',
                'description' => 'A flexible CMS for ambitious digital experiences. Build everything from personal blogs to enterprise applications.',
                'version' => '10.x',
                'min_php_version' => '8.1',
                'requirements' => ['mysql', 'composer'],
                'source_type' => 'composer',
                'source_url' => 'drupal/recommended-project',
                'install_commands' => [
                    'composer_create_project',
                    'create_database',
                    'set_permissions',
                    'web_installer'
                ],
                'default_config' => [
                    'profile' => 'standard',
                    'locale' => 'en'
                ],
                'icon_url' => '/images/apps/drupal.png',
                'documentation_url' => 'https://www.drupal.org/docs',
                'demo_url' => 'https://www.drupal.org/try-drupal',
                'is_featured' => true,
                'rating' => 4.5
            ],
            [
                'name' => 'Magento',
                'slug' => 'magento',
                'type' => 'ecommerce',
                'category' => 'PHP',
                'description' => 'Powerful ecommerce platform for growing businesses. Create engaging shopping experiences.',
                'version' => '2.4',
                'min_php_version' => '8.1',
                'requirements' => ['mysql', 'elasticsearch', 'composer', 'high_memory'],
                'source_type' => 'composer',
                'source_url' => 'magento/project-community-edition',
                'install_commands' => [
                    'composer_create_project',
                    'create_database',
                    'run_setup_install',
                    'set_permissions',
                    'configure_cron'
                ],
                'default_config' => [
                    'backend_frontname' => 'admin',
                    'currency' => 'USD',
                    'timezone' => 'America/Chicago',
                    'language' => 'en_US'
                ],
                'icon_url' => '/images/apps/magento.png',
                'documentation_url' => 'https://devdocs.magento.com/',
                'demo_url' => 'https://magento.com/products/magento-commerce',
                'is_featured' => false,
                'rating' => 4.2
            ],
            [
                'name' => 'PrestaShop',
                'slug' => 'prestashop',
                'type' => 'ecommerce',
                'category' => 'PHP',
                'description' => 'Free, open-source ecommerce solution. Perfect for small to medium online stores.',
                'version' => '8.1',
                'min_php_version' => '7.4',
                'requirements' => ['mysql', 'gd_extension', 'curl_extension'],
                'source_type' => 'zip',
                'source_url' => 'https://github.com/PrestaShop/PrestaShop/releases/latest/download/prestashop_8.1.0.zip',
                'install_commands' => [
                    'download_extract',
                    'create_database',
                    'set_permissions',
                    'web_installer'
                ],
                'default_config' => [
                    'shop_name' => '',
                    'admin_folder' => 'admin',
                    'country' => 'US',
                    'timezone' => 'America/New_York'
                ],
                'icon_url' => '/images/apps/prestashop.png',
                'documentation_url' => 'https://devdocs.prestashop.com/',
                'demo_url' => 'https://www.prestashop.com/en/prestashop-demo',
                'is_featured' => false,
                'rating' => 4.3
            ]
        ];
    }
}
