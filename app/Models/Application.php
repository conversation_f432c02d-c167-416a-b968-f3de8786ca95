<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Application extends Model
{
    use HasFactory;

    protected $table = 'apps';

    protected $fillable = [
        'user_id',
        'domain_id',
        'name',
        'type',
        'version',
        'status',
        'install_path',
        'url',
        'admin_url',
        'admin_username',
        'admin_email',
        'database_config',
        'environment_config',
        'custom_config',
        'installed_at',
        'last_updated_at',
        'auto_update',
        'notes'
    ];

    protected $casts = [
        'database_config' => 'array',
        'environment_config' => 'array',
        'custom_config' => 'array',
        'installed_at' => 'datetime',
        'last_updated_at' => 'datetime',
        'auto_update' => 'boolean'
    ];

    protected $hidden = [
        'database_config',
        'environment_config'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }

    // Accessors & Mutators
    protected function statusColor(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->status) {
                'active' => 'green',
                'inactive' => 'gray',
                'updating' => 'yellow',
                'failed' => 'red',
                default => 'gray'
            }
        );
    }

    protected function statusIcon(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->status) {
                'active' => 'check-circle',
                'inactive' => 'x-circle',
                'updating' => 'clock',
                'failed' => 'exclamation-triangle',
                default => 'question-mark-circle'
            }
        );
    }

    protected function typeIcon(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->type) {
                'cms' => 'document-text',
                'framework' => 'code',
                'ecommerce' => 'shopping-cart',
                'blog' => 'pencil',
                'portfolio' => 'photograph',
                'forum' => 'chat',
                'wiki' => 'book-open',
                'crm' => 'users',
                'analytics' => 'chart-bar',
                'monitoring' => 'desktop-computer',
                default => 'cube'
            }
        );
    }

    protected function formattedInstallDate(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->installed_at?->format('M d, Y')
        );
    }

    protected function formattedLastUpdate(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->last_updated_at?->format('M d, Y H:i') ?? 'Never'
        );
    }

    protected function daysSinceInstall(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->installed_at?->diffInDays(now()) ?? 0
        );
    }

    protected function isOutdated(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->last_updated_at?->diffInDays(now()) > 30
        );
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByDomain($query, $domainId)
    {
        return $query->where('domain_id', $domainId);
    }

    public function scopeNeedsUpdate($query)
    {
        return $query->where('last_updated_at', '<', now()->subDays(30))
                    ->orWhereNull('last_updated_at');
    }

    // Static Methods
    public static function getAvailableTypes()
    {
        return [
            'cms' => 'Content Management System',
            'framework' => 'Web Framework',
            'ecommerce' => 'E-commerce Platform',
            'blog' => 'Blog Platform',
            'portfolio' => 'Portfolio Site',
            'forum' => 'Forum Software',
            'wiki' => 'Wiki Platform',
            'crm' => 'Customer Relationship Management',
            'analytics' => 'Analytics Platform',
            'monitoring' => 'Monitoring Tool'
        ];
    }

    public static function getAvailableStatuses()
    {
        return [
            'active' => 'Active',
            'inactive' => 'Inactive',
            'updating' => 'Updating',
            'failed' => 'Failed'
        ];
    }

    public static function getPopularApps()
    {
        return [
            'WordPress' => ['type' => 'cms', 'icon' => 'wordpress'],
            'Laravel' => ['type' => 'framework', 'icon' => 'laravel'],
            'WooCommerce' => ['type' => 'ecommerce', 'icon' => 'shopping-cart'],
            'Drupal' => ['type' => 'cms', 'icon' => 'drupal'],
            'Joomla' => ['type' => 'cms', 'icon' => 'joomla'],
            'Magento' => ['type' => 'ecommerce', 'icon' => 'magento'],
            'Ghost' => ['type' => 'blog', 'icon' => 'ghost'],
            'Discourse' => ['type' => 'forum', 'icon' => 'chat'],
            'MediaWiki' => ['type' => 'wiki', 'icon' => 'book-open'],
            'Nextcloud' => ['type' => 'storage', 'icon' => 'cloud']
        ];
    }

    // Helper Methods
    public function canUpdate(): bool
    {
        return in_array($this->status, ['active', 'failed']);
    }

    public function canDelete(): bool
    {
        return in_array($this->status, ['inactive', 'failed']);
    }

    public function getHealthScore(): int
    {
        $score = 100;
        
        // Deduct points for various issues
        if ($this->status === 'failed') $score -= 50;
        if ($this->status === 'inactive') $score -= 30;
        if ($this->is_outdated) $score -= 20;
        if (!$this->auto_update) $score -= 10;
        
        return max(0, $score);
    }

    public function getHealthColor(): string
    {
        $score = $this->getHealthScore();
        
        if ($score >= 80) return 'green';
        if ($score >= 60) return 'yellow';
        if ($score >= 40) return 'orange';
        return 'red';
    }
}
