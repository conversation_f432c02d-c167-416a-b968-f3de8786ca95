<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PaymentMethod extends Model
{
    protected $fillable = [
        'user_id',
        'type',
        'gateway',
        'gateway_payment_method_id',
        'gateway_customer_id',
        'last_four',
        'brand',
        'exp_month',
        'exp_year',
        'cardholder_name',
        'paypal_email',
        'paypal_payer_id',
        'bank_name',
        'account_last_four',
        'routing_number',
        'account_type',
        'is_default',
        'is_active',
        'metadata',
        'verified_at',
        'expires_at',
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'is_active' => 'boolean',
        'metadata' => 'array',
        'verified_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    protected $hidden = [
        'gateway_payment_method_id',
        'gateway_customer_id',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Get display name for payment method
     */
    public function getDisplayNameAttribute(): string
    {
        switch ($this->type) {
            case 'credit_card':
                return ucfirst($this->brand) . ' ending in ' . $this->last_four;
            case 'paypal':
                return 'PayPal (' . $this->paypal_email . ')';
            case 'bank_account':
                return $this->bank_name . ' ending in ' . $this->account_last_four;
            default:
                return ucfirst(str_replace('_', ' ', $this->type));
        }
    }

    /**
     * Get icon for payment method
     */
    public function getIconAttribute(): string
    {
        switch ($this->type) {
            case 'credit_card':
                return match($this->brand) {
                    'visa' => 'fab fa-cc-visa',
                    'mastercard' => 'fab fa-cc-mastercard',
                    'amex' => 'fab fa-cc-amex',
                    'discover' => 'fab fa-cc-discover',
                    default => 'fas fa-credit-card',
                };
            case 'paypal':
                return 'fab fa-paypal';
            case 'bank_account':
                return 'fas fa-university';
            default:
                return 'fas fa-credit-card';
        }
    }

    /**
     * Check if payment method is expired
     */
    public function isExpired(): bool
    {
        if (!$this->expires_at) {
            return false;
        }

        return $this->expires_at->isPast();
    }

    /**
     * Check if payment method is expiring soon (within 30 days)
     */
    public function isExpiringSoon(): bool
    {
        if (!$this->expires_at) {
            return false;
        }

        return $this->expires_at->isBefore(now()->addDays(30));
    }

    /**
     * Set as default payment method
     */
    public function setAsDefault(): bool
    {
        // Remove default from other payment methods
        $this->user->paymentMethods()->update(['is_default' => false]);
        
        // Set this as default
        return $this->update(['is_default' => true]);
    }

    /**
     * Scope for active payment methods
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for default payment method
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope for specific type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for specific gateway
     */
    public function scopeForGateway($query, string $gateway)
    {
        return $query->where('gateway', $gateway);
    }


}
