<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmailAlias extends Model
{
    protected $fillable = [
        'email_account_id',
        'domain_id',
        'alias',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function emailAccount(): BelongsTo
    {
        return $this->belongsTo(EmailAccount::class);
    }

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }

    public function getFullAliasAttribute(): string
    {
        return $this->alias . '@' . $this->domain->name;
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
