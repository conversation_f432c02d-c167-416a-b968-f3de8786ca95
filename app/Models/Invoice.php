<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Invoice extends Model
{
    protected $fillable = [
        'user_id',
        'subscription_id',
        'invoice_number',
        'status',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'currency',
        'issue_date',
        'due_date',
        'paid_at',
        'payment_method',
        'payment_gateway',
        'payment_transaction_id',
        'line_items',
        'billing_address',
        'notes',
        'pdf_path',
        'auto_generated',
        'reminder_count',
        'last_reminder_sent_at',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'issue_date' => 'date',
        'due_date' => 'date',
        'paid_at' => 'date',
        'line_items' => 'array',
        'billing_address' => 'array',
        'auto_generated' => 'boolean',
        'last_reminder_sent_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($invoice) {
            if (empty($invoice->invoice_number)) {
                $invoice->invoice_number = static::generateInvoiceNumber();
            }
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Generate unique invoice number
     */
    public static function generateInvoiceNumber(): string
    {
        $prefix = 'INV-' . now()->format('Y');
        $lastInvoice = static::where('invoice_number', 'like', $prefix . '%')
                           ->orderBy('invoice_number', 'desc')
                           ->first();

        if ($lastInvoice) {
            $lastNumber = (int) substr($lastInvoice->invoice_number, -6);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return $prefix . '-' . str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Check if invoice is paid
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * Check if invoice is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if invoice is overdue
     */
    public function isOverdue(): bool
    {
        return $this->status === 'overdue' || 
               ($this->status === 'pending' && $this->due_date->isPast());
    }

    /**
     * Check if invoice is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if invoice is refunded
     */
    public function isRefunded(): bool
    {
        return $this->status === 'refunded';
    }

    /**
     * Get days until due
     */
    public function getDaysUntilDueAttribute(): int
    {
        return now()->diffInDays($this->due_date, false);
    }

    /**
     * Get formatted amounts
     */
    public function getFormattedSubtotalAttribute(): string
    {
        return '$' . number_format($this->subtotal, 2);
    }

    public function getFormattedTaxAmountAttribute(): string
    {
        return '$' . number_format($this->tax_amount, 2);
    }

    public function getFormattedDiscountAmountAttribute(): string
    {
        return '$' . number_format($this->discount_amount, 2);
    }

    public function getFormattedTotalAmountAttribute(): string
    {
        return '$' . number_format($this->total_amount, 2);
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'paid' => 'bg-green-100 text-green-800',
            'pending' => 'bg-yellow-100 text-yellow-800',
            'overdue' => 'bg-red-100 text-red-800',
            'cancelled' => 'bg-gray-100 text-gray-800',
            'refunded' => 'bg-purple-100 text-purple-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Mark invoice as paid
     */
    public function markAsPaid(string $paymentMethod = null, string $transactionId = null): bool
    {
        return $this->update([
            'status' => 'paid',
            'paid_at' => now(),
            'payment_method' => $paymentMethod,
            'payment_transaction_id' => $transactionId,
        ]);
    }

    /**
     * Mark invoice as overdue
     */
    public function markAsOverdue(): bool
    {
        return $this->update(['status' => 'overdue']);
    }

    /**
     * Cancel invoice
     */
    public function cancel(): bool
    {
        return $this->update(['status' => 'cancelled']);
    }

    /**
     * Add line item to invoice
     */
    public function addLineItem(array $item): bool
    {
        $lineItems = $this->line_items ?? [];
        $lineItems[] = array_merge($item, [
            'id' => Str::uuid(),
            'created_at' => now()->toISOString(),
        ]);

        return $this->update(['line_items' => $lineItems]);
    }

    /**
     * Calculate totals from line items
     */
    public function calculateTotals(): bool
    {
        $lineItems = $this->line_items ?? [];
        $subtotal = 0;

        foreach ($lineItems as $item) {
            $subtotal += ($item['quantity'] ?? 1) * ($item['unit_price'] ?? 0);
        }

        $taxAmount = $this->tax_amount ?? 0;
        $discountAmount = $this->discount_amount ?? 0;
        $totalAmount = $subtotal + $taxAmount - $discountAmount;

        return $this->update([
            'subtotal' => $subtotal,
            'total_amount' => $totalAmount,
        ]);
    }

    /**
     * Send reminder
     */
    public function sendReminder(): bool
    {
        // Implementation would send email reminder
        return $this->update([
            'reminder_count' => $this->reminder_count + 1,
            'last_reminder_sent_at' => now(),
        ]);
    }

    /**
     * Generate PDF
     */
    public function generatePdf(): string
    {
        // Implementation would generate PDF and return path
        $filename = 'invoice_' . $this->invoice_number . '.pdf';
        $path = 'invoices/' . $filename;
        
        // Store PDF path
        $this->update(['pdf_path' => $path]);
        
        return $path;
    }

    /**
     * Scope for paid invoices
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope for pending invoices
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for overdue invoices
     */
    public function scopeOverdue($query)
    {
        return $query->where(function ($q) {
            $q->where('status', 'overdue')
              ->orWhere(function ($q2) {
                  $q2->where('status', 'pending')
                     ->where('due_date', '<', now());
              });
        });
    }

    /**
     * Scope for due soon (within days)
     */
    public function scopeDueSoon($query, int $days = 7)
    {
        return $query->where('status', 'pending')
                    ->where('due_date', '<=', now()->addDays($days))
                    ->where('due_date', '>', now());
    }
}
