<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Domain extends Model
{
    protected $fillable = [
        'user_id',
        'name',
        'status',
        'registrar',
        'registered_at',
        'expires_at',
        'auto_renew',
        'nameserver1',
        'nameserver2',
        'nameserver3',
        'nameserver4',
        'dns_managed',
        'document_root',
        'php_version',
        'ssl_enabled',
        'ssl_provider',
        'dns_settings',
        'notes',
    ];

    protected $casts = [
        'registered_at' => 'date',
        'expires_at' => 'date',
        'auto_renew' => 'boolean',
        'dns_managed' => 'boolean',
        'ssl_enabled' => 'boolean',
        'dns_settings' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function subdomains(): HasMany
    {
        return $this->hasMany(Subdomain::class);
    }

    public function dnsRecords(): HasMany
    {
        return $this->hasMany(DnsRecord::class);
    }

    public function backups(): MorphMany
    {
        return $this->morphMany(Backup::class, 'backupable');
    }

    public function sslCertificates(): HasMany
    {
        return $this->hasMany(SslCertificate::class);
    }

    public function webHosts(): HasMany
    {
        return $this->hasMany(WebHost::class);
    }

    public function webRedirects(): HasMany
    {
        return $this->hasMany(WebRedirect::class);
    }

    public function emailAccounts(): HasMany
    {
        return $this->hasMany(EmailAccount::class);
    }

    public function apps(): HasMany
    {
        return $this->hasMany(App::class);
    }

    public function isExpiringSoon(int $days = 30): bool
    {
        return $this->expires_at && $this->expires_at->diffInDays(now()) <= $days;
    }

    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }
}
