<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Plan extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'yearly_price',
        'currency',
        'billing_cycle',
        'trial_days',
        'max_domains',
        'max_subdomains',
        'max_email_accounts',
        'disk_space_gb',
        'bandwidth_gb',
        'max_databases',
        'max_cron_jobs',
        'max_backups',
        'backup_retention_days',
        'ssl_certificates',
        'email_hosting',
        'dns_management',
        'file_manager',
        'app_installer',
        'priority_support',
        'white_label',
        'features',
        'is_active',
        'is_featured',
        'sort_order',
    ];

    protected $casts = [
        'features' => 'array',
        'ssl_certificates' => 'boolean',
        'email_hosting' => 'boolean',
        'dns_management' => 'boolean',
        'file_manager' => 'boolean',
        'app_installer' => 'boolean',
        'priority_support' => 'boolean',
        'white_label' => 'boolean',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'price' => 'decimal:2',
        'yearly_price' => 'decimal:2',
    ];

    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    public function isUnlimited(string $feature): bool
    {
        return $this->getAttribute($feature) === -1;
    }

    public function getFormattedPriceAttribute(): string
    {
        return '$' . number_format($this->price, 2);
    }

    public function getFormattedYearlyPriceAttribute(): string
    {
        return '$' . number_format($this->yearly_price, 2);
    }
}
