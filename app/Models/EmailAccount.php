<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Hash;

class EmailAccount extends Model
{
    protected $fillable = [
        'domain_id',
        'user_id',
        'email',
        'username',
        'password_hash',
        'quota_bytes',
        'used_bytes',
        'is_active',
        'can_send',
        'can_receive',
        'max_send_per_hour',
        'sent_today',
        'sent_reset_date',
        'last_login_at',
        'last_login_ip',
        'forwarding_addresses',
        'vacation_enabled',
        'vacation_message',
        'vacation_start_date',
        'vacation_end_date',
    ];

    protected $casts = [
        'quota_bytes' => 'integer',
        'used_bytes' => 'integer',
        'is_active' => 'boolean',
        'can_send' => 'boolean',
        'can_receive' => 'boolean',
        'max_send_per_hour' => 'integer',
        'sent_today' => 'integer',
        'sent_reset_date' => 'date',
        'last_login_at' => 'datetime',
        'forwarding_addresses' => 'array',
        'vacation_enabled' => 'boolean',
        'vacation_start_date' => 'date',
        'vacation_end_date' => 'date',
    ];

    protected $hidden = [
        'password_hash',
    ];

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function forwarders(): HasMany
    {
        return $this->hasMany(EmailForwarder::class);
    }

    public function aliases(): HasMany
    {
        return $this->hasMany(EmailAlias::class);
    }

    public function setPasswordAttribute($value): void
    {
        if ($value) {
            $this->attributes['password_hash'] = Hash::make($value);
        }
    }

    public function getUsagePercentageAttribute(): float
    {
        if (!$this->quota_bytes || $this->quota_bytes <= 0) {
            return 0;
        }

        return min(100, ($this->used_bytes / $this->quota_bytes) * 100);
    }

    public function getFormattedUsedAttribute(): string
    {
        return $this->formatBytes($this->used_bytes);
    }

    public function getFormattedQuotaAttribute(): string
    {
        if ($this->quota_bytes <= 0) {
            return 'Unlimited';
        }

        return $this->formatBytes($this->quota_bytes);
    }

    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'inactive';
        }

        if ($this->isOverQuota()) {
            return 'over_quota';
        }

        if ($this->isNearQuota()) {
            return 'near_quota';
        }

        return 'active';
    }

    public function getStatusNameAttribute(): string
    {
        return match($this->status) {
            'active' => 'Active',
            'inactive' => 'Inactive',
            'over_quota' => 'Over Quota',
            'near_quota' => 'Near Quota',
            default => 'Unknown'
        };
    }

    public function isOverQuota(): bool
    {
        return $this->quota_bytes > 0 && $this->used_bytes > $this->quota_bytes;
    }

    public function isNearQuota(int $threshold = 90): bool
    {
        return $this->quota_bytes > 0 && $this->usage_percentage >= $threshold;
    }

    public function hasVacationMessage(): bool
    {
        if (!$this->vacation_enabled) {
            return false;
        }

        $now = now()->toDateString();

        if ($this->vacation_start_date && $now < $this->vacation_start_date) {
            return false;
        }

        if ($this->vacation_end_date && $now > $this->vacation_end_date) {
            return false;
        }

        return true;
    }

    public function isForwarding(): bool
    {
        return !empty($this->forwarding_addresses);
    }

    public function getForwardingEmailsAttribute(): array
    {
        return $this->forwarding_addresses ?? [];
    }

    public function updateUsage(): void
    {
        try {
            // Here you would implement actual mailbox size calculation
            // This could involve checking mailbox files or using IMAP
            
            $usage = $this->calculateMailboxUsage();
            $this->update(['used_mb' => $usage]);
        } catch (\Exception $e) {
            // Log error
        }
    }

    public function createAlias(string $alias): ?EmailAlias
    {
        try {
            return $this->aliases()->create([
                'alias' => $alias,
                'domain_id' => $this->domain_id,
                'is_active' => true,
            ]);
        } catch (\Exception $e) {
            return null;
        }
    }

    public function addForwarder(string $email): ?EmailForwarder
    {
        try {
            return $this->forwarders()->create([
                'forward_to' => $email,
                'domain_id' => $this->domain_id,
                'is_active' => true,
            ]);
        } catch (\Exception $e) {
            return null;
        }
    }

    public function checkPassword(string $password): bool
    {
        return Hash::check($password, $this->password_hash);
    }

    public function recordLogin(string $ip = null): void
    {
        $this->update([
            'last_login_at' => now(),
            'last_login_ip' => $ip ?? request()->ip(),
        ]);
    }

    public function getImapSettings(): array
    {
        return [
            'server' => 'mail.' . $this->domain->name,
            'port' => 993,
            'encryption' => 'ssl',
            'username' => $this->email,
        ];
    }

    public function getSmtpSettings(): array
    {
        return [
            'server' => 'mail.' . $this->domain->name,
            'port' => 587,
            'encryption' => 'tls',
            'username' => $this->email,
        ];
    }

    public function getPopSettings(): array
    {
        return [
            'server' => 'mail.' . $this->domain->name,
            'port' => 995,
            'encryption' => 'ssl',
            'username' => $this->email,
        ];
    }

    private function calculateMailboxUsage(): int
    {
        // Placeholder - implement actual mailbox usage calculation
        return $this->used_mb ?? 0;
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    public function scopeOverQuota($query)
    {
        return $query->whereRaw('used_mb > quota_mb AND quota_mb > 0');
    }

    public function scopeNearQuota($query, int $threshold = 90)
    {
        return $query->whereRaw('(used_mb / quota_mb * 100) >= ? AND quota_mb > 0', [$threshold]);
    }

    public function scopeCatchAll($query)
    {
        return $query->where('is_catch_all', true);
    }

    public function scopeWithAutoResponder($query)
    {
        return $query->where('auto_responder_enabled', true);
    }

    public function scopeWithForwarding($query)
    {
        return $query->whereNotNull('forward_to');
    }
}
