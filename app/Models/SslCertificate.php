<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Crypt;

class SslCertificate extends Model
{
    protected $fillable = [
        'domain_id',
        'subdomain_id',
        'name',
        'type',
        'status',
        'domains',
        'certificate',
        'private_key',
        'certificate_chain',
        'issuer',
        'issued_at',
        'expires_at',
        'auto_renew',
        'days_before_renewal',
        'last_renewal_attempt',
        'renewal_error',
        'acme_challenge_type',
        'acme_challenge_data',
    ];

    protected $casts = [
        'domains' => 'array',
        'issued_at' => 'date',
        'expires_at' => 'date',
        'auto_renew' => 'boolean',
        'last_renewal_attempt' => 'datetime',
        'acme_challenge_data' => 'array',
        'days_before_renewal' => 'integer',
    ];

    protected $hidden = [
        'private_key',
        'certificate',
        'certificate_chain',
    ];

    const TYPES = [
        'letsencrypt' => 'Let\'s Encrypt',
        'custom' => 'Custom Certificate',
        'self-signed' => 'Self-Signed',
        'cloudflare' => 'Cloudflare',
    ];

    const STATUSES = [
        'pending' => 'Pending',
        'active' => 'Active',
        'expired' => 'Expired',
        'failed' => 'Failed',
        'revoked' => 'Revoked',
    ];

    const CHALLENGE_TYPES = [
        'http-01' => 'HTTP Challenge',
        'dns-01' => 'DNS Challenge',
        'tls-alpn-01' => 'TLS-ALPN Challenge',
    ];

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }

    public function subdomain(): BelongsTo
    {
        return $this->belongsTo(Subdomain::class);
    }

    public function renewals(): HasMany
    {
        return $this->hasMany(SslCertificateRenewal::class);
    }

    public function getDecryptedPrivateKeyAttribute(): ?string
    {
        if (!$this->private_key) {
            return null;
        }

        try {
            return Crypt::decryptString($this->private_key);
        } catch (\Exception $e) {
            return null;
        }
    }

    public function setPrivateKeyAttribute($value): void
    {
        if ($value) {
            $this->attributes['private_key'] = Crypt::encryptString($value);
        }
    }

    public function getTypeNameAttribute(): string
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    public function getStatusNameAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    public function getChallengeTypeNameAttribute(): string
    {
        return self::CHALLENGE_TYPES[$this->acme_challenge_type] ?? $this->acme_challenge_type;
    }

    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function isExpiringSoon(int $days = null): bool
    {
        $days = $days ?? $this->days_before_renewal ?? 30;
        return $this->expires_at && $this->expires_at->diffInDays(now()) <= $days;
    }

    public function needsRenewal(): bool
    {
        return $this->auto_renew && 
               $this->status === 'active' && 
               $this->isExpiringSoon();
    }

    public function getDaysUntilExpiryAttribute(): ?int
    {
        if (!$this->expires_at) {
            return null;
        }

        return max(0, $this->expires_at->diffInDays(now()));
    }

    public function getValidityPeriodAttribute(): ?int
    {
        if (!$this->issued_at || !$this->expires_at) {
            return null;
        }

        return $this->issued_at->diffInDays($this->expires_at);
    }

    public function getCertificateInfoAttribute(): ?array
    {
        if (!$this->certificate) {
            return null;
        }

        try {
            $cert = openssl_x509_parse($this->certificate);
            return [
                'subject' => $cert['subject'] ?? null,
                'issuer' => $cert['issuer'] ?? null,
                'valid_from' => isset($cert['validFrom_time_t']) ? date('Y-m-d H:i:s', $cert['validFrom_time_t']) : null,
                'valid_to' => isset($cert['validTo_time_t']) ? date('Y-m-d H:i:s', $cert['validTo_time_t']) : null,
                'serial_number' => $cert['serialNumber'] ?? null,
                'signature_algorithm' => $cert['signatureTypeSN'] ?? null,
            ];
        } catch (\Exception $e) {
            return null;
        }
    }

    public function verifyCertificate(): bool
    {
        if (!$this->certificate || !$this->private_key) {
            return false;
        }

        try {
            $cert = openssl_x509_read($this->certificate);
            $key = openssl_pkey_get_private($this->decrypted_private_key);
            
            return openssl_x509_check_private_key($cert, $key);
        } catch (\Exception $e) {
            return false;
        }
    }

    public function install(): bool
    {
        if ($this->status !== 'active' || !$this->verifyCertificate()) {
            return false;
        }

        try {
            // Here you would implement the actual SSL installation
            // This could involve updating web server configuration
            // For now, we'll just mark it as installed
            
            $this->update(['status' => 'active']);
            return true;
        } catch (\Exception $e) {
            $this->update([
                'status' => 'failed',
                'renewal_error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function renew(): bool
    {
        if (!$this->auto_renew) {
            return false;
        }

        try {
            $renewal = $this->renewals()->create([
                'status' => 'pending',
                'started_at' => now(),
                'renewal_method' => 'acme',
                'old_expiry_date' => $this->expires_at,
            ]);

            // Here you would implement the actual renewal logic
            // For Let's Encrypt, this would involve ACME protocol
            
            $renewal->update([
                'status' => 'completed',
                'completed_at' => now(),
            ]);

            $this->update([
                'status' => 'active',
                'expires_at' => now()->addDays(90), // Let's Encrypt certificates are valid for 90 days
                'last_renewal_attempt' => now(),
                'renewal_error' => null,
            ]);

            return true;
        } catch (\Exception $e) {
            $this->update([
                'last_renewal_attempt' => now(),
                'renewal_error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    public function scopeExpiringSoon($query, int $days = 30)
    {
        return $query->where('expires_at', '<=', now()->addDays($days))
                    ->where('expires_at', '>', now());
    }

    public function scopeAutoRenew($query)
    {
        return $query->where('auto_renew', true);
    }

    public function scopeNeedsRenewal($query)
    {
        return $query->autoRenew()
                    ->active()
                    ->expiringSoon();
    }
}
