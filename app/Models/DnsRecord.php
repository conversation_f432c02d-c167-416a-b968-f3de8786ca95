<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DnsRecord extends Model
{
    protected $fillable = [
        'domain_id',
        'subdomain_id',
        'name',
        'type',
        'value',
        'ttl',
        'priority',
        'weight',
        'port',
        'is_active',
        'is_system',
        'comment',
        'last_checked_at',
        'propagated',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_system' => 'boolean',
        'propagated' => 'boolean',
        'last_checked_at' => 'datetime',
        'ttl' => 'integer',
        'priority' => 'integer',
        'weight' => 'integer',
        'port' => 'integer',
    ];

    const RECORD_TYPES = [
        'A' => 'A Record (IPv4)',
        'AAAA' => 'AAAA Record (IPv6)',
        'CNAME' => 'CNAME Record (Alias)',
        'MX' => 'MX Record (Mail Exchange)',
        'TXT' => 'TXT Record (Text)',
        'NS' => 'NS Record (Name Server)',
        'SRV' => 'SRV Record (Service)',
        'PTR' => 'PTR Record (Reverse DNS)',
        'CAA' => 'CAA Record (Certificate Authority)',
    ];

    const DEFAULT_TTLS = [
        300 => '5 minutes',
        600 => '10 minutes',
        1800 => '30 minutes',
        3600 => '1 hour',
        7200 => '2 hours',
        14400 => '4 hours',
        28800 => '8 hours',
        43200 => '12 hours',
        86400 => '24 hours',
    ];

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }

    public function subdomain(): BelongsTo
    {
        return $this->belongsTo(Subdomain::class);
    }

    public function getFullNameAttribute(): string
    {
        if ($this->subdomain_id) {
            return $this->subdomain->full_name;
        }

        if ($this->name === '@') {
            return $this->domain->name;
        }

        return $this->name . '.' . $this->domain->name;
    }

    public function getTypeNameAttribute(): string
    {
        return self::RECORD_TYPES[$this->type] ?? $this->type;
    }

    public function getTtlNameAttribute(): string
    {
        return self::DEFAULT_TTLS[$this->ttl] ?? $this->ttl . ' seconds';
    }

    public function isValid(): bool
    {
        return match($this->type) {
            'A' => filter_var($this->value, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) !== false,
            'AAAA' => filter_var($this->value, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6) !== false,
            'CNAME' => !empty($this->value) && $this->value !== $this->full_name,
            'MX' => !empty($this->value) && is_numeric($this->priority),
            'TXT' => !empty($this->value),
            'NS' => !empty($this->value),
            'SRV' => !empty($this->value) && is_numeric($this->priority) && is_numeric($this->weight) && is_numeric($this->port),
            default => !empty($this->value)
        };
    }

    public function checkPropagation(): bool
    {
        try {
            $result = dns_get_record($this->full_name, $this->getDnsType());
            
            foreach ($result as $record) {
                if ($this->matchesRecord($record)) {
                    $this->update([
                        'propagated' => true,
                        'last_checked_at' => now()
                    ]);
                    return true;
                }
            }
        } catch (\Exception $e) {
            // DNS lookup failed
        }

        $this->update([
            'propagated' => false,
            'last_checked_at' => now()
        ]);

        return false;
    }

    private function getDnsType(): int
    {
        return match($this->type) {
            'A' => DNS_A,
            'AAAA' => DNS_AAAA,
            'CNAME' => DNS_CNAME,
            'MX' => DNS_MX,
            'TXT' => DNS_TXT,
            'NS' => DNS_NS,
            'SRV' => DNS_SRV,
            'PTR' => DNS_PTR,
            default => DNS_ANY
        };
    }

    private function matchesRecord(array $record): bool
    {
        return match($this->type) {
            'A' => $record['ip'] === $this->value,
            'AAAA' => $record['ipv6'] === $this->value,
            'CNAME' => $record['target'] === $this->value,
            'MX' => $record['target'] === $this->value && $record['pri'] == $this->priority,
            'TXT' => $record['txt'] === $this->value,
            'NS' => $record['target'] === $this->value,
            'SRV' => $record['target'] === $this->value && 
                     $record['pri'] == $this->priority && 
                     $record['weight'] == $this->weight && 
                     $record['port'] == $this->port,
            default => false
        };
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }

    public function scopeCustom($query)
    {
        return $query->where('is_system', false);
    }
}
