<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DatabaseUser extends Model
{
    protected $fillable = [
        'user_id',
        'username',
        'password_hash',
        'host',
        'is_active',
        'last_login_at',
        'last_login_ip',
        'global_privileges',
        'description',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'last_login_at' => 'datetime',
        'global_privileges' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function databaseUserPermissions(): HasMany
    {
        return $this->hasMany(DatabaseUserPermission::class);
    }

    public function isActive(): bool
    {
        return $this->is_active;
    }

    public function hasPrivilege(string $privilege): bool
    {
        return in_array($privilege, $this->global_privileges ?? []);
    }
}
