<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Server extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'hostname',
        'ip_address',
        'port',
        'username',
        'ssh_key',
        'password',
        'status',
        'type',
        'operating_system',
        'control_panel',
        'specifications',
        'monitoring_data',
        'last_ping',
        'notes',
        'is_active'
    ];

    protected $casts = [
        'specifications' => 'array',
        'monitoring_data' => 'array',
        'last_ping' => 'datetime',
        'is_active' => 'boolean'
    ];

    protected $hidden = [
        'password',
        'ssh_key'
    ];

    // Accessors
    protected function statusBadge(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->status) {
                'online' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Online</span>',
                'offline' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Offline</span>',
                'maintenance' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Maintenance</span>',
                default => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Unknown</span>'
            }
        );
    }

    protected function typeBadge(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->type) {
                'web' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Web Server</span>',
                'database' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">Database</span>',
                'mail' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">Mail Server</span>',
                'dns' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800">DNS Server</span>',
                'load_balancer' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">Load Balancer</span>',
                'storage' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">Storage</span>',
                default => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Unknown</span>'
            }
        );
    }

    protected function cpuUsage(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->monitoring_data['cpu_usage'] ?? 0
        );
    }

    protected function ramUsage(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->monitoring_data['ram_usage'] ?? 0
        );
    }

    protected function diskUsage(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->monitoring_data['disk_usage'] ?? 0
        );
    }

    protected function uptime(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->monitoring_data['uptime'] ?? '0 days'
        );
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOnline($query)
    {
        return $query->where('status', 'online');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Methods
    public function ping()
    {
        try {
            $serverManagement = new \App\Services\ServerManagementService();
            $result = $serverManagement->testConnection($this);

            $this->update([
                'last_ping' => now(),
                'status' => $result['online'] ? 'online' : 'offline'
            ]);

            return $result['online'];
        } catch (\Exception $e) {
            $this->update([
                'last_ping' => now(),
                'status' => 'offline'
            ]);
            return false;
        }
    }

    public function updateMonitoringData()
    {
        try {
            $serverManagement = new \App\Services\ServerManagementService();
            $result = $serverManagement->connectAndUpdateServer($this);

            return $result['success'];
        } catch (\Exception $e) {
            // Fallback to simulated data if real connection fails
            $this->update([
                'monitoring_data' => [
                    'cpu_usage' => rand(10, 95),
                    'ram_usage' => rand(20, 85),
                    'disk_usage' => rand(15, 90),
                    'uptime' => rand(1, 365) . ' days',
                    'load_average' => round(rand(1, 500) / 100, 2),
                    'network_in' => rand(100, 1000) . ' MB/s',
                    'network_out' => rand(50, 500) . ' MB/s',
                    'last_updated' => now()->toISOString(),
                    'connection_error' => $e->getMessage()
                ]
            ]);
            return false;
        }
    }

    /**
     * Connect and get real server data
     */
    public function connectAndUpdate()
    {
        $serverManagement = new \App\Services\ServerManagementService();
        return $serverManagement->connectAndUpdateServer($this);
    }

    /**
     * Restart a service on this server
     */
    public function restartService($serviceName)
    {
        $serverManagement = new \App\Services\ServerManagementService();
        return $serverManagement->restartService($this, $serviceName);
    }

    /**
     * Execute command on this server
     */
    public function executeCommand($command)
    {
        $serverManagement = new \App\Services\ServerManagementService();
        return $serverManagement->executeCommand($this, $command);
    }

    /**
     * Create website on this server
     */
    public function createWebsite($domain, $username, $password = null)
    {
        $serverManagement = new \App\Services\ServerManagementService();
        return $serverManagement->createWebsite($this, $domain, $username, $password);
    }

    /**
     * Create database on this server
     */
    public function createDatabase($dbName, $dbUser, $dbPassword)
    {
        $serverManagement = new \App\Services\ServerManagementService();
        return $serverManagement->createDatabase($this, $dbName, $dbUser, $dbPassword);
    }

    /**
     * Install SSL certificate
     */
    public function installSSL($domain)
    {
        $serverManagement = new \App\Services\ServerManagementService();
        return $serverManagement->installSSL($this, $domain);
    }

    /**
     * Get server logs
     */
    public function getLogs($logType = 'system', $lines = 100)
    {
        $serverManagement = new \App\Services\ServerManagementService();
        return $serverManagement->getServerLogs($this, $logType, $lines);
    }
}
