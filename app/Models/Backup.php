<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\Storage;

class Backup extends Model
{
    protected $fillable = [
        'user_id',
        'name',
        'type',
        'status',
        'backupable_type',
        'backupable_id',
        'storage_path',
        'storage_driver',
        'size_bytes',
        'compression',
        'encrypted',
        'encryption_method',
        'started_at',
        'completed_at',
        'duration_seconds',
        'error_message',
        'metadata',
        'is_automatic',
        'expires_at',
    ];

    protected $casts = [
        'size_bytes' => 'integer',
        'encrypted' => 'boolean',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'duration_seconds' => 'integer',
        'metadata' => 'array',
        'is_automatic' => 'boolean',
        'expires_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function backupable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get backup size in human readable format
     */
    public function getSizeHumanAttribute(): string
    {
        $bytes = $this->size_bytes;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get backup duration in human readable format
     */
    public function getDurationHumanAttribute(): string
    {
        if (!$this->duration_seconds) {
            return 'N/A';
        }

        $seconds = $this->duration_seconds;
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;

        if ($hours > 0) {
            return sprintf('%dh %dm %ds', $hours, $minutes, $seconds);
        } elseif ($minutes > 0) {
            return sprintf('%dm %ds', $minutes, $seconds);
        } else {
            return sprintf('%ds', $seconds);
        }
    }

    /**
     * Check if backup is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if backup is running
     */
    public function isRunning(): bool
    {
        return $this->status === 'running';
    }

    /**
     * Check if backup failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if backup is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if backup is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if backup file exists
     */
    public function fileExists(): bool
    {
        if ($this->storage_driver === 'backups') {
            $fullPath = storage_path('app/backups/' . $this->storage_path);
            return file_exists($fullPath);
        }

        return Storage::disk($this->storage_driver)->exists($this->storage_path);
    }

    /**
     * Get backup file download URL
     */
    public function getDownloadUrlAttribute(): string
    {
        return route('admin.backups.download', $this);
    }

    /**
     * Get status color class
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'completed' => 'text-green-600 bg-green-100',
            'running' => 'text-blue-600 bg-blue-100',
            'failed' => 'text-red-600 bg-red-100',
            'pending' => 'text-yellow-600 bg-yellow-100',
            default => 'text-gray-600 bg-gray-100',
        };
    }

    /**
     * Get type icon
     */
    public function getTypeIconAttribute(): string
    {
        return match($this->type) {
            'database' => 'database',
            'files' => 'folder',
            'full' => 'server',
            default => 'archive',
        };
    }

    /**
     * Get type color class
     */
    public function getTypeColorAttribute(): string
    {
        return match($this->type) {
            'database' => 'text-blue-600',
            'files' => 'text-green-600',
            'full' => 'text-purple-600',
            default => 'text-gray-600',
        };
    }

    /**
     * Mark backup as started
     */
    public function markAsStarted(): void
    {
        $this->update([
            'status' => 'running',
            'started_at' => now(),
        ]);
    }

    /**
     * Mark backup as completed
     */
    public function markAsCompleted(int $sizeBytes = 0): void
    {
        $startedAt = $this->started_at ?: now();
        
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'duration_seconds' => now()->diffInSeconds($startedAt),
            'size_bytes' => $sizeBytes,
            'error_message' => null,
        ]);
    }

    /**
     * Mark backup as failed
     */
    public function markAsFailed(string $errorMessage): void
    {
        $startedAt = $this->started_at ?: now();
        
        $this->update([
            'status' => 'failed',
            'completed_at' => now(),
            'duration_seconds' => now()->diffInSeconds($startedAt),
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Delete backup file from storage
     */
    public function deleteFile(): bool
    {
        if ($this->fileExists()) {
            return Storage::disk($this->storage_driver)->delete($this->storage_path);
        }
        
        return true;
    }

    /**
     * Scope for completed backups
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for failed backups
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for running backups
     */
    public function scopeRunning($query)
    {
        return $query->where('status', 'running');
    }

    /**
     * Scope for automatic backups
     */
    public function scopeAutomatic($query)
    {
        return $query->where('is_automatic', true);
    }

    /**
     * Scope for backups of specific type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for manual backups
     */
    public function scopeManual($query)
    {
        return $query->where('is_automatic', false);
    }

    /**
     * Scope for expired backups
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }



    /**
     * Get backup progress percentage (for running backups)
     */
    public function getProgressPercentage(): int
    {
        if (!$this->isRunning() || !$this->started_at) {
            return 0;
        }

        // Estimate progress based on time elapsed
        // This is a simple estimation - in real implementation you'd track actual progress
        $elapsed = now()->diffInSeconds($this->started_at);
        $estimated = match($this->type) {
            'database' => 300, // 5 minutes
            'files' => 600,    // 10 minutes
            'full' => 1800,    // 30 minutes
            default => 300,
        };

        return min(95, round(($elapsed / $estimated) * 100));
    }

    /**
     * Generate backup filename
     */
    public static function generateFilename(string $type, string $name, string $compression = 'gzip'): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $extension = match($compression) {
            'gzip' => '.gz',
            'zip' => '.zip',
            default => '',
        };

        return "{$type}_{$name}_{$timestamp}{$extension}";
    }

    /**
     * Calculate retention date based on backup type
     */
    public static function calculateRetentionDate(string $type): \Carbon\Carbon
    {
        $days = match($type) {
            'database' => 30,  // 30 days for database backups
            'files' => 14,     // 14 days for file backups
            'full' => 7,       // 7 days for full backups
            default => 30,
        };

        return now()->addDays($days);
    }







    /**
     * Get progress percentage for running backups
     */
    public function getProgressPercentageAttribute(): int
    {
        if (!$this->isRunning() || !$this->started_at) {
            return 0;
        }

        // Simulate progress based on time elapsed
        $elapsed = now()->diffInSeconds($this->started_at);
        $estimatedTotal = match($this->type) {
            'database' => 300,  // 5 minutes
            'files' => 1800,    // 30 minutes
            'full' => 3600,     // 1 hour
            default => 600,
        };

        $progress = min(95, ($elapsed / $estimatedTotal) * 100);
        return (int) $progress;
    }
}
