<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Database extends Model
{
    protected $fillable = [
        'user_id',
        'domain_id',
        'name',
        'type',
        'host',
        'port',
        'charset',
        'collation',
        'size_bytes',
        'max_size_bytes',
        'table_count',
        'is_active',
        'last_backup_at',
        'connection_settings',
        'description',
    ];

    protected $casts = [
        'size_bytes' => 'integer',
        'max_size_bytes' => 'integer',
        'table_count' => 'integer',
        'is_active' => 'boolean',
        'last_backup_at' => 'datetime',
        'connection_settings' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }

    public function databaseUsers(): HasMany
    {
        return $this->hasMany(DatabaseUser::class);
    }

    public function backups(): MorphMany
    {
        return $this->morphMany(Backup::class, 'backupable');
    }

    public function getSizeInMbAttribute(): float
    {
        if (!$this->size_bytes) {
            return 0.0;
        }
        return round($this->size_bytes / 1024 / 1024, 2);
    }

    public function getUsagePercentageAttribute(): float
    {
        if (!$this->max_size_bytes || !$this->size_bytes) {
            return 0.0;
        }
        return round(($this->size_bytes / $this->max_size_bytes) * 100, 2);
    }

    public function isNearQuota(int $threshold = 80): bool
    {
        return $this->usage_percentage >= $threshold;
    }

    /**
     * Create real database on server
     */
    public function createOnServer($server, $dbPassword = null)
    {
        $dbService = new \App\Services\DatabaseManagementService();
        return $dbService->createRealDatabase($this, $server, $dbPassword);
    }

    /**
     * Delete real database from server
     */
    public function deleteFromServer($server)
    {
        $dbService = new \App\Services\DatabaseManagementService();
        return $dbService->deleteRealDatabase($this, $server);
    }

    /**
     * Update real database statistics
     */
    public function updateRealStats()
    {
        $dbService = new \App\Services\DatabaseManagementService();
        return $dbService->updateDatabaseStats($this);
    }

    /**
     * Create database backup
     */
    public function createBackup($server, $backupPath = null)
    {
        $dbService = new \App\Services\DatabaseManagementService();
        return $dbService->createDatabaseBackup($this, $server, $backupPath);
    }

    /**
     * Restore database from backup
     */
    public function restoreFromBackup($server, $backupFile)
    {
        $dbService = new \App\Services\DatabaseManagementService();
        return $dbService->restoreDatabase($this, $server, $backupFile);
    }

    /**
     * Get database connection string
     */
    public function getConnectionString()
    {
        switch ($this->type) {
            case 'mysql':
                return "mysql:host={$this->host};port=" . ($this->port ?: 3306) . ";dbname={$this->name};charset={$this->charset}";
            case 'postgresql':
                return "pgsql:host={$this->host};port=" . ($this->port ?: 5432) . ";dbname={$this->name}";
            default:
                return null;
        }
    }

    /**
     * Test database connection
     */
    public function testConnection($username = null, $password = null)
    {
        try {
            $connectionString = $this->getConnectionString();
            if (!$connectionString) {
                return false;
            }

            $pdo = new \PDO($connectionString, $username, $password);
            $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
