<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class App extends Model
{
    protected $fillable = [
        'domain_id',
        'subdomain_id',
        'user_id',
        'name',
        'slug',
        'type',
        'version',
        'status',
        'install_path',
        'database_name',
        'admin_url',
        'admin_username',
        'admin_email',
        'auto_update',
        'backup_enabled',
        'ssl_required',
        'custom_config',
        'installed_at',
        'last_updated_at',
        'last_backup_at',
        'notes',
    ];

    protected $casts = [
        'auto_update' => 'boolean',
        'backup_enabled' => 'boolean',
        'ssl_required' => 'boolean',
        'custom_config' => 'array',
        'installed_at' => 'datetime',
        'last_updated_at' => 'datetime',
        'last_backup_at' => 'datetime',
    ];

    const TYPES = [
        'wordpress' => 'WordPress',
        'joomla' => 'Joomla',
        'drupal' => 'Drupal',
        'magento' => 'Magento',
        'prestashop' => 'PrestaShop',
        'opencart' => 'OpenCart',
        'laravel' => 'Laravel',
        'codeigniter' => 'CodeIgniter',
        'symfony' => 'Symfony',
        'custom' => 'Custom Application',
    ];

    const STATUSES = [
        'installing' => 'Installing',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'updating' => 'Updating',
        'error' => 'Error',
        'maintenance' => 'Maintenance',
        'suspended' => 'Suspended',
    ];

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }

    public function subdomain(): BelongsTo
    {
        return $this->belongsTo(Subdomain::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function backups(): HasMany
    {
        return $this->hasMany(Backup::class, 'backupable_id')
                    ->where('backupable_type', self::class);
    }

    public function getTypeNameAttribute(): string
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    public function getStatusNameAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    public function getFullUrlAttribute(): string
    {
        $domain = $this->subdomain ? $this->subdomain->full_name : $this->domain->name;
        $protocol = $this->ssl_required ? 'https' : 'http';
        
        return $protocol . '://' . $domain . ($this->install_path !== '/' ? $this->install_path : '');
    }

    public function getAdminUrlFullAttribute(): string
    {
        if (!$this->admin_url) {
            return $this->full_url;
        }

        if (str_starts_with($this->admin_url, 'http')) {
            return $this->admin_url;
        }

        return $this->full_url . '/' . ltrim($this->admin_url, '/');
    }

    public function isWordPress(): bool
    {
        return $this->type === 'wordpress';
    }

    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function needsUpdate(): bool
    {
        if (!$this->auto_update) {
            return false;
        }

        // Here you would implement version checking logic
        // For now, return false as placeholder
        return false;
    }

    public function needsBackup(): bool
    {
        if (!$this->backup_enabled) {
            return false;
        }

        // Check if backup is needed (e.g., daily backup)
        return !$this->last_backup_at || $this->last_backup_at->diffInDays(now()) >= 1;
    }

    public function install(array $config = []): bool
    {
        try {
            $this->update(['status' => 'installing']);

            // Here you would implement the actual installation logic
            // This could involve:
            // - Downloading application files
            // - Creating database
            // - Running installation scripts
            // - Setting up configuration

            $this->update([
                'status' => 'active',
                'installed_at' => now(),
                'custom_config' => array_merge($this->custom_config ?? [], $config),
            ]);

            return true;
        } catch (\Exception $e) {
            $this->update(['status' => 'error']);
            return false;
        }
    }

    public function update(): bool
    {
        if (!$this->auto_update || $this->status !== 'active') {
            return false;
        }

        try {
            $this->update(['status' => 'updating']);

            // Here you would implement the actual update logic
            
            $this->update([
                'status' => 'active',
                'last_updated_at' => now(),
            ]);

            return true;
        } catch (\Exception $e) {
            $this->update(['status' => 'error']);
            return false;
        }
    }

    public function createBackup(string $type = 'auto'): ?Backup
    {
        if (!$this->backup_enabled) {
            return null;
        }

        try {
            $backup = $this->backups()->create([
                'name' => $this->name . '_' . now()->format('Y-m-d_H-i-s'),
                'type' => $type,
                'status' => 'pending',
                'size_bytes' => 0,
                'started_at' => now(),
            ]);

            // Here you would implement the actual backup process
            
            $backup->update([
                'status' => 'completed',
                'completed_at' => now(),
                'size_bytes' => 100 * 1024 * 1024, // Placeholder size
            ]);

            $this->update(['last_backup_at' => now()]);

            return $backup;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function uninstall(): bool
    {
        try {
            // Here you would implement the actual uninstallation logic
            // This could involve:
            // - Removing application files
            // - Dropping database
            // - Cleaning up configuration

            $this->delete();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function getConfigValue(string $key, $default = null)
    {
        return $this->custom_config[$key] ?? $default;
    }

    public function setConfigValue(string $key, $value): void
    {
        $config = $this->custom_config ?? [];
        $config[$key] = $value;
        $this->update(['custom_config' => $config]);
    }

    public function getInstallationAge(): ?int
    {
        return $this->installed_at ? $this->installed_at->diffInDays(now()) : null;
    }

    public function getLastUpdateAge(): ?int
    {
        return $this->last_updated_at ? $this->last_updated_at->diffInDays(now()) : null;
    }

    public function getLastBackupAge(): ?int
    {
        return $this->last_backup_at ? $this->last_backup_at->diffInDays(now()) : null;
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeNeedsUpdate($query)
    {
        return $query->where('auto_update', true)
                    ->where('status', 'active');
    }

    public function scopeNeedsBackup($query)
    {
        return $query->where('backup_enabled', true)
                    ->where('status', 'active')
                    ->where(function($q) {
                        $q->whereNull('last_backup_at')
                          ->orWhere('last_backup_at', '<', now()->subDay());
                    });
    }

    public function scopeWordPress($query)
    {
        return $query->where('type', 'wordpress');
    }

    public function scopeWithSSL($query)
    {
        return $query->where('ssl_required', true);
    }
}
