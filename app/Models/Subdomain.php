<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subdomain extends Model
{
    protected $fillable = [
        'domain_id',
        'name',
        'full_name',
        'document_root',
        'redirect_url',
        'redirect_type',
        'is_active',
        'php_version',
        'ssl_enabled',
        'custom_headers',
        'notes',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'ssl_enabled' => 'boolean',
        'custom_headers' => 'array',
        'redirect_type' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($subdomain) {
            if ($subdomain->domain) {
                $subdomain->full_name = $subdomain->name . '.' . $subdomain->domain->name;
            }
        });

        static::updating(function ($subdomain) {
            if ($subdomain->domain) {
                $subdomain->full_name = $subdomain->name . '.' . $subdomain->domain->name;
            }
        });
    }

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }

    public function dnsRecords(): HasMany
    {
        return $this->hasMany(DnsRecord::class);
    }

    public function sslCertificates(): HasMany
    {
        return $this->hasMany(SslCertificate::class);
    }

    public function isRedirect(): bool
    {
        return !empty($this->redirect_url);
    }

    public function getRedirectTypeNameAttribute(): string
    {
        return match($this->redirect_type) {
            301 => 'Permanent (301)',
            302 => 'Temporary (302)',
            default => 'None'
        };
    }

    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'inactive';
        }

        if ($this->isRedirect()) {
            return 'redirect';
        }

        return 'active';
    }

    public function getPhpVersionAttribute($value): string
    {
        return $value ?? $this->domain->php_version ?? '8.2';
    }
}
