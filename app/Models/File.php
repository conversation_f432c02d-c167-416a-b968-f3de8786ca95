<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class File extends Model
{
    protected $fillable = [
        'user_id',
        'domain_id',
        'name',
        'path',
        'relative_path',
        'type',
        'mime_type',
        'size_bytes',
        'permissions',
        'owner',
        'group',
        'hash',
        'is_hidden',
        'is_system',
        'last_modified_at',
        'last_accessed_at',
        'metadata',
    ];

    protected $casts = [
        'size_bytes' => 'integer',
        'is_hidden' => 'boolean',
        'is_system' => 'boolean',
        'last_modified_at' => 'datetime',
        'last_accessed_at' => 'datetime',
        'metadata' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }

    /**
     * Get file size in human readable format
     */
    public function getSizeHumanAttribute(): string
    {
        $bytes = $this->size_bytes;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if file is an image
     */
    public function isImage(): bool
    {
        return str_starts_with($this->mime_type ?? '', 'image/');
    }

    /**
     * Check if file is a text file
     */
    public function isText(): bool
    {
        $textMimes = [
            'text/plain',
            'text/html',
            'text/css',
            'text/javascript',
            'application/json',
            'application/xml',
            'text/xml',
        ];

        return in_array($this->mime_type, $textMimes) || 
               str_starts_with($this->mime_type ?? '', 'text/');
    }

    /**
     * Check if file is editable
     */
    public function isEditable(): bool
    {
        if ($this->type !== 'file') {
            return false;
        }

        $editableExtensions = [
            'txt', 'html', 'htm', 'css', 'js', 'json', 'xml', 'php', 'py', 'rb', 'java',
            'c', 'cpp', 'h', 'hpp', 'sql', 'md', 'yml', 'yaml', 'ini', 'conf', 'log',
            'sh', 'bash', 'zsh', 'fish', 'ps1', 'bat', 'cmd', 'htaccess', 'env'
        ];

        $extension = strtolower(pathinfo($this->name, PATHINFO_EXTENSION));
        
        return in_array($extension, $editableExtensions) || $this->isText();
    }

    /**
     * Check if file is an archive
     */
    public function isArchive(): bool
    {
        $archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'];
        $extension = strtolower(pathinfo($this->name, PATHINFO_EXTENSION));
        
        return in_array($extension, $archiveExtensions);
    }

    /**
     * Get file icon based on type/extension
     */
    public function getIconAttribute(): string
    {
        if ($this->type === 'directory') {
            return 'folder';
        }

        if ($this->isImage()) {
            return 'image';
        }

        $extension = strtolower(pathinfo($this->name, PATHINFO_EXTENSION));
        
        $iconMap = [
            'pdf' => 'file-pdf',
            'doc' => 'file-word',
            'docx' => 'file-word',
            'xls' => 'file-excel',
            'xlsx' => 'file-excel',
            'ppt' => 'file-powerpoint',
            'pptx' => 'file-powerpoint',
            'zip' => 'file-archive',
            'rar' => 'file-archive',
            '7z' => 'file-archive',
            'tar' => 'file-archive',
            'gz' => 'file-archive',
            'mp3' => 'file-audio',
            'wav' => 'file-audio',
            'mp4' => 'file-video',
            'avi' => 'file-video',
            'mov' => 'file-video',
            'php' => 'file-code',
            'js' => 'file-code',
            'css' => 'file-code',
            'html' => 'file-code',
            'htm' => 'file-code',
            'json' => 'file-code',
            'xml' => 'file-code',
            'sql' => 'file-code',
            'txt' => 'file-text',
            'md' => 'file-text',
            'log' => 'file-text',
        ];

        return $iconMap[$extension] ?? 'file';
    }

    /**
     * Get file color class based on type
     */
    public function getColorClassAttribute(): string
    {
        if ($this->type === 'directory') {
            return 'text-blue-600';
        }

        if ($this->isImage()) {
            return 'text-green-600';
        }

        $extension = strtolower(pathinfo($this->name, PATHINFO_EXTENSION));
        
        $colorMap = [
            'pdf' => 'text-red-600',
            'doc' => 'text-blue-600',
            'docx' => 'text-blue-600',
            'xls' => 'text-green-600',
            'xlsx' => 'text-green-600',
            'ppt' => 'text-orange-600',
            'pptx' => 'text-orange-600',
            'zip' => 'text-purple-600',
            'rar' => 'text-purple-600',
            '7z' => 'text-purple-600',
            'mp3' => 'text-pink-600',
            'wav' => 'text-pink-600',
            'mp4' => 'text-indigo-600',
            'avi' => 'text-indigo-600',
            'php' => 'text-purple-600',
            'js' => 'text-yellow-600',
            'css' => 'text-blue-600',
            'html' => 'text-orange-600',
            'json' => 'text-gray-600',
            'xml' => 'text-gray-600',
            'sql' => 'text-blue-600',
            'txt' => 'text-gray-600',
            'md' => 'text-gray-600',
            'log' => 'text-gray-600',
        ];

        return $colorMap[$extension] ?? 'text-gray-600';
    }

    /**
     * Get permissions in human readable format
     */
    public function getPermissionsHumanAttribute(): string
    {
        $perms = $this->permissions;
        $result = '';

        // Owner permissions
        $result .= ($perms[1] & 4) ? 'r' : '-';
        $result .= ($perms[1] & 2) ? 'w' : '-';
        $result .= ($perms[1] & 1) ? 'x' : '-';

        // Group permissions
        $result .= ($perms[2] & 4) ? 'r' : '-';
        $result .= ($perms[2] & 2) ? 'w' : '-';
        $result .= ($perms[2] & 1) ? 'x' : '-';

        // Other permissions
        $result .= ($perms[3] & 4) ? 'r' : '-';
        $result .= ($perms[3] & 2) ? 'w' : '-';
        $result .= ($perms[3] & 1) ? 'x' : '-';

        return $result;
    }

    /**
     * Check if file exists on disk
     */
    public function existsOnDisk(): bool
    {
        return file_exists($this->path);
    }

    /**
     * Get file content (for text files)
     */
    public function getContent(): ?string
    {
        if (!$this->isText() || !$this->existsOnDisk()) {
            return null;
        }

        return file_get_contents($this->path);
    }

    /**
     * Update file content (for text files)
     */
    public function updateContent(string $content): bool
    {
        if (!$this->isEditable()) {
            return false;
        }

        $result = file_put_contents($this->path, $content);
        
        if ($result !== false) {
            $this->update([
                'size_bytes' => strlen($content),
                'last_modified_at' => now(),
                'hash' => hash_file('md5', $this->path),
            ]);
            
            return true;
        }

        return false;
    }

    /**
     * Scope for files only
     */
    public function scopeFiles($query)
    {
        return $query->where('type', 'file');
    }

    /**
     * Scope for directories only
     */
    public function scopeDirectories($query)
    {
        return $query->where('type', 'directory');
    }

    /**
     * Scope for visible files (not hidden)
     */
    public function scopeVisible($query)
    {
        return $query->where('is_hidden', false);
    }

    /**
     * Scope for non-system files
     */
    public function scopeNonSystem($query)
    {
        return $query->where('is_system', false);
    }
}
