<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Crypt;

class TwoFactorAuth extends Model
{
    use HasFactory;

    protected $table = 'two_factor_auth';

    protected $fillable = [
        'user_id',
        'secret_key',
        'enabled',
        'enabled_at',
        'recovery_codes',
        'recovery_codes_generated_at',
        'last_used_recovery_code',
        'last_used_at',
        'backup_method',
        'backup_contact',
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'enabled_at' => 'datetime',
        'recovery_codes' => 'array',
        'recovery_codes_generated_at' => 'datetime',
        'last_used_at' => 'datetime',
    ];

    protected $hidden = [
        'secret_key',
        'recovery_codes',
    ];

    /**
     * Get the user that owns the 2FA settings
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the encrypted secret key
     */
    public function getSecretKeyAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    /**
     * Set the encrypted secret key
     */
    public function setSecretKeyAttribute($value)
    {
        $this->attributes['secret_key'] = $value ? Crypt::encryptString($value) : null;
    }

    /**
     * Get the encrypted recovery codes
     */
    public function getRecoveryCodesAttribute($value)
    {
        if (!$value) return null;
        
        $codes = json_decode($value, true);
        return array_map(function($code) {
            return Crypt::decryptString($code);
        }, $codes);
    }

    /**
     * Set the encrypted recovery codes
     */
    public function setRecoveryCodesAttribute($value)
    {
        if (!$value) {
            $this->attributes['recovery_codes'] = null;
            return;
        }

        $encryptedCodes = array_map(function($code) {
            return Crypt::encryptString($code);
        }, $value);

        $this->attributes['recovery_codes'] = json_encode($encryptedCodes);
    }

    /**
     * Generate new recovery codes
     */
    public function generateRecoveryCodes(): array
    {
        $codes = [];
        for ($i = 0; $i < 8; $i++) {
            $codes[] = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8));
        }

        $this->recovery_codes = $codes;
        $this->recovery_codes_generated_at = now();
        $this->save();

        return $codes;
    }

    /**
     * Use a recovery code
     */
    public function useRecoveryCode(string $code): bool
    {
        $recoveryCodes = $this->recovery_codes;
        
        if (!$recoveryCodes || !in_array(strtoupper($code), $recoveryCodes)) {
            return false;
        }

        // Remove the used code
        $this->recovery_codes = array_values(array_diff($recoveryCodes, [strtoupper($code)]));
        $this->last_used_recovery_code = strtoupper($code);
        $this->last_used_at = now();
        $this->save();

        return true;
    }

    /**
     * Check if 2FA is enabled
     */
    public function isEnabled(): bool
    {
        return $this->enabled && !empty($this->secret_key);
    }

    /**
     * Enable 2FA
     */
    public function enable(): void
    {
        $this->enabled = true;
        $this->enabled_at = now();
        $this->save();
    }

    /**
     * Disable 2FA
     */
    public function disable(): void
    {
        $this->enabled = false;
        $this->enabled_at = null;
        $this->secret_key = null;
        $this->recovery_codes = null;
        $this->save();
    }
}
