<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WebHost extends Model
{
    protected $fillable = [
        'domain_id',
        'server_name',
        'server_alias',
        'document_root',
        'index_files',
        'web_server',
        'php_version',
        'php_enabled',
        'php_modules',
        'custom_directives',
        'gzip_enabled',
        'access_log_enabled',
        'error_log_enabled',
        'access_log_path',
        'error_log_path',
        'security_headers',
        'hotlink_protection',
        'blocked_ips',
        'allowed_ips',
        'is_active',
        'last_config_update',
    ];

    protected $casts = [
        'php_enabled' => 'boolean',
        'php_modules' => 'array',
        'custom_directives' => 'array',
        'gzip_enabled' => 'boolean',
        'access_log_enabled' => 'boolean',
        'error_log_enabled' => 'boolean',
        'security_headers' => 'array',
        'hotlink_protection' => 'boolean',
        'blocked_ips' => 'array',
        'allowed_ips' => 'array',
        'is_active' => 'boolean',
        'last_config_update' => 'datetime',
    ];

    const STATUSES = [
        'active' => 'Active',
        'inactive' => 'Inactive',
        'suspended' => 'Suspended',
        'maintenance' => 'Maintenance',
        'error' => 'Error',
    ];

    const PHP_VERSIONS = [
        '7.4' => 'PHP 7.4',
        '8.0' => 'PHP 8.0',
        '8.1' => 'PHP 8.1',
        '8.2' => 'PHP 8.2',
        '8.3' => 'PHP 8.3',
    ];

    const BACKUP_FREQUENCIES = [
        'daily' => 'Daily',
        'weekly' => 'Weekly',
        'monthly' => 'Monthly',
        'manual' => 'Manual Only',
    ];

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }

    public function subdomain(): BelongsTo
    {
        return $this->belongsTo(Subdomain::class);
    }

    public function server(): BelongsTo
    {
        return $this->belongsTo(Server::class);
    }

    public function backups(): HasMany
    {
        return $this->hasMany(Backup::class, 'backupable_id')
                    ->where('backupable_type', self::class);
    }

    public function getStatusNameAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    public function getPhpVersionNameAttribute(): string
    {
        return self::PHP_VERSIONS[$this->php_version] ?? $this->php_version;
    }

    public function getBackupFrequencyNameAttribute(): string
    {
        return self::BACKUP_FREQUENCIES[$this->backup_frequency] ?? $this->backup_frequency;
    }

    public function getDiskUsagePercentageAttribute(): float
    {
        if (!$this->max_disk_mb || $this->max_disk_mb <= 0) {
            return 0;
        }

        return min(100, ($this->disk_usage_mb / $this->max_disk_mb) * 100);
    }

    public function getBandwidthUsagePercentageAttribute(): float
    {
        if (!$this->max_bandwidth_mb || $this->max_bandwidth_mb <= 0) {
            return 0;
        }

        return min(100, ($this->bandwidth_usage_mb / $this->max_bandwidth_mb) * 100);
    }

    public function getFormattedDiskUsageAttribute(): string
    {
        return $this->formatBytes($this->disk_usage_mb * 1024 * 1024);
    }

    public function getFormattedBandwidthUsageAttribute(): string
    {
        return $this->formatBytes($this->bandwidth_usage_mb * 1024 * 1024);
    }

    public function getFormattedMaxDiskAttribute(): string
    {
        return $this->formatBytes($this->max_disk_mb * 1024 * 1024);
    }

    public function getFormattedMaxBandwidthAttribute(): string
    {
        return $this->formatBytes($this->max_bandwidth_mb * 1024 * 1024);
    }

    public function isOverDiskQuota(): bool
    {
        return $this->max_disk_mb > 0 && $this->disk_usage_mb > $this->max_disk_mb;
    }

    public function isOverBandwidthQuota(): bool
    {
        return $this->max_bandwidth_mb > 0 && $this->bandwidth_usage_mb > $this->max_bandwidth_mb;
    }

    public function needsBackup(): bool
    {
        if (!$this->auto_backup || $this->backup_frequency === 'manual') {
            return false;
        }

        if (!$this->last_backup_at) {
            return true;
        }

        return match($this->backup_frequency) {
            'daily' => $this->last_backup_at->diffInDays(now()) >= 1,
            'weekly' => $this->last_backup_at->diffInWeeks(now()) >= 1,
            'monthly' => $this->last_backup_at->diffInMonths(now()) >= 1,
            default => false
        };
    }

    public function updateUsageStats(): void
    {
        try {
            // Here you would implement actual disk and bandwidth usage calculation
            // This could involve checking server files and logs
            
            $diskUsage = $this->calculateDiskUsage();
            $bandwidthUsage = $this->calculateBandwidthUsage();

            $this->update([
                'disk_usage_mb' => $diskUsage,
                'bandwidth_usage_mb' => $bandwidthUsage,
            ]);
        } catch (\Exception $e) {
            // Log error
        }
    }

    public function createBackup(string $type = 'auto'): ?Backup
    {
        try {
            $backup = $this->backups()->create([
                'name' => $this->name . '_' . now()->format('Y-m-d_H-i-s'),
                'type' => $type,
                'status' => 'pending',
                'size_bytes' => 0,
                'started_at' => now(),
            ]);

            // Here you would implement the actual backup process
            
            $backup->update([
                'status' => 'completed',
                'completed_at' => now(),
                'size_bytes' => $this->disk_usage_mb * 1024 * 1024, // Estimate
            ]);

            $this->update(['last_backup_at' => now()]);

            return $backup;
        } catch (\Exception $e) {
            return null;
        }
    }

    private function calculateDiskUsage(): int
    {
        // Placeholder - implement actual disk usage calculation
        return $this->disk_usage_mb ?? 0;
    }

    private function calculateBandwidthUsage(): int
    {
        // Placeholder - implement actual bandwidth usage calculation
        return $this->bandwidth_usage_mb ?? 0;
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeOverQuota($query)
    {
        return $query->whereRaw('disk_usage_mb > max_disk_mb OR bandwidth_usage_mb > max_bandwidth_mb');
    }

    public function scopeNeedsBackup($query)
    {
        return $query->where('auto_backup', true)
                    ->where('backup_frequency', '!=', 'manual')
                    ->where(function($q) {
                        $q->whereNull('last_backup_at')
                          ->orWhere('last_backup_at', '<', now()->subDay());
                    });
    }
}
