<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SslCertificateRenewal extends Model
{
    protected $fillable = [
        'ssl_certificate_id',
        'status',
        'started_at',
        'completed_at',
        'attempt_number',
        'renewal_method',
        'challenge_data',
        'error_message',
        'logs',
        'old_expiry_date',
        'new_expiry_date',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'logs' => 'array',
        'old_expiry_date' => 'date',
        'new_expiry_date' => 'date',
        'attempt_number' => 'integer',
    ];

    const STATUSES = [
        'pending' => 'Pending',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'failed' => 'Failed',
        'cancelled' => 'Cancelled',
    ];

    const RENEWAL_METHODS = [
        'acme' => 'ACME Protocol',
        'manual' => 'Manual Upload',
        'api' => 'Provider API',
    ];

    public function sslCertificate(): BelongsTo
    {
        return $this->belongsTo(SslCertificate::class);
    }

    public function getStatusNameAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    public function getRenewalMethodNameAttribute(): string
    {
        return self::RENEWAL_METHODS[$this->renewal_method] ?? $this->renewal_method;
    }

    public function getDurationAttribute(): ?int
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        return $this->started_at->diffInSeconds($this->completed_at);
    }

    public function getFormattedDurationAttribute(): ?string
    {
        $duration = $this->duration;
        if (!$duration) {
            return null;
        }

        if ($duration < 60) {
            return $duration . ' seconds';
        } elseif ($duration < 3600) {
            return round($duration / 60, 1) . ' minutes';
        } else {
            return round($duration / 3600, 1) . ' hours';
        }
    }

    public function isSuccessful(): bool
    {
        return $this->status === 'completed';
    }

    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    public function isInProgress(): bool
    {
        return in_array($this->status, ['pending', 'in_progress']);
    }

    public function addLog(string $level, string $message, array $context = []): void
    {
        $logs = $this->logs ?? [];
        $logs[] = [
            'timestamp' => now()->toISOString(),
            'level' => $level,
            'message' => $message,
            'context' => $context,
        ];

        $this->update(['logs' => $logs]);
    }

    public function markAsCompleted(?string $newExpiryDate = null): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'new_expiry_date' => $newExpiryDate,
        ]);

        $this->addLog('info', 'SSL certificate renewal completed successfully');
    }

    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'completed_at' => now(),
            'error_message' => $errorMessage,
        ]);

        $this->addLog('error', 'SSL certificate renewal failed', ['error' => $errorMessage]);
    }

    public function scopeSuccessful($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeInProgress($query)
    {
        return $query->whereIn('status', ['pending', 'in_progress']);
    }

    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('started_at', '>=', now()->subDays($days));
    }
}
