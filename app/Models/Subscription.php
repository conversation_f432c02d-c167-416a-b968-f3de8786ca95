<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subscription extends Model
{
    protected $fillable = [
        'user_id',
        'plan_id',
        'status',
        'amount',
        'currency',
        'billing_cycle',
        'started_at',
        'current_period_start',
        'current_period_end',
        'trial_ends_at',
        'cancelled_at',
        'expires_at',
        'cancellation_reason',
        'auto_renew',
        'grace_period_days',
        'gateway',
        'gateway_subscription_id',
        'gateway_customer_id',
        'gateway_data',
        'usage_limits',
        'current_usage',
        'usage_alerts_enabled',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'started_at' => 'date',
        'current_period_start' => 'date',
        'current_period_end' => 'date',
        'trial_ends_at' => 'date',
        'cancelled_at' => 'date',
        'expires_at' => 'date',
        'auto_renew' => 'boolean',
        'gateway_data' => 'array',
        'usage_limits' => 'array',
        'current_usage' => 'array',
        'usage_alerts_enabled' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Check if subscription is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && 
               $this->current_period_end->isFuture();
    }

    /**
     * Check if subscription is in trial
     */
    public function isOnTrial(): bool
    {
        return $this->trial_ends_at && 
               $this->trial_ends_at->isFuture();
    }

    /**
     * Check if subscription is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if subscription is expired
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired' || 
               $this->current_period_end->isPast();
    }

    /**
     * Check if subscription is suspended
     */
    public function isSuspended(): bool
    {
        return $this->status === 'suspended';
    }

    /**
     * Get days until renewal
     */
    public function getDaysUntilRenewalAttribute(): int
    {
        return now()->diffInDays($this->current_period_end, false);
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute(): string
    {
        return '$' . number_format($this->amount, 2);
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'active' => 'bg-green-100 text-green-800',
            'trial' => 'bg-blue-100 text-blue-800',
            'suspended' => 'bg-yellow-100 text-yellow-800',
            'cancelled' => 'bg-red-100 text-red-800',
            'expired' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Cancel subscription
     */
    public function cancel(string $reason = null): bool
    {
        return $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => $reason,
            'auto_renew' => false,
        ]);
    }

    /**
     * Suspend subscription
     */
    public function suspend(): bool
    {
        return $this->update(['status' => 'suspended']);
    }

    /**
     * Resume subscription
     */
    public function resume(): bool
    {
        return $this->update(['status' => 'active']);
    }

    /**
     * Renew subscription for next period
     */
    public function renew(): bool
    {
        $nextPeriodStart = $this->current_period_end->addDay();
        $nextPeriodEnd = match($this->billing_cycle) {
            'monthly' => $nextPeriodStart->copy()->addMonth(),
            'yearly' => $nextPeriodStart->copy()->addYear(),
            default => $nextPeriodStart->copy()->addMonth(),
        };

        return $this->update([
            'current_period_start' => $nextPeriodStart,
            'current_period_end' => $nextPeriodEnd,
            'status' => 'active',
        ]);
    }

    /**
     * Update usage for a specific resource
     */
    public function updateUsage(string $resource, int $value): bool
    {
        $usage = $this->current_usage ?? [];
        $usage[$resource] = $value;

        return $this->update(['current_usage' => $usage]);
    }

    /**
     * Check if usage limit is exceeded for a resource
     */
    public function isUsageLimitExceeded(string $resource): bool
    {
        $limits = $this->usage_limits ?? [];
        $usage = $this->current_usage ?? [];

        if (!isset($limits[$resource])) {
            return false; // No limit set
        }

        $limit = $limits[$resource];
        $currentUsage = $usage[$resource] ?? 0;

        return $limit !== -1 && $currentUsage >= $limit; // -1 means unlimited
    }

    /**
     * Get usage percentage for a resource
     */
    public function getUsagePercentage(string $resource): float
    {
        $limits = $this->usage_limits ?? [];
        $usage = $this->current_usage ?? [];

        if (!isset($limits[$resource]) || $limits[$resource] === -1) {
            return 0; // Unlimited
        }

        $limit = $limits[$resource];
        $currentUsage = $usage[$resource] ?? 0;

        return $limit > 0 ? min(100, ($currentUsage / $limit) * 100) : 0;
    }

    /**
     * Scope for active subscriptions
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for trial subscriptions
     */
    public function scopeOnTrial($query)
    {
        return $query->where('trial_ends_at', '>', now());
    }

    /**
     * Scope for cancelled subscriptions
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Scope for expired subscriptions
     */
    public function scopeExpired($query)
    {
        return $query->where('current_period_end', '<', now());
    }

    /**
     * Scope for expiring soon (within days)
     */
    public function scopeExpiringSoon($query, int $days = 7)
    {
        return $query->where('current_period_end', '<=', now()->addDays($days))
                    ->where('current_period_end', '>', now());
    }
}
