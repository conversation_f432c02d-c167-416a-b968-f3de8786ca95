<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PaymentGateway extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'provider',
        'is_active',
        'is_default',
        'configuration',
        'supported_currencies',
        'supported_countries',
        'fee_percentage',
        'fee_fixed',
        'min_amount',
        'max_amount',
        'webhook_url',
        'webhook_secret',
        'description',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'configuration' => 'array',
        'supported_currencies' => 'array',
        'supported_countries' => 'array',
        'fee_percentage' => 'decimal:4',
        'fee_fixed' => 'decimal:2',
    ];

    protected $hidden = [
        'configuration',
        'webhook_secret',
    ];

    /**
     * Get configuration value
     */
    public function getConfig(string $key, $default = null)
    {
        return data_get($this->configuration, $key, $default);
    }

    /**
     * Set configuration value
     */
    public function setConfig(string $key, $value): void
    {
        $config = $this->configuration ?? [];
        data_set($config, $key, $value);
        $this->configuration = $config;
    }

    /**
     * Check if currency is supported
     */
    public function supportsCurrency(string $currency): bool
    {
        if (!$this->supported_currencies) {
            return true; // If no restrictions, support all
        }

        return in_array(strtoupper($currency), $this->supported_currencies);
    }

    /**
     * Check if country is supported
     */
    public function supportsCountry(string $country): bool
    {
        if (!$this->supported_countries) {
            return true; // If no restrictions, support all
        }

        return in_array(strtoupper($country), $this->supported_countries);
    }

    /**
     * Calculate fees for amount
     */
    public function calculateFees(float $amount): array
    {
        $percentageFee = $amount * $this->fee_percentage;
        $fixedFee = $this->fee_fixed;
        $totalFee = $percentageFee + $fixedFee;

        return [
            'percentage_fee' => round($percentageFee, 2),
            'fixed_fee' => round($fixedFee, 2),
            'total_fee' => round($totalFee, 2),
            'net_amount' => round($amount - $totalFee, 2),
        ];
    }

    /**
     * Check if amount is within limits
     */
    public function isAmountValid(float $amount): bool
    {
        $amountInCents = $amount * 100;

        if ($this->min_amount && $amountInCents < $this->min_amount) {
            return false;
        }

        if ($this->max_amount && $amountInCents > $this->max_amount) {
            return false;
        }

        return true;
    }

    /**
     * Get minimum amount in dollars
     */
    public function getMinAmountDollarsAttribute(): float
    {
        return $this->min_amount ? $this->min_amount / 100 : 0;
    }

    /**
     * Get maximum amount in dollars
     */
    public function getMaxAmountDollarsAttribute(): ?float
    {
        return $this->max_amount ? $this->max_amount / 100 : null;
    }

    /**
     * Set as default gateway
     */
    public function setAsDefault(): bool
    {
        // Remove default from other gateways
        static::where('is_default', true)->update(['is_default' => false]);
        
        // Set this as default
        return $this->update(['is_default' => true]);
    }

    /**
     * Scope for active gateways
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for default gateway
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope ordered by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Scope for specific provider
     */
    public function scopeForProvider($query, string $provider)
    {
        return $query->where('provider', $provider);
    }

    /**
     * Get icon for gateway
     */
    public function getIconAttribute(): string
    {
        return match($this->slug) {
            'stripe' => 'fab fa-stripe',
            'paypal' => 'fab fa-paypal',
            'bank_transfer' => 'fas fa-university',
            'cryptocurrency' => 'fab fa-bitcoin',
            default => 'fas fa-credit-card',
        };
    }

    /**
     * Get display name with status
     */
    public function getDisplayNameAttribute(): string
    {
        $name = $this->name;
        
        if ($this->is_default) {
            $name .= ' (Default)';
        }
        
        if (!$this->is_active) {
            $name .= ' (Inactive)';
        }
        
        return $name;
    }
}
