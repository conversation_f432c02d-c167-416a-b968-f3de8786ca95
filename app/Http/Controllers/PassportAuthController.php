<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class PassportAuthController extends Controller
{
    /**
     * Register a new user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        // Assign default role to user
        try {
            $user->assignRole('user');
            \Log::info('Default role assigned to user: ' . $user->email);
        } catch (\Exception $e) {
            \Log::error('Failed to assign default role: ' . $e->getMessage());
        }

        // Send email verification notification
        try {
            $user->sendEmailVerificationNotification();
            \Log::info('API: Verification email sent to: ' . $user->email);
        } catch (\Exception $e) {
            \Log::error('API: Failed to send verification email: ' . $e->getMessage());

            // Log mail configuration for debugging
            $mailDriver = config('mail.default');
            \Log::error('Mail Driver: ' . $mailDriver);

            if ($mailDriver === 'smtp') {
                \Log::error('SMTP Configuration: ' . config('mail.mailers.smtp.host') . ':' . config('mail.mailers.smtp.port'));
            }

            \Log::error('From Address: ' . config('mail.from.address'));

            // Don't automatically verify in API context
            \Log::warning('API: Email verification failed but registration continued for user: ' . $user->email);
        }

        $token = $user->createToken('Personal Access Token')->accessToken;

        return response()->json([
            'user' => $user,
            'token' => $token,
            'message' => 'User registered successfully. Please check your email to verify your account.'
        ], 201);
    }

    /**
     * Login user and create token
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|string|email',
            'password' => 'required|string',
        ]);

        if (!Auth::attempt($request->only('email', 'password'))) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        $user = User::where('email', $request->email)->firstOrFail();
        $token = $user->createToken('Personal Access Token')->accessToken;

        return response()->json([
            'user' => $user,
            'token' => $token,
            'message' => 'User logged in successfully'
        ]);
    }

    /**
     * Get the authenticated user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function user(Request $request)
    {
        return response()->json($request->user());
    }

    /**
     * Logout user (Revoke the token)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        $request->user()->token()->revoke();

        return response()->json([
            'message' => 'Successfully logged out'
        ]);
    }
}
