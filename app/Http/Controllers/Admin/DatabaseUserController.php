<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DatabaseUser;
use App\Models\Database;
use App\Models\Server;
use App\Services\DatabaseManagementService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class DatabaseUserController extends Controller
{
    private $dbService;

    public function __construct()
    {
        $this->dbService = new DatabaseManagementService();
    }

    /**
     * Display a listing of database users
     */
    public function index(Request $request)
    {
        $query = DatabaseUser::with(['user']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('username', 'like', "%{$search}%")
                  ->orWhere('host', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $isActive = $request->get('status') === 'active';
            $query->where('is_active', $isActive);
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->get('user_id'));
        }

        $databaseUsers = $query->latest()->paginate(15);
        $stats = $this->getDatabaseUserStats();

        return view('admin.database-users.index', compact('databaseUsers', 'stats'));
    }

    /**
     * Show the form for creating a new database user
     */
    public function create()
    {
        $users = \App\Models\User::all();
        return view('admin.database-users.create', compact('users'));
    }

    /**
     * Store a newly created database user
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => ['required', 'exists:users,id'],
            'username' => ['required', 'string', 'max:64', 'unique:database_users', 'regex:/^[a-zA-Z0-9_]+$/'],
            'password' => ['required', 'string', 'min:8'],
            'host' => ['nullable', 'string', 'max:255'],
            'global_privileges' => ['nullable', 'array'],
            'global_privileges.*' => ['string', 'in:SELECT,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER,INDEX,GRANT'],
            'description' => ['nullable', 'string', 'max:1000'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $data = $validator->validated();
            $data['password_hash'] = Hash::make($data['password']);
            $data['host'] = $data['host'] ?: '%';
            $data['is_active'] = true;

            // Create database user record
            $databaseUser = DatabaseUser::create($data);

            // Create actual database user on server
            $server = Server::where('is_active', true)->first();
            if ($server) {
                $result = $this->createRealDatabaseUser($databaseUser, $server, $data['password']);
                if (!$result['success']) {
                    $databaseUser->delete();
                    return back()->with('error', 'Failed to create database user on server: ' . $result['message'])->withInput();
                }
            }

            return redirect()->route('admin.database-users.index')
                ->with('success', 'Database user created successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Error creating database user: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Display the specified database user
     */
    public function show(DatabaseUser $databaseUser)
    {
        $databaseUser->load(['user', 'databaseUserPermissions.database']);
        return view('admin.database-users.show', compact('databaseUser'));
    }

    /**
     * Show the form for editing the specified database user
     */
    public function edit(DatabaseUser $databaseUser)
    {
        $users = \App\Models\User::all();
        return view('admin.database-users.edit', compact('databaseUser', 'users'));
    }

    /**
     * Update the specified database user
     */
    public function update(Request $request, DatabaseUser $databaseUser)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => ['required', 'exists:users,id'],
            'username' => ['required', 'string', 'max:64', 'unique:database_users,username,' . $databaseUser->id, 'regex:/^[a-zA-Z0-9_]+$/'],
            'password' => ['nullable', 'string', 'min:8'],
            'host' => ['nullable', 'string', 'max:255'],
            'global_privileges' => ['nullable', 'array'],
            'global_privileges.*' => ['string', 'in:SELECT,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER,INDEX,GRANT'],
            'description' => ['nullable', 'string', 'max:1000'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $data = $validator->validated();
            $data['host'] = $data['host'] ?: '%';

            // Update password if provided
            if (!empty($data['password'])) {
                $data['password_hash'] = Hash::make($data['password']);
                
                // Update password on server
                $server = Server::where('is_active', true)->first();
                if ($server) {
                    $this->updateDatabaseUserPassword($databaseUser, $server, $data['password']);
                }
            }

            unset($data['password']);
            $databaseUser->update($data);

            return redirect()->route('admin.database-users.index')
                ->with('success', 'Database user updated successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Error updating database user: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Remove the specified database user
     */
    public function destroy(DatabaseUser $databaseUser)
    {
        try {
            // Delete user from server
            $server = Server::where('is_active', true)->first();
            if ($server) {
                $result = $this->deleteRealDatabaseUser($databaseUser, $server);
                if (!$result['success']) {
                    return back()->with('error', 'Failed to delete database user from server: ' . $result['message']);
                }
            }

            // Delete database user record
            $databaseUser->delete();
            
            return redirect()->route('admin.database-users.index')
                ->with('success', 'Database user deleted successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Error deleting database user: ' . $e->getMessage());
        }
    }

    /**
     * Grant database permissions to user
     */
    public function grantPermissions(Request $request, DatabaseUser $databaseUser)
    {
        $validator = Validator::make($request->all(), [
            'database_id' => ['required', 'exists:databases,id'],
            'permissions' => ['required', 'array'],
            'permissions.*' => ['string', 'in:SELECT,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER,INDEX']
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        try {
            $database = Database::findOrFail($request->database_id);
            
            // Create permission record
            $databaseUser->databaseUserPermissions()->updateOrCreate(
                ['database_id' => $database->id],
                [
                    'permissions' => $request->permissions,
                    'granted_at' => now(),
                    'granted_by' => auth()->id(),
                    'is_active' => true
                ]
            );

            // Grant permissions on server
            $server = Server::where('is_active', true)->first();
            if ($server) {
                $this->grantDatabasePermissions($databaseUser, $database, $server, $request->permissions);
            }

            return back()->with('success', 'Database permissions granted successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Error granting permissions: ' . $e->getMessage());
        }
    }

    /**
     * Create real database user on server
     */
    private function createRealDatabaseUser(DatabaseUser $databaseUser, Server $server, $password)
    {
        try {
            $sshService = new \App\Services\SSHService();
            $serverPassword = $server->password ? decrypt($server->password) : null;
            
            $sshService->connect(
                $server->hostname,
                $server->port,
                $server->username,
                $serverPassword,
                $server->ssh_key
            );

            // Create MySQL user
            $createUserCommand = "sudo mysql -e \"CREATE USER IF NOT EXISTS '{$databaseUser->username}'@'{$databaseUser->host}' IDENTIFIED BY '{$password}';\"";
            $sshService->execute($createUserCommand);

            // Grant global privileges if any
            if ($databaseUser->global_privileges) {
                $privileges = implode(', ', $databaseUser->global_privileges);
                $grantCommand = "sudo mysql -e \"GRANT {$privileges} ON *.* TO '{$databaseUser->username}'@'{$databaseUser->host}';\"";
                $sshService->execute($grantCommand);
            }

            // Flush privileges
            $flushCommand = "sudo mysql -e \"FLUSH PRIVILEGES;\"";
            $sshService->execute($flushCommand);

            return [
                'success' => true,
                'message' => "Database user '{$databaseUser->username}' created successfully"
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete real database user from server
     */
    private function deleteRealDatabaseUser(DatabaseUser $databaseUser, Server $server)
    {
        try {
            $sshService = new \App\Services\SSHService();
            $serverPassword = $server->password ? decrypt($server->password) : null;
            
            $sshService->connect(
                $server->hostname,
                $server->port,
                $server->username,
                $serverPassword,
                $server->ssh_key
            );

            // Drop MySQL user
            $dropUserCommand = "sudo mysql -e \"DROP USER IF EXISTS '{$databaseUser->username}'@'{$databaseUser->host}';\"";
            $sshService->execute($dropUserCommand);

            // Flush privileges
            $flushCommand = "sudo mysql -e \"FLUSH PRIVILEGES;\"";
            $sshService->execute($flushCommand);

            return [
                'success' => true,
                'message' => "Database user '{$databaseUser->username}' deleted successfully"
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Update database user password on server
     */
    private function updateDatabaseUserPassword(DatabaseUser $databaseUser, Server $server, $newPassword)
    {
        try {
            $sshService = new \App\Services\SSHService();
            $serverPassword = $server->password ? decrypt($server->password) : null;
            
            $sshService->connect(
                $server->hostname,
                $server->port,
                $server->username,
                $serverPassword,
                $server->ssh_key
            );

            // Update password
            $updatePasswordCommand = "sudo mysql -e \"ALTER USER '{$databaseUser->username}'@'{$databaseUser->host}' IDENTIFIED BY '{$newPassword}';\"";
            $sshService->execute($updatePasswordCommand);

            // Flush privileges
            $flushCommand = "sudo mysql -e \"FLUSH PRIVILEGES;\"";
            $sshService->execute($flushCommand);

            return true;

        } catch (\Exception $e) {
            \Log::error("Failed to update database user password: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Grant database permissions on server
     */
    private function grantDatabasePermissions(DatabaseUser $databaseUser, Database $database, Server $server, $permissions)
    {
        try {
            $sshService = new \App\Services\SSHService();
            $serverPassword = $server->password ? decrypt($server->password) : null;
            
            $sshService->connect(
                $server->hostname,
                $server->port,
                $server->username,
                $serverPassword,
                $server->ssh_key
            );

            // Grant permissions
            $privilegesList = implode(', ', $permissions);
            $grantCommand = "sudo mysql -e \"GRANT {$privilegesList} ON \`{$database->name}\`.* TO '{$databaseUser->username}'@'{$databaseUser->host}';\"";
            $sshService->execute($grantCommand);

            // Flush privileges
            $flushCommand = "sudo mysql -e \"FLUSH PRIVILEGES;\"";
            $sshService->execute($flushCommand);

            return true;

        } catch (\Exception $e) {
            \Log::error("Failed to grant database permissions: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get database user statistics
     */
    private function getDatabaseUserStats()
    {
        return [
            'total_users' => DatabaseUser::count(),
            'active_users' => DatabaseUser::where('is_active', true)->count(),
            'inactive_users' => DatabaseUser::where('is_active', false)->count(),
            'users_today' => DatabaseUser::whereDate('created_at', today())->count(),
            'users_this_month' => DatabaseUser::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
        ];
    }
}
