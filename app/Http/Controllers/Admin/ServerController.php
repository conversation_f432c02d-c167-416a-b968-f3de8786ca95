<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Server;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ServerController extends Controller
{
    public function index(Request $request)
    {
        $query = Server::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('hostname', 'like', "%{$search}%")
                  ->orWhere('ip_address', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Sort
        $sortBy = $request->get('sort', 'name');
        $sortDirection = $request->get('direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $servers = $query->paginate(10)->withQueryString();

        // Statistics
        $stats = [
            'total' => Server::count(),
            'online' => Server::where('status', 'online')->count(),
            'offline' => Server::where('status', 'offline')->count(),
            'maintenance' => Server::where('status', 'maintenance')->count(),
        ];

        return view('admin.servers.index', compact('servers', 'stats'));
    }

    public function create()
    {
        return view('admin.servers.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'hostname' => 'required|string|max:255',
            'ip_address' => 'required|ip',
            'port' => 'required|integer|min:1|max:65535',
            'username' => 'required|string|max:255',
            'password' => 'nullable|string',
            'ssh_key' => 'nullable|string',
            'type' => 'required|in:web,database,mail,dns,load_balancer,storage',
            'operating_system' => 'nullable|string|max:255',
            'control_panel' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'specifications.cpu' => 'nullable|string',
            'specifications.ram' => 'nullable|string',
            'specifications.storage' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $serverData = $request->only([
            'name', 'hostname', 'ip_address', 'port', 'username', 
            'password', 'ssh_key', 'type', 'operating_system', 
            'control_panel', 'notes'
        ]);

        // Handle specifications
        if ($request->has('specifications')) {
            $serverData['specifications'] = array_filter($request->specifications);
        }

        // Encrypt password if provided
        if ($request->filled('password')) {
            $serverData['password'] = encrypt($request->password);
        }

        $server = Server::create($serverData);

        // Initial ping to set status
        $server->ping();
        $server->updateMonitoringData();

        return redirect()->route('admin.servers.index')
            ->with('success', 'Server created successfully!');
    }

    public function show(Server $server)
    {
        // Update monitoring data
        $server->updateMonitoringData();
        
        return view('admin.servers.show', compact('server'));
    }

    public function edit(Server $server)
    {
        return view('admin.servers.edit', compact('server'));
    }

    public function update(Request $request, Server $server)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'hostname' => 'required|string|max:255',
            'ip_address' => 'required|ip',
            'port' => 'required|integer|min:1|max:65535',
            'username' => 'required|string|max:255',
            'password' => 'nullable|string',
            'ssh_key' => 'nullable|string',
            'type' => 'required|in:web,database,mail,dns,load_balancer,storage',
            'operating_system' => 'nullable|string|max:255',
            'control_panel' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'specifications.cpu' => 'nullable|string',
            'specifications.ram' => 'nullable|string',
            'specifications.storage' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $serverData = $request->only([
            'name', 'hostname', 'ip_address', 'port', 'username', 
            'ssh_key', 'type', 'operating_system', 
            'control_panel', 'notes'
        ]);

        // Handle specifications
        if ($request->has('specifications')) {
            $serverData['specifications'] = array_filter($request->specifications);
        }

        // Handle password update
        if ($request->filled('password')) {
            $serverData['password'] = encrypt($request->password);
        }

        $server->update($serverData);

        return redirect()->route('admin.servers.index')
            ->with('success', 'Server updated successfully!');
    }

    public function destroy(Server $server)
    {
        $server->delete();

        return redirect()->route('admin.servers.index')
            ->with('success', 'Server deleted successfully!');
    }

    public function ping(Server $server)
    {
        $isOnline = $server->ping();

        return response()->json([
            'success' => true,
            'status' => $server->status,
            'message' => $isOnline ? 'Server is online' : 'Server is offline',
            'last_ping' => $server->last_ping->format('Y-m-d H:i:s')
        ]);
    }

    public function updateMonitoring(Server $server)
    {
        $result = $server->updateMonitoringData();

        return response()->json([
            'success' => $result,
            'monitoring_data' => $server->fresh()->monitoring_data,
            'message' => $result ? 'Monitoring data updated successfully' : 'Failed to connect to server'
        ]);
    }

    /**
     * Test server connection
     */
    public function testConnection(Server $server)
    {
        $result = $server->connectAndUpdate();

        return response()->json($result);
    }

    /**
     * Restart a service on the server
     */
    public function restartService(Request $request, Server $server)
    {
        $request->validate([
            'service' => 'required|string|in:apache2,nginx,mysql,mariadb,postgresql,ssh,fail2ban'
        ]);

        $result = $server->restartService($request->service);

        return response()->json($result);
    }

    /**
     * Execute command on server
     */
    public function executeCommand(Request $request, Server $server)
    {
        $request->validate([
            'command' => 'required|string|max:500'
        ]);

        // Security check - only allow safe commands
        $allowedCommands = [
            'ls', 'pwd', 'whoami', 'date', 'uptime', 'df', 'free', 'ps',
            'systemctl status', 'systemctl restart', 'tail', 'cat', 'grep'
        ];

        $command = $request->command;
        $isAllowed = false;

        foreach ($allowedCommands as $allowedCmd) {
            if (str_starts_with($command, $allowedCmd)) {
                $isAllowed = true;
                break;
            }
        }

        if (!$isAllowed) {
            return response()->json([
                'success' => false,
                'message' => 'Command not allowed for security reasons'
            ], 403);
        }

        $result = $server->executeCommand($command);

        return response()->json($result);
    }

    /**
     * Get server logs
     */
    public function getLogs(Request $request, Server $server)
    {
        $request->validate([
            'type' => 'nullable|string|in:system,apache,nginx,mysql,auth',
            'lines' => 'nullable|integer|min:10|max:1000'
        ]);

        $logType = $request->get('type', 'system');
        $lines = $request->get('lines', 100);

        $result = $server->getLogs($logType, $lines);

        return response()->json($result);
    }

    public function bulkAction(Request $request)
    {
        $action = $request->action;
        $serverIds = $request->server_ids;

        if (empty($serverIds)) {
            return redirect()->back()->with('error', 'No servers selected.');
        }

        $servers = Server::whereIn('id', $serverIds);

        switch ($action) {
            case 'ping':
                foreach ($servers->get() as $server) {
                    $server->ping();
                }
                $message = 'Servers pinged successfully.';
                break;
                
            case 'update_monitoring':
                foreach ($servers->get() as $server) {
                    $server->updateMonitoringData();
                }
                $message = 'Monitoring data updated successfully.';
                break;
                
            case 'set_maintenance':
                $servers->update(['status' => 'maintenance']);
                $message = 'Servers set to maintenance mode.';
                break;
                
            case 'activate':
                $servers->update(['is_active' => true]);
                $message = 'Servers activated successfully.';
                break;
                
            case 'deactivate':
                $servers->update(['is_active' => false]);
                $message = 'Servers deactivated successfully.';
                break;
                
            case 'delete':
                $servers->delete();
                $message = 'Servers deleted successfully.';
                break;
                
            default:
                return redirect()->back()->with('error', 'Invalid action.');
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Test server connection from form data
     */
    public function testConnectionForm(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'hostname' => 'required|string',
                'ip_address' => 'required|ip',
                'port' => 'required|integer|min:1|max:65535',
                'username' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed: ' . $validator->errors()->first()
                ], 400);
            }

            // Check if we have authentication method
            if (empty($request->password) && empty($request->ssh_key)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Either password or SSH key is required'
                ], 400);
            }

            $ssh = new SSHService();
            $connected = $ssh->connect(
                $request->ip_address,
                $request->port,
                $request->username,
                $request->password,
                $request->ssh_key
            );

            if ($connected) {
                // Get basic system info if connection successful
                $systemInfo = $ssh->getSystemInfo();

                return response()->json([
                    'success' => true,
                    'message' => 'Connection successful! Server is reachable.',
                    'system_info' => [
                        'os' => $systemInfo['os'] ?? 'Unknown',
                        'uptime' => $systemInfo['uptime'] ?? 'Unknown',
                        'cpu_cores' => $systemInfo['cpu']['cores'] ?? 'Unknown',
                        'memory_total' => $systemInfo['memory']['total'] ?? 'Unknown'
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Connection failed. Please check your credentials.'
                ], 400);
            }
        } catch (\Exception $e) {
            Log::error('Server connection test failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Connection error: ' . $e->getMessage()
            ], 500);
        }
    }
}
