<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Domain;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class DomainController extends Controller
{
    /**
     * Display a listing of domains
     */
    public function index(Request $request)
    {
        try {
            $query = Domain::with('user');

            // Apply filters
            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('registrar', 'like', "%{$search}%");
                });
            }

            if ($request->filled('status')) {
                $query->where('status', $request->get('status'));
            }

            if ($request->filled('ssl_enabled')) {
                $query->where('ssl_enabled', $request->get('ssl_enabled') === 'true');
            }

            // Sort
            $sortBy = $request->get('sort_by', 'created_at');
            $sortDirection = $request->get('sort_direction', 'desc');
            $query->orderBy($sortBy, $sortDirection);

            $domains = $query->paginate(15);

            // Get statistics
            $stats = $this->getDomainStats();

            return view('admin.domains.index', compact('domains', 'stats'));
        } catch (\Exception $e) {
            return back()->with('error', 'Error loading domains: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new domain
     */
    public function create()
    {
        $users = User::orderBy('name')->get();
        return view('admin.domains.create', compact('users'));
    }

    /**
     * Store a newly created domain
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => ['required', 'exists:users,id'],
            'name' => ['required', 'string', 'max:255', 'unique:domains'],
            'status' => ['required', 'in:active,inactive,suspended'],
            'registrar' => ['nullable', 'string', 'max:255'],
            'registered_at' => ['nullable', 'date'],
            'expires_at' => ['nullable', 'date', 'after:registered_at'],
            'auto_renew' => ['boolean'],
            'nameserver1' => ['nullable', 'string', 'max:255'],
            'nameserver2' => ['nullable', 'string', 'max:255'],
            'nameserver3' => ['nullable', 'string', 'max:255'],
            'nameserver4' => ['nullable', 'string', 'max:255'],
            'dns_managed' => ['boolean'],
            'document_root' => ['nullable', 'string', 'max:500'],
            'php_version' => ['nullable', 'string', 'max:10'],
            'ssl_enabled' => ['boolean'],
            'ssl_provider' => ['nullable', 'string', 'max:255'],
            'notes' => ['nullable', 'string', 'max:1000'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $domain = Domain::create($request->all());

        return redirect()->route('admin.domains.index')
            ->with('success', 'Domain created successfully.');
    }

    /**
     * Display the specified domain
     */
    public function show(Domain $domain)
    {
        $domain->load('user', 'subdomains', 'dnsRecords', 'sslCertificates');
        return view('admin.domains.show', compact('domain'));
    }

    /**
     * Show the form for editing the specified domain
     */
    public function edit(Domain $domain)
    {
        $users = User::orderBy('name')->get();
        return view('admin.domains.edit', compact('domain', 'users'));
    }

    /**
     * Update the specified domain
     */
    public function update(Request $request, Domain $domain)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => ['required', 'exists:users,id'],
            'name' => ['required', 'string', 'max:255', 'unique:domains,name,' . $domain->id],
            'status' => ['required', 'in:active,inactive,suspended'],
            'registrar' => ['nullable', 'string', 'max:255'],
            'registered_at' => ['nullable', 'date'],
            'expires_at' => ['nullable', 'date', 'after:registered_at'],
            'auto_renew' => ['boolean'],
            'nameserver1' => ['nullable', 'string', 'max:255'],
            'nameserver2' => ['nullable', 'string', 'max:255'],
            'nameserver3' => ['nullable', 'string', 'max:255'],
            'nameserver4' => ['nullable', 'string', 'max:255'],
            'dns_managed' => ['boolean'],
            'document_root' => ['nullable', 'string', 'max:500'],
            'php_version' => ['nullable', 'string', 'max:10'],
            'ssl_enabled' => ['boolean'],
            'ssl_provider' => ['nullable', 'string', 'max:255'],
            'notes' => ['nullable', 'string', 'max:1000'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $domain->update($request->all());

        return redirect()->route('admin.domains.index')
            ->with('success', 'Domain updated successfully.');
    }

    /**
     * Remove the specified domain
     */
    public function destroy(Domain $domain)
    {
        $domain->delete();

        return redirect()->route('admin.domains.index')
            ->with('success', 'Domain deleted successfully.');
    }

    /**
     * Bulk actions for domains
     */
    public function bulkAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => ['required', 'in:delete,activate,deactivate,enable_ssl,disable_ssl'],
            'domains' => ['required', 'array'],
            'domains.*' => ['exists:domains,id'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $domains = Domain::whereIn('id', $request->domains);

        switch ($request->action) {
            case 'delete':
                $count = $domains->count();
                $domains->delete();
                return back()->with('success', "{$count} domains deleted successfully.");

            case 'activate':
                $domains->update(['status' => 'active']);
                return back()->with('success', 'Selected domains activated successfully.');

            case 'deactivate':
                $domains->update(['status' => 'inactive']);
                return back()->with('success', 'Selected domains deactivated successfully.');

            case 'enable_ssl':
                $domains->update(['ssl_enabled' => true]);
                return back()->with('success', 'SSL enabled for selected domains.');

            case 'disable_ssl':
                $domains->update(['ssl_enabled' => false]);
                return back()->with('success', 'SSL disabled for selected domains.');

            default:
                return back()->with('error', 'Invalid action.');
        }
    }

    /**
     * Get domain statistics
     */
    private function getDomainStats()
    {
        return [
            'total_domains' => Domain::count(),
            'active_domains' => Domain::where('status', 'active')->count(),
            'inactive_domains' => Domain::where('status', 'inactive')->count(),
            'suspended_domains' => Domain::where('status', 'suspended')->count(),
            'ssl_enabled' => Domain::where('ssl_enabled', true)->count(),
            'ssl_disabled' => Domain::where('ssl_enabled', false)->count(),
            'expiring_soon' => Domain::where('expires_at', '<=', now()->addDays(30))->count(),
            'expired' => Domain::where('expires_at', '<', now())->count(),
            'domains_today' => Domain::whereDate('created_at', today())->count(),
            'domains_this_month' => Domain::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
        ];
    }

    /**
     * Export domains
     */
    public function export(Request $request)
    {
        $domains = Domain::with('user')->get();

        $filename = 'domains_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($domains) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'ID', 'Domain Name', 'Owner', 'Status', 'Registrar', 
                'Registered Date', 'Expiry Date', 'Auto Renew', 'SSL Enabled',
                'PHP Version', 'Created Date'
            ]);

            // CSV data
            foreach ($domains as $domain) {
                fputcsv($file, [
                    $domain->id,
                    $domain->name,
                    $domain->user->name ?? 'N/A',
                    $domain->status,
                    $domain->registrar ?? 'N/A',
                    $domain->registered_at ? $domain->registered_at->format('Y-m-d') : 'N/A',
                    $domain->expires_at ? $domain->expires_at->format('Y-m-d') : 'N/A',
                    $domain->auto_renew ? 'Yes' : 'No',
                    $domain->ssl_enabled ? 'Yes' : 'No',
                    $domain->php_version ?? 'N/A',
                    $domain->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
