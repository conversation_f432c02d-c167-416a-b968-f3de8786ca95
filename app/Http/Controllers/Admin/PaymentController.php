<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PaymentMethod;
use App\Models\Transaction;
use App\Models\PaymentGateway;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Display payment methods
     */
    public function paymentMethods()
    {
        $user = auth()->user();
        $paymentMethods = $user->paymentMethods()->active()->get();
        $availableGateways = PaymentGateway::active()->ordered()->get();

        return view('admin.payments.methods', compact('paymentMethods', 'availableGateways'));
    }

    /**
     * Show add payment method form
     */
    public function createPaymentMethod()
    {
        $availableGateways = PaymentGateway::active()->ordered()->get();
        
        return view('admin.payments.create-method', compact('availableGateways'));
    }

    /**
     * Store new payment method
     */
    public function storePaymentMethod(Request $request)
    {
        $request->validate([
            'gateway' => 'required|string|exists:payment_gateways,slug',
            'type' => 'required|string|in:credit_card,paypal,bank_account',
            'cardholder_name' => 'required_if:type,credit_card|string|max:255',
            'card_number' => 'required_if:type,credit_card|string|min:13|max:19',
            'exp_month' => 'required_if:type,credit_card|string|size:2',
            'exp_year' => 'required_if:type,credit_card|string|size:4',
            'cvv' => 'required_if:type,credit_card|string|size:3',
            'paypal_email' => 'required_if:type,paypal|email',
            'bank_name' => 'required_if:type,bank_account|string|max:255',
            'account_number' => 'required_if:type,bank_account|string|max:20',
            'routing_number' => 'required_if:type,bank_account|string|max:20',
        ]);

        try {
            $user = auth()->user();
            
            // Prepare payment data based on type
            $paymentData = [
                'type' => $request->type,
            ];

            if ($request->type === 'credit_card') {
                $paymentData = array_merge($paymentData, [
                    'cardholder_name' => $request->cardholder_name,
                    'card_number' => $request->card_number,
                    'exp_month' => $request->exp_month,
                    'exp_year' => $request->exp_year,
                    'cvv' => $request->cvv,
                    'last_four' => substr($request->card_number, -4),
                    'brand' => $this->detectCardBrand($request->card_number),
                ]);
            } elseif ($request->type === 'paypal') {
                $paymentData['paypal_email'] = $request->paypal_email;
            } elseif ($request->type === 'bank_account') {
                $paymentData = array_merge($paymentData, [
                    'bank_name' => $request->bank_name,
                    'account_number' => $request->account_number,
                    'routing_number' => $request->routing_number,
                    'account_last_four' => substr($request->account_number, -4),
                ]);
            }

            $result = $this->paymentService->addPaymentMethod($user, $paymentData, $request->gateway);

            if ($result['success']) {
                return redirect()->route('admin.payments.methods')
                    ->with('success', 'Payment method added successfully!');
            } else {
                return back()->withInput()
                    ->with('error', 'Failed to add payment method: ' . $result['error']);
            }

        } catch (\Exception $e) {
            Log::error('Payment method creation failed', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->withInput()
                ->with('error', 'An error occurred while adding the payment method.');
        }
    }

    /**
     * Set payment method as default
     */
    public function setDefaultPaymentMethod(PaymentMethod $paymentMethod)
    {
        if ($paymentMethod->user_id !== auth()->id()) {
            abort(403);
        }

        $paymentMethod->setAsDefault();

        return back()->with('success', 'Default payment method updated successfully!');
    }

    /**
     * Delete payment method
     */
    public function deletePaymentMethod(PaymentMethod $paymentMethod)
    {
        if ($paymentMethod->user_id !== auth()->id()) {
            abort(403);
        }

        $result = $this->paymentService->removePaymentMethod($paymentMethod);

        if ($result['success']) {
            return back()->with('success', 'Payment method removed successfully!');
        } else {
            return back()->with('error', 'Failed to remove payment method: ' . $result['error']);
        }
    }

    /**
     * Display transactions
     */
    public function transactions(Request $request)
    {
        $user = auth()->user();
        
        $query = $user->transactions()->with(['subscription', 'invoice', 'paymentMethod']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('gateway')) {
            $query->where('gateway', $request->gateway);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $transactions = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get filter options
        $statuses = ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'];
        $types = ['payment', 'refund', 'chargeback', 'adjustment'];
        $gateways = PaymentGateway::active()->pluck('name', 'slug');

        return view('admin.payments.transactions', compact(
            'transactions', 
            'statuses', 
            'types', 
            'gateways'
        ));
    }

    /**
     * Show transaction details
     */
    public function showTransaction(Transaction $transaction)
    {
        if ($transaction->user_id !== auth()->id()) {
            abort(403);
        }

        $transaction->load(['subscription', 'invoice', 'paymentMethod']);

        return view('admin.payments.transaction-details', compact('transaction'));
    }

    /**
     * Process refund
     */
    public function processRefund(Request $request, Transaction $transaction)
    {
        if ($transaction->user_id !== auth()->id()) {
            abort(403);
        }

        $request->validate([
            'amount' => 'required|numeric|min:0.01|max:' . $transaction->amount,
            'reason' => 'required|string|max:500',
        ]);

        try {
            $result = $this->paymentService->processRefund($transaction, $request->amount);

            if ($result['success']) {
                return back()->with('success', 'Refund processed successfully!');
            } else {
                return back()->with('error', 'Failed to process refund: ' . $result['error']);
            }

        } catch (\Exception $e) {
            Log::error('Refund processing failed', [
                'transaction_id' => $transaction->transaction_id,
                'amount' => $request->amount,
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'An error occurred while processing the refund.');
        }
    }

    /**
     * Make one-time payment
     */
    public function makePayment(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'payment_method_id' => 'required|exists:payment_methods,id',
            'description' => 'required|string|max:255',
        ]);

        try {
            $user = auth()->user();
            $paymentMethod = PaymentMethod::where('id', $request->payment_method_id)
                ->where('user_id', $user->id)
                ->firstOrFail();

            $result = $this->paymentService->processOneTimePayment(
                $user,
                $request->amount,
                $paymentMethod,
                [
                    'description' => $request->description,
                    'currency' => 'USD',
                ]
            );

            if ($result['success']) {
                return redirect()->route('admin.payments.transactions')
                    ->with('success', 'Payment processed successfully!');
            } else {
                return back()->withInput()
                    ->with('error', 'Payment failed: ' . $result['error']);
            }

        } catch (\Exception $e) {
            Log::error('One-time payment failed', [
                'user_id' => auth()->id(),
                'amount' => $request->amount,
                'error' => $e->getMessage()
            ]);

            return back()->withInput()
                ->with('error', 'An error occurred while processing the payment.');
        }
    }

    /**
     * Detect card brand from card number
     */
    protected function detectCardBrand(string $cardNumber): string
    {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);

        if (preg_match('/^4/', $cardNumber)) {
            return 'visa';
        } elseif (preg_match('/^5[1-5]/', $cardNumber)) {
            return 'mastercard';
        } elseif (preg_match('/^3[47]/', $cardNumber)) {
            return 'amex';
        } elseif (preg_match('/^6(?:011|5)/', $cardNumber)) {
            return 'discover';
        }

        return 'unknown';
    }
}
