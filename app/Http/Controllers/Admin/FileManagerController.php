<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\File;
use App\Models\Domain;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use ZipArchive;

class FileManagerController extends Controller
{
    /**
     * Display the file manager
     */
    public function index(Request $request)
    {
        $currentPath = $request->get('path', '/');
        $domainId = $request->get('domain_id');
        $userId = $request->get('user_id');

        // Get domains for filter
        $domains = Domain::orderBy('name')->get();
        $users = User::orderBy('name')->get();

        // Build query
        $query = File::with(['user', 'domain']);

        // Apply filters
        if ($domainId) {
            $query->where('domain_id', $domainId);
        }

        if ($userId) {
            $query->where('user_id', $userId);
        }

        // Get files in current path
        $files = $query->where('relative_path', $currentPath)
                      ->orderBy('type', 'desc') // Directories first
                      ->orderBy('name')
                      ->get();

        // Get breadcrumb
        $breadcrumb = $this->getBreadcrumb($currentPath);

        // Get statistics
        $stats = $this->getFileStats($domainId, $userId);

        return view('admin.files.index', compact(
            'files', 'currentPath', 'breadcrumb', 'domains', 'users', 
            'domainId', 'userId', 'stats'
        ));
    }

    /**
     * Upload files
     */
    public function upload(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'files.*' => ['required', 'file', 'max:102400'], // 100MB max
            'path' => ['required', 'string'],
            'domain_id' => ['nullable', 'exists:domains,id'],
            'user_id' => ['required', 'exists:users,id'],
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()->first()], 422);
        }

        $uploadedFiles = [];
        $path = $request->get('path', '/');
        $domainId = $request->get('domain_id');
        $userId = $request->get('user_id');

        foreach ($request->file('files') as $uploadedFile) {
            try {
                // Generate unique filename if file exists
                $filename = $uploadedFile->getClientOriginalName();
                $targetPath = $this->getFullPath($path, $filename, $domainId);
                
                if (file_exists($targetPath)) {
                    $filename = $this->generateUniqueFilename($path, $filename, $domainId);
                    $targetPath = $this->getFullPath($path, $filename, $domainId);
                }

                // Move file
                $uploadedFile->move(dirname($targetPath), basename($targetPath));

                // Create file record
                $file = File::create([
                    'user_id' => $userId,
                    'domain_id' => $domainId,
                    'name' => $filename,
                    'path' => $targetPath,
                    'relative_path' => $path,
                    'type' => 'file',
                    'mime_type' => $uploadedFile->getMimeType(),
                    'size_bytes' => filesize($targetPath),
                    'permissions' => '0644',
                    'hash' => hash_file('md5', $targetPath),
                    'last_modified_at' => now(),
                ]);

                $uploadedFiles[] = $file;
            } catch (\Exception $e) {
                return response()->json(['error' => 'Failed to upload ' . $uploadedFile->getClientOriginalName()], 500);
            }
        }

        return response()->json([
            'success' => true,
            'message' => count($uploadedFiles) . ' file(s) uploaded successfully',
            'files' => $uploadedFiles
        ]);
    }

    /**
     * Create new folder
     */
    public function createFolder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z0-9_\-\s\.]+$/'],
            'path' => ['required', 'string'],
            'domain_id' => ['nullable', 'exists:domains,id'],
            'user_id' => ['required', 'exists:users,id'],
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()->first()], 422);
        }

        $name = $request->get('name');
        $path = $request->get('path', '/');
        $domainId = $request->get('domain_id');
        $userId = $request->get('user_id');

        $fullPath = $this->getFullPath($path, $name, $domainId);

        if (file_exists($fullPath)) {
            return response()->json(['error' => 'Folder already exists'], 422);
        }

        try {
            mkdir($fullPath, 0755, true);

            $file = File::create([
                'user_id' => $userId,
                'domain_id' => $domainId,
                'name' => $name,
                'path' => $fullPath,
                'relative_path' => $path,
                'type' => 'directory',
                'size_bytes' => 0,
                'permissions' => '0755',
                'last_modified_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Folder created successfully',
                'file' => $file
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to create folder'], 500);
        }
    }

    /**
     * Delete files/folders
     */
    public function delete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'files' => ['required', 'array'],
            'files.*' => ['exists:files,id'],
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()->first()], 422);
        }

        $files = File::whereIn('id', $request->get('files'))->get();
        $deletedCount = 0;

        foreach ($files as $file) {
            try {
                if ($file->type === 'directory') {
                    $this->deleteDirectory($file->path);
                } else {
                    unlink($file->path);
                }

                $file->delete();
                $deletedCount++;
            } catch (\Exception $e) {
                // Continue with other files
            }
        }

        return response()->json([
            'success' => true,
            'message' => $deletedCount . ' item(s) deleted successfully'
        ]);
    }

    /**
     * Rename file/folder
     */
    public function rename(Request $request, File $file)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z0-9_\-\s\.]+$/'],
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()->first()], 422);
        }

        $newName = $request->get('name');
        $newPath = dirname($file->path) . '/' . $newName;

        if (file_exists($newPath)) {
            return response()->json(['error' => 'File with this name already exists'], 422);
        }

        try {
            rename($file->path, $newPath);

            $file->update([
                'name' => $newName,
                'path' => $newPath,
                'last_modified_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Item renamed successfully',
                'file' => $file
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to rename item'], 500);
        }
    }

    /**
     * Download file
     */
    public function download(File $file)
    {
        if ($file->type !== 'file' || !file_exists($file->path)) {
            abort(404);
        }

        return response()->download($file->path, $file->name);
    }

    /**
     * Get file content for editing
     */
    public function getContent(File $file)
    {
        if (!$file->isEditable()) {
            return response()->json(['error' => 'File is not editable'], 422);
        }

        $content = $file->getContent();

        if ($content === null) {
            return response()->json(['error' => 'Unable to read file content'], 500);
        }

        return response()->json([
            'success' => true,
            'content' => $content,
            'file' => $file
        ]);
    }

    /**
     * Update file content
     */
    public function updateContent(Request $request, File $file)
    {
        $validator = Validator::make($request->all(), [
            'content' => ['required', 'string'],
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()->first()], 422);
        }

        if (!$file->isEditable()) {
            return response()->json(['error' => 'File is not editable'], 422);
        }

        $success = $file->updateContent($request->get('content'));

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'File updated successfully',
                'file' => $file->fresh()
            ]);
        }

        return response()->json(['error' => 'Failed to update file'], 500);
    }

    /**
     * Get breadcrumb for current path
     */
    private function getBreadcrumb(string $path): array
    {
        $breadcrumb = [['name' => 'Root', 'path' => '/']];
        
        if ($path !== '/') {
            $parts = explode('/', trim($path, '/'));
            $currentPath = '';
            
            foreach ($parts as $part) {
                if (!empty($part)) {
                    $currentPath .= '/' . $part;
                    $breadcrumb[] = ['name' => $part, 'path' => $currentPath];
                }
            }
        }

        return $breadcrumb;
    }

    /**
     * Get full file system path
     */
    private function getFullPath(string $relativePath, string $filename, ?int $domainId): string
    {
        $basePath = storage_path('app/files');
        
        if ($domainId) {
            $domain = Domain::find($domainId);
            $basePath .= '/domains/' . $domain->name;
        } else {
            $basePath .= '/system';
        }

        return $basePath . rtrim($relativePath, '/') . '/' . $filename;
    }

    /**
     * Generate unique filename
     */
    private function generateUniqueFilename(string $path, string $filename, ?int $domainId): string
    {
        $info = pathinfo($filename);
        $name = $info['filename'];
        $extension = isset($info['extension']) ? '.' . $info['extension'] : '';
        
        $counter = 1;
        do {
            $newFilename = $name . '_' . $counter . $extension;
            $fullPath = $this->getFullPath($path, $newFilename, $domainId);
            $counter++;
        } while (file_exists($fullPath));

        return $newFilename;
    }

    /**
     * Delete directory recursively
     */
    private function deleteDirectory(string $path): bool
    {
        if (!is_dir($path)) {
            return false;
        }

        $files = array_diff(scandir($path), ['.', '..']);
        
        foreach ($files as $file) {
            $filePath = $path . '/' . $file;
            if (is_dir($filePath)) {
                $this->deleteDirectory($filePath);
            } else {
                unlink($filePath);
            }
        }

        return rmdir($path);
    }

    /**
     * Get file statistics
     */
    private function getFileStats(?int $domainId = null, ?int $userId = null): array
    {
        $query = File::query();

        if ($domainId) {
            $query->where('domain_id', $domainId);
        }

        if ($userId) {
            $query->where('user_id', $userId);
        }

        return [
            'total_files' => $query->clone()->where('type', 'file')->count(),
            'total_directories' => $query->clone()->where('type', 'directory')->count(),
            'total_size' => $query->clone()->sum('size_bytes'),
            'images' => $query->clone()->where('mime_type', 'like', 'image/%')->count(),
            'documents' => $query->clone()->whereIn('mime_type', [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            ])->count(),
            'archives' => $query->clone()->whereIn('mime_type', [
                'application/zip',
                'application/x-rar-compressed',
                'application/x-7z-compressed'
            ])->count(),
        ];
    }
}
