<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DnsRecord;
use App\Models\Domain;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class DnsRecordController extends Controller
{
    public function index(Request $request, Domain $domain)
    {
        $query = $domain->dnsRecords();

        if ($request->filled('type')) {
            $query->where('type', $request->get('type'));
        }

        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('value', 'like', "%{$search}%");
            });
        }

        $records = $query->orderBy('type')->orderBy('name')->paginate(20);

        $stats = [
            'total_records' => $domain->dnsRecords()->count(),
            'active_records' => $domain->dnsRecords()->active()->count(),
            'system_records' => $domain->dnsRecords()->system()->count(),
            'custom_records' => $domain->dnsRecords()->custom()->count(),
        ];

        return view('admin.dns.index', compact('domain', 'records', 'stats'));
    }

    public function create(Domain $domain)
    {
        return view('admin.dns.create', compact('domain'));
    }

    public function store(Request $request, Domain $domain)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'type' => ['required', 'in:A,AAAA,CNAME,MX,TXT,NS,SRV,PTR,CAA'],
            'value' => ['required', 'string', 'max:1000'],
            'ttl' => ['required', 'integer', 'min:300', 'max:86400'],
            'priority' => ['nullable', 'integer', 'min:0', 'max:65535'],
            'weight' => ['nullable', 'integer', 'min:0', 'max:65535'],
            'port' => ['nullable', 'integer', 'min:1', 'max:65535'],
            'comment' => ['nullable', 'string', 'max:500'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $record = $domain->dnsRecords()->create([
            'name' => $request->name,
            'type' => $request->type,
            'value' => $request->value,
            'ttl' => $request->ttl,
            'priority' => $request->priority,
            'weight' => $request->weight,
            'port' => $request->port,
            'comment' => $request->comment,
            'is_active' => true,
            'is_system' => false,
        ]);

        return redirect()->route('admin.domains.dns.index', $domain)
            ->with('success', 'DNS record created successfully.');
    }

    public function edit(Domain $domain, DnsRecord $record)
    {
        if ($record->domain_id !== $domain->id) {
            abort(404);
        }

        return view('admin.dns.edit', compact('domain', 'record'));
    }

    public function update(Request $request, Domain $domain, DnsRecord $record)
    {
        if ($record->domain_id !== $domain->id) {
            abort(404);
        }

        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'type' => ['required', 'in:A,AAAA,CNAME,MX,TXT,NS,SRV,PTR,CAA'],
            'value' => ['required', 'string', 'max:1000'],
            'ttl' => ['required', 'integer', 'min:300', 'max:86400'],
            'priority' => ['nullable', 'integer', 'min:0', 'max:65535'],
            'weight' => ['nullable', 'integer', 'min:0', 'max:65535'],
            'port' => ['nullable', 'integer', 'min:1', 'max:65535'],
            'comment' => ['nullable', 'string', 'max:500'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $record->update($request->only([
            'name', 'type', 'value', 'ttl', 'priority', 'weight', 'port', 'comment'
        ]));

        return redirect()->route('admin.domains.dns.index', $domain)
            ->with('success', 'DNS record updated successfully.');
    }

    public function destroy(Domain $domain, DnsRecord $record)
    {
        if ($record->domain_id !== $domain->id) {
            abort(404);
        }

        if ($record->is_system) {
            return back()->with('error', 'Cannot delete system DNS records.');
        }

        $record->delete();

        return redirect()->route('admin.domains.dns.index', $domain)
            ->with('success', 'DNS record deleted successfully.');
    }

    public function toggle(Domain $domain, DnsRecord $record)
    {
        if ($record->domain_id !== $domain->id) {
            abort(404);
        }

        $record->update(['is_active' => !$record->is_active]);

        $status = $record->is_active ? 'activated' : 'deactivated';
        return back()->with('success', "DNS record {$status} successfully.");
    }

    public function checkPropagation(Domain $domain, DnsRecord $record)
    {
        if ($record->domain_id !== $domain->id) {
            abort(404);
        }

        $propagated = $record->checkPropagation();

        $message = $propagated 
            ? 'DNS record is properly propagated.' 
            : 'DNS record propagation is still pending.';

        return back()->with($propagated ? 'success' : 'warning', $message);
    }

    public function bulkAction(Request $request, Domain $domain)
    {
        $validator = Validator::make($request->all(), [
            'action' => ['required', 'in:delete,activate,deactivate,check_propagation'],
            'records' => ['required', 'array'],
            'records.*' => ['exists:dns_records,id'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $records = DnsRecord::whereIn('id', $request->records)
                           ->where('domain_id', $domain->id);

        switch ($request->action) {
            case 'delete':
                $count = $records->where('is_system', false)->count();
                $records->where('is_system', false)->delete();
                return back()->with('success', "{$count} DNS records deleted successfully.");

            case 'activate':
                $records->update(['is_active' => true]);
                return back()->with('success', 'Selected DNS records activated successfully.');

            case 'deactivate':
                $records->update(['is_active' => false]);
                return back()->with('success', 'Selected DNS records deactivated successfully.');

            case 'check_propagation':
                $records->get()->each->checkPropagation();
                return back()->with('success', 'DNS propagation check completed for selected records.');

            default:
                return back()->with('error', 'Invalid action.');
        }
    }
}
