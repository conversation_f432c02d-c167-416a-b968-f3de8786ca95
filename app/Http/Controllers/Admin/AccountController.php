<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class AccountController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        
        // Get user's login sessions (if you have a sessions table)
        $sessions = collect(); // Placeholder for now
        
        // Get user's activity log (if you have an activity log)
        $activities = collect(); // Placeholder for now
        
        // Account statistics
        $stats = [
            'total_logins' => 0, // Placeholder
            'last_login' => $user->last_login_at ?? now(),
            'account_created' => $user->created_at,
            'profile_completion' => $this->calculateProfileCompletion($user),
        ];
        
        return view('admin.account.index', compact('user', 'sessions', 'activities', 'stats'));
    }

    public function update(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'timezone' => ['required', 'string', 'max:255'],
            'language' => ['required', 'string', 'max:10'],
            'email_notifications' => ['boolean'],
            'sms_notifications' => ['boolean'],
            'marketing_emails' => ['boolean'],
            'security_alerts' => ['boolean'],
            'two_factor_enabled' => ['boolean'],
        ]);

        // Update account preferences
        $user->timezone = $request->timezone;
        $user->language = $request->language;
        $user->email_notifications = $request->boolean('email_notifications');
        $user->sms_notifications = $request->boolean('sms_notifications');
        $user->marketing_emails = $request->boolean('marketing_emails');
        $user->security_alerts = $request->boolean('security_alerts');

        $user->save();

        return back()->with('success', 'Account settings updated successfully!');
    }

    public function deleteAccount(Request $request)
    {
        $request->validate([
            'password' => ['required'],
            'confirmation' => ['required', 'in:DELETE'],
        ]);

        $user = Auth::user();

        if (!Hash::check($request->password, $user->password)) {
            return back()->withErrors(['password' => 'The password is incorrect.']);
        }

        // Log out the user
        Auth::logout();

        // Delete the user account
        $user->delete();

        return redirect('/')->with('success', 'Your account has been deleted successfully.');
    }

    private function calculateProfileCompletion($user)
    {
        $fields = ['name', 'email', 'phone', 'bio', 'location', 'avatar'];
        $completed = 0;

        foreach ($fields as $field) {
            if (!empty($user->$field)) {
                $completed++;
            }
        }

        return round(($completed / count($fields)) * 100);
    }
}
