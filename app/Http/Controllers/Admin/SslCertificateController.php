<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SslCertificate;
use App\Models\Domain;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SslCertificateController extends Controller
{
    public function index(Request $request, Domain $domain)
    {
        $query = $domain->sslCertificates();

        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('type')) {
            $query->where('type', $request->get('type'));
        }

        $certificates = $query->orderBy('created_at', 'desc')->paginate(15);

        $stats = [
            'total_certificates' => $domain->sslCertificates()->count(),
            'active_certificates' => $domain->sslCertificates()->active()->count(),
            'expired_certificates' => $domain->sslCertificates()->expired()->count(),
            'expiring_soon' => $domain->sslCertificates()->expiringSoon()->count(),
        ];

        return view('admin.ssl.index', compact('domain', 'certificates', 'stats'));
    }

    public function create(Domain $domain)
    {
        return view('admin.ssl.create', compact('domain'));
    }

    public function store(Request $request, Domain $domain)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'type' => ['required', 'in:letsencrypt,custom,self-signed,cloudflare'],
            'domains' => ['required', 'array'],
            'domains.*' => ['string', 'max:255'],
            'auto_renew' => ['boolean'],
            'days_before_renewal' => ['integer', 'min:1', 'max:90'],
            'certificate' => ['nullable', 'string'],
            'private_key' => ['nullable', 'string'],
            'certificate_chain' => ['nullable', 'string'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $certificate = $domain->sslCertificates()->create([
            'name' => $request->name,
            'type' => $request->type,
            'domains' => $request->domains,
            'auto_renew' => $request->boolean('auto_renew', true),
            'days_before_renewal' => $request->days_before_renewal ?? 30,
            'certificate' => $request->certificate,
            'private_key' => $request->private_key,
            'certificate_chain' => $request->certificate_chain,
            'status' => 'pending',
        ]);

        // If it's Let's Encrypt, start the automatic process
        if ($request->type === 'letsencrypt') {
            $this->requestLetsEncryptCertificate($certificate);
        } elseif ($request->certificate && $request->private_key) {
            // If custom certificate is provided, validate and activate
            if ($certificate->verifyCertificate()) {
                $certificate->update(['status' => 'active']);
            } else {
                $certificate->update(['status' => 'failed']);
                return back()->with('error', 'Invalid certificate or private key.');
            }
        }

        return redirect()->route('admin.domains.ssl.index', $domain)
            ->with('success', 'SSL certificate created successfully.');
    }

    public function show(Domain $domain, SslCertificate $certificate)
    {
        if ($certificate->domain_id !== $domain->id) {
            abort(404);
        }

        $certificate->load('renewals');
        
        return view('admin.ssl.show', compact('domain', 'certificate'));
    }

    public function renew(Domain $domain, SslCertificate $certificate)
    {
        if ($certificate->domain_id !== $domain->id) {
            abort(404);
        }

        if ($certificate->renew()) {
            return back()->with('success', 'SSL certificate renewal started successfully.');
        } else {
            return back()->with('error', 'Failed to start SSL certificate renewal.');
        }
    }

    public function install(Domain $domain, SslCertificate $certificate)
    {
        if ($certificate->domain_id !== $domain->id) {
            abort(404);
        }

        if ($certificate->install()) {
            return back()->with('success', 'SSL certificate installed successfully.');
        } else {
            return back()->with('error', 'Failed to install SSL certificate.');
        }
    }

    public function destroy(Domain $domain, SslCertificate $certificate)
    {
        if ($certificate->domain_id !== $domain->id) {
            abort(404);
        }

        $certificate->delete();

        return redirect()->route('admin.domains.ssl.index', $domain)
            ->with('success', 'SSL certificate deleted successfully.');
    }

    public function download(Domain $domain, SslCertificate $certificate, string $type)
    {
        if ($certificate->domain_id !== $domain->id) {
            abort(404);
        }

        $content = match($type) {
            'certificate' => $certificate->certificate,
            'private_key' => $certificate->decrypted_private_key,
            'chain' => $certificate->certificate_chain,
            default => null
        };

        if (!$content) {
            abort(404);
        }

        $filename = $certificate->name . '_' . $type . '.pem';

        return response($content)
            ->header('Content-Type', 'application/x-pem-file')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    private function requestLetsEncryptCertificate(SslCertificate $certificate): void
    {
        try {
            // Here you would implement the Let's Encrypt ACME protocol
            // This is a complex process that involves:
            // 1. Creating an account with Let's Encrypt
            // 2. Requesting a certificate for the domains
            // 3. Completing the domain validation challenge
            // 4. Downloading and installing the certificate

            // For now, we'll simulate the process
            $certificate->update([
                'status' => 'active',
                'issued_at' => now(),
                'expires_at' => now()->addDays(90),
                'issuer' => 'Let\'s Encrypt',
                'acme_challenge_type' => 'http-01',
            ]);
        } catch (\Exception $e) {
            $certificate->update([
                'status' => 'failed',
                'renewal_error' => $e->getMessage(),
            ]);
        }
    }

    public function bulkAction(Request $request, Domain $domain)
    {
        $validator = Validator::make($request->all(), [
            'action' => ['required', 'in:delete,renew,install'],
            'certificates' => ['required', 'array'],
            'certificates.*' => ['exists:ssl_certificates,id'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $certificates = SslCertificate::whereIn('id', $request->certificates)
                                   ->where('domain_id', $domain->id);

        switch ($request->action) {
            case 'delete':
                $count = $certificates->count();
                $certificates->delete();
                return back()->with('success', "{$count} SSL certificates deleted successfully.");

            case 'renew':
                $certificates->get()->each->renew();
                return back()->with('success', 'SSL certificate renewal started for selected certificates.');

            case 'install':
                $certificates->get()->each->install();
                return back()->with('success', 'SSL certificates installation started for selected certificates.');

            default:
                return back()->with('error', 'Invalid action.');
        }
    }
}
