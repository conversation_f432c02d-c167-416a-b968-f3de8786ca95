<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Database;
use App\Models\User;
use App\Models\Domain;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class DatabaseController extends Controller
{
    /**
     * Display a listing of databases
     */
    public function index(Request $request)
    {
        try {
            $query = Database::with(['user', 'domain']);

            // Apply filters
            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('type', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            if ($request->filled('type')) {
                $query->where('type', $request->get('type'));
            }

            if ($request->filled('status')) {
                $isActive = $request->get('status') === 'active';
                $query->where('is_active', $isActive);
            }

            if ($request->filled('user_id')) {
                $query->where('user_id', $request->get('user_id'));
            }

            // Sort
            $sortBy = $request->get('sort_by', 'created_at');
            $sortDirection = $request->get('sort_direction', 'desc');
            $query->orderBy($sortBy, $sortDirection);

            $databases = $query->paginate(15);

            // Get statistics
            $stats = $this->getDatabaseStats();

            // Get users for filter
            $users = User::orderBy('name')->get();

            return view('admin.databases.index', compact('databases', 'stats', 'users'));
        } catch (\Exception $e) {
            return back()->with('error', 'Error loading databases: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new database
     */
    public function create()
    {
        $users = User::orderBy('name')->get();
        $domains = Domain::orderBy('name')->get();
        return view('admin.databases.create', compact('users', 'domains'));
    }

    /**
     * Store a newly created database
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => ['required', 'exists:users,id'],
            'domain_id' => ['nullable', 'exists:domains,id'],
            'name' => ['required', 'string', 'max:64', 'unique:databases', 'regex:/^[a-zA-Z0-9_]+$/'],
            'type' => ['required', 'in:mysql,postgresql,mongodb'],
            'host' => ['required', 'string', 'max:255'],
            'port' => ['nullable', 'integer', 'min:1', 'max:65535'],
            'charset' => ['nullable', 'string', 'max:50'],
            'collation' => ['nullable', 'string', 'max:50'],
            'max_size_bytes' => ['nullable', 'integer', 'min:0'],
            'description' => ['nullable', 'string', 'max:1000'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();

        // Set default port based on database type
        if (!$data['port']) {
            $data['port'] = match($data['type']) {
                'mysql' => 3306,
                'postgresql' => 5432,
                'mongodb' => 27017,
                default => 3306
            };
        }

        // Set default charset and collation for MySQL
        if ($data['type'] === 'mysql') {
            $data['charset'] = $data['charset'] ?: 'utf8mb4';
            $data['collation'] = $data['collation'] ?: 'utf8mb4_unicode_ci';
        }

        // Convert max size from MB to bytes
        if ($request->filled('max_size_mb')) {
            $data['max_size_bytes'] = $request->get('max_size_mb') * 1024 * 1024;
        }

        try {
            $database = Database::create($data);

            // Get server for database creation
            $server = \App\Models\Server::where('is_active', true)->first();
            if (!$server) {
                $database->delete();
                return back()->with('error', 'No active server available for database creation')->withInput();
            }

            // Generate database password if not provided
            $dbPassword = $request->get('db_password') ?: \Str::random(16);

            // Create actual database on server
            $result = $database->createOnServer($server, $dbPassword);

            if ($result['success']) {
                return redirect()->route('admin.databases.index')
                    ->with('success', $result['message'] . ($result['database_user'] ? " Database user: {$result['database_user']}" : ''));
            } else {
                $database->delete();
                return back()->with('error', 'Failed to create database on server: ' . $result['message'])->withInput();
            }
        } catch (\Exception $e) {
            if (isset($database)) {
                $database->delete();
            }
            return back()->with('error', 'Error creating database: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Display the specified database
     */
    public function show(Database $database)
    {
        $database->load(['user', 'domain', 'databaseUsers']);
        return view('admin.databases.show', compact('database'));
    }

    /**
     * Show the form for editing the specified database
     */
    public function edit(Database $database)
    {
        $users = User::orderBy('name')->get();
        $domains = Domain::orderBy('name')->get();
        return view('admin.databases.edit', compact('database', 'users', 'domains'));
    }

    /**
     * Update the specified database
     */
    public function update(Request $request, Database $database)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => ['required', 'exists:users,id'],
            'domain_id' => ['nullable', 'exists:domains,id'],
            'name' => ['required', 'string', 'max:64', 'unique:databases,name,' . $database->id, 'regex:/^[a-zA-Z0-9_]+$/'],
            'type' => ['required', 'in:mysql,postgresql,mongodb'],
            'host' => ['required', 'string', 'max:255'],
            'port' => ['nullable', 'integer', 'min:1', 'max:65535'],
            'charset' => ['nullable', 'string', 'max:50'],
            'collation' => ['nullable', 'string', 'max:50'],
            'max_size_bytes' => ['nullable', 'integer', 'min:0'],
            'description' => ['nullable', 'string', 'max:1000'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();

        // Convert max size from MB to bytes
        if ($request->filled('max_size_mb')) {
            $data['max_size_bytes'] = $request->get('max_size_mb') * 1024 * 1024;
        }

        $database->update($data);

        return redirect()->route('admin.databases.index')
            ->with('success', 'Database updated successfully.');
    }

    /**
     * Remove the specified database
     */
    public function destroy(Database $database)
    {
        try {
            // Get server for database deletion
            $server = \App\Models\Server::where('is_active', true)->first();
            if ($server) {
                // Delete actual database from server
                $result = $database->deleteFromServer($server);
                if (!$result['success']) {
                    return back()->with('error', 'Failed to delete database from server: ' . $result['message']);
                }
            }

            // Delete database record
            $database->delete();

            return redirect()->route('admin.databases.index')
                ->with('success', 'Database deleted successfully from server and records.');
        } catch (\Exception $e) {
            return back()->with('error', 'Error deleting database: ' . $e->getMessage());
        }
    }

    /**
     * Bulk actions for databases
     */
    public function bulkAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => ['required', 'in:delete,activate,deactivate'],
            'databases' => ['required', 'array'],
            'databases.*' => ['exists:databases,id'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $databases = Database::whereIn('id', $request->databases);

        switch ($request->action) {
            case 'delete':
                $count = $databases->count();
                $databases->delete();
                return back()->with('success', "{$count} databases deleted successfully.");

            case 'activate':
                $databases->update(['is_active' => true]);
                return back()->with('success', 'Selected databases activated successfully.');

            case 'deactivate':
                $databases->update(['is_active' => false]);
                return back()->with('success', 'Selected databases deactivated successfully.');

            default:
                return back()->with('error', 'Invalid action.');
        }
    }

    /**
     * Get database statistics
     */
    private function getDatabaseStats()
    {
        return [
            'total_databases' => Database::count(),
            'active_databases' => Database::where('is_active', true)->count(),
            'inactive_databases' => Database::where('is_active', false)->count(),
            'mysql_databases' => Database::where('type', 'mysql')->count(),
            'postgresql_databases' => Database::where('type', 'postgresql')->count(),
            'mongodb_databases' => Database::where('type', 'mongodb')->count(),
            'total_size_mb' => round((Database::sum('size_bytes') ?: 0) / 1024 / 1024, 2),
            'databases_near_quota' => Database::whereRaw('size_bytes > (max_size_bytes * 0.8)')
                ->whereNotNull('max_size_bytes')
                ->whereNotNull('size_bytes')
                ->where('size_bytes', '>', 0)
                ->count(),
            'databases_today' => Database::whereDate('created_at', today())->count(),
            'databases_this_month' => Database::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
        ];
    }

    /**
     * Export databases
     */
    public function export(Request $request)
    {
        $databases = Database::with(['user', 'domain'])->get();

        $filename = 'databases_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($databases) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID', 'Database Name', 'Owner', 'Domain', 'Type', 'Host', 'Port',
                'Size (MB)', 'Max Size (MB)', 'Table Count', 'Status', 'Created Date'
            ]);

            // CSV data
            foreach ($databases as $database) {
                fputcsv($file, [
                    $database->id,
                    $database->name,
                    $database->user->name ?? 'N/A',
                    $database->domain->name ?? 'N/A',
                    $database->type,
                    $database->host,
                    $database->port,
                    $database->size_in_mb,
                    $database->max_size_bytes ? round($database->max_size_bytes / 1024 / 1024, 2) : 'N/A',
                    $database->table_count,
                    $database->is_active ? 'Active' : 'Inactive',
                    $database->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Update database size and table count
     */
    public function updateStats(Database $database)
    {
        try {
            // Update real database statistics
            $result = $database->updateRealStats();

            if ($result) {
                return back()->with('success', 'Database statistics updated successfully from server.');
            } else {
                // Fallback to simulated data if real connection fails
                $database->update([
                    'size_bytes' => rand(1024 * 1024, 1024 * 1024 * 500),
                    'table_count' => rand(1, 50),
                ]);
                return back()->with('warning', 'Database statistics updated with simulated data (server connection failed).');
            }
        } catch (\Exception $e) {
            return back()->with('error', 'Error updating database statistics: ' . $e->getMessage());
        }
    }

    /**
     * Create database backup
     */
    public function createBackup(Database $database)
    {
        try {
            $server = \App\Models\Server::where('is_active', true)->first();
            if (!$server) {
                return back()->with('error', 'No active server available for backup creation');
            }

            $result = $database->createBackup($server);

            if ($result['success']) {
                return back()->with('success', $result['message'] . " Backup file: {$result['backup_file']}");
            } else {
                return back()->with('error', 'Failed to create backup: ' . $result['message']);
            }
        } catch (\Exception $e) {
            return back()->with('error', 'Error creating backup: ' . $e->getMessage());
        }
    }

    /**
     * Restore database from backup
     */
    public function restoreBackup(Request $request, Database $database)
    {
        $validator = Validator::make($request->all(), [
            'backup_file' => 'required|string'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        try {
            $server = \App\Models\Server::where('is_active', true)->first();
            if (!$server) {
                return back()->with('error', 'No active server available for backup restoration');
            }

            $result = $database->restoreFromBackup($server, $request->backup_file);

            if ($result['success']) {
                return back()->with('success', $result['message']);
            } else {
                return back()->with('error', 'Failed to restore backup: ' . $result['message']);
            }
        } catch (\Exception $e) {
            return back()->with('error', 'Error restoring backup: ' . $e->getMessage());
        }
    }

    /**
     * Test database connection
     */
    public function testConnection(Database $database)
    {
        try {
            // Get database user credentials
            $dbUser = DatabaseUser::where('user_id', $database->user_id)->first();

            if (!$dbUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'No database user found for connection test'
                ]);
            }

            $canConnect = $database->testConnection($dbUser->username, 'test_password');

            return response()->json([
                'success' => $canConnect,
                'message' => $canConnect ? 'Database connection successful' : 'Database connection failed'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Connection test error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Create actual database in the database server
     */
    private function createActualDatabase(Database $database)
    {
        try {
            switch ($database->type) {
                case 'postgresql':
                    $this->createPostgreSQLDatabase($database);
                    break;
                case 'mysql':
                    $this->createMySQLDatabase($database);
                    break;
                case 'mongodb':
                    $this->createMongoDatabase($database);
                    break;
                default:
                    throw new \Exception("Unsupported database type: {$database->type}");
            }
        } catch (\Exception $e) {
            // If database creation fails, delete the record
            $database->delete();
            throw $e;
        }
    }

    /**
     * Create PostgreSQL database
     */
    private function createPostgreSQLDatabase(Database $database)
    {
        $connection = \DB::connection('pgsql');

        // Create database
        $connection->statement("CREATE DATABASE \"{$database->name}\"");

        \Log::info("PostgreSQL database created: {$database->name}");
    }

    /**
     * Create MySQL database
     */
    private function createMySQLDatabase(Database $database)
    {
        // For MySQL, we would need a separate MySQL connection
        // This is a placeholder implementation
        \Log::info("MySQL database creation requested: {$database->name}");

        // In a real implementation, you would:
        // 1. Connect to MySQL server
        // 2. Execute: CREATE DATABASE `{$database->name}` CHARACTER SET {$database->charset} COLLATE {$database->collation}

        throw new \Exception("MySQL database creation not implemented yet. Database record created but actual MySQL database needs manual creation.");
    }

    /**
     * Create MongoDB database
     */
    private function createMongoDatabase(Database $database)
    {
        // For MongoDB, databases are created automatically when first used
        \Log::info("MongoDB database creation requested: {$database->name}");

        // In a real implementation, you would:
        // 1. Connect to MongoDB
        // 2. Create a collection to initialize the database

        throw new \Exception("MongoDB database creation not implemented yet. Database record created but actual MongoDB database needs manual creation.");
    }
}
