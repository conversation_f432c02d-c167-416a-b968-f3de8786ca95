<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class ResellerController extends Controller
{
    /**
     * Display the reseller panel dashboard
     */
    public function index()
    {
        // Get reseller statistics
        $stats = $this->getResellerStats();
        
        // Get reseller accounts
        $resellers = $this->getResellers();
        
        // Get reseller packages
        $packages = $this->getResellerPackages();
        
        // Get recent activities
        $recentActivities = $this->getRecentActivities();
        
        // Get commission data
        $commissionData = $this->getCommissionData();

        return view('admin.reseller.index', compact(
            'stats',
            'resellers',
            'packages',
            'recentActivities',
            'commissionData'
        ));
    }

    /**
     * Get reseller statistics
     */
    private function getResellerStats()
    {
        return [
            'total_resellers' => 24,
            'active_resellers' => 18,
            'pending_resellers' => 3,
            'suspended_resellers' => 3,
            'total_clients' => 156,
            'total_revenue' => 12450.75,
            'monthly_revenue' => 3250.50,
            'commission_paid' => 2890.25,
            'pending_commission' => 485.75
        ];
    }

    /**
     * Get reseller accounts
     */
    private function getResellers()
    {
        return [
            [
                'id' => 1,
                'name' => 'Ahmed Hassan',
                'email' => '<EMAIL>',
                'company' => 'Hassan Web Solutions',
                'status' => 'active',
                'package' => 'Premium',
                'clients_count' => 15,
                'monthly_revenue' => 850.00,
                'commission_rate' => 25,
                'commission_earned' => 212.50,
                'joined_date' => now()->subMonths(8),
                'last_login' => now()->subHours(2),
                'total_sales' => 6800.00
            ],
            [
                'id' => 2,
                'name' => 'Sara Mohamed',
                'email' => '<EMAIL>',
                'company' => 'WebHost Pro',
                'status' => 'active',
                'package' => 'Business',
                'clients_count' => 22,
                'monthly_revenue' => 1200.00,
                'commission_rate' => 30,
                'commission_earned' => 360.00,
                'joined_date' => now()->subMonths(12),
                'last_login' => now()->subMinutes(30),
                'total_sales' => 14400.00
            ],
            [
                'id' => 3,
                'name' => 'Omar Ali',
                'email' => '<EMAIL>',
                'company' => 'Tech Solutions Ltd',
                'status' => 'pending',
                'package' => 'Starter',
                'clients_count' => 0,
                'monthly_revenue' => 0.00,
                'commission_rate' => 20,
                'commission_earned' => 0.00,
                'joined_date' => now()->subDays(5),
                'last_login' => now()->subDays(2),
                'total_sales' => 0.00
            ],
            [
                'id' => 4,
                'name' => 'Fatima Khalil',
                'email' => '<EMAIL>',
                'company' => 'Digital Agency Plus',
                'status' => 'active',
                'package' => 'Enterprise',
                'clients_count' => 35,
                'monthly_revenue' => 2100.00,
                'commission_rate' => 35,
                'commission_earned' => 735.00,
                'joined_date' => now()->subMonths(18),
                'last_login' => now()->subHours(1),
                'total_sales' => 37800.00
            ],
            [
                'id' => 5,
                'name' => 'Khaled Ibrahim',
                'email' => '<EMAIL>',
                'company' => 'HostMaster Services',
                'status' => 'suspended',
                'package' => 'Business',
                'clients_count' => 8,
                'monthly_revenue' => 0.00,
                'commission_rate' => 25,
                'commission_earned' => 0.00,
                'joined_date' => now()->subMonths(6),
                'last_login' => now()->subWeeks(2),
                'total_sales' => 3200.00
            ]
        ];
    }

    /**
     * Get reseller packages
     */
    private function getResellerPackages()
    {
        return [
            [
                'id' => 1,
                'name' => 'Starter',
                'price' => 29.99,
                'commission_rate' => 20,
                'max_clients' => 10,
                'disk_space' => '50 GB',
                'bandwidth' => '500 GB',
                'features' => ['Basic Support', 'White Label', 'Client Management'],
                'active_resellers' => 5
            ],
            [
                'id' => 2,
                'name' => 'Business',
                'price' => 59.99,
                'commission_rate' => 25,
                'max_clients' => 25,
                'disk_space' => '150 GB',
                'bandwidth' => '1.5 TB',
                'features' => ['Priority Support', 'White Label', 'Advanced Analytics', 'Custom Branding'],
                'active_resellers' => 12
            ],
            [
                'id' => 3,
                'name' => 'Premium',
                'price' => 99.99,
                'commission_rate' => 30,
                'max_clients' => 50,
                'disk_space' => '300 GB',
                'bandwidth' => '3 TB',
                'features' => ['24/7 Support', 'Full White Label', 'Advanced Tools', 'API Access'],
                'active_resellers' => 8
            ],
            [
                'id' => 4,
                'name' => 'Enterprise',
                'price' => 199.99,
                'commission_rate' => 35,
                'max_clients' => 100,
                'disk_space' => 'Unlimited',
                'bandwidth' => 'Unlimited',
                'features' => ['Dedicated Support', 'Complete Customization', 'Advanced API', 'Priority Processing'],
                'active_resellers' => 3
            ]
        ];
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities()
    {
        return [
            [
                'reseller' => 'Ahmed Hassan',
                'action' => 'New client registration',
                'details' => 'Client: example.com',
                'amount' => 45.00,
                'commission' => 11.25,
                'timestamp' => now()->subMinutes(15)
            ],
            [
                'reseller' => 'Sara Mohamed',
                'action' => 'Package upgrade',
                'details' => 'Client upgraded to Premium',
                'amount' => 89.99,
                'commission' => 26.99,
                'timestamp' => now()->subHours(2)
            ],
            [
                'reseller' => 'Fatima Khalil',
                'action' => 'Commission payout',
                'details' => 'Monthly commission paid',
                'amount' => 735.00,
                'commission' => 735.00,
                'timestamp' => now()->subHours(5)
            ],
            [
                'reseller' => 'Omar Ali',
                'action' => 'Account approved',
                'details' => 'Reseller account activated',
                'amount' => 0.00,
                'commission' => 0.00,
                'timestamp' => now()->subDays(1)
            ],
            [
                'reseller' => 'Ahmed Hassan',
                'action' => 'Client renewal',
                'details' => 'Annual hosting renewal',
                'amount' => 120.00,
                'commission' => 30.00,
                'timestamp' => now()->subDays(2)
            ]
        ];
    }

    /**
     * Get commission data
     */
    private function getCommissionData()
    {
        return [
            'total_earned' => 15750.25,
            'this_month' => 3250.50,
            'pending_payout' => 485.75,
            'next_payout_date' => now()->addDays(5),
            'average_commission_rate' => 27.5,
            'top_earner' => [
                'name' => 'Fatima Khalil',
                'earnings' => 735.00
            ]
        ];
    }

    /**
     * Approve reseller account
     */
    public function approve(Request $request)
    {
        $resellerId = $request->get('reseller_id');
        
        try {
            // Simulate approval process
            return response()->json([
                'status' => 'success',
                'message' => 'Reseller account approved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Suspend reseller account
     */
    public function suspend(Request $request)
    {
        $resellerId = $request->get('reseller_id');
        
        try {
            // Simulate suspension process
            return response()->json([
                'status' => 'success',
                'message' => 'Reseller account suspended successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process commission payout
     */
    public function processPayout(Request $request)
    {
        $resellerId = $request->get('reseller_id');
        
        try {
            // Simulate payout process
            return response()->json([
                'status' => 'success',
                'message' => 'Commission payout processed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get real-time reseller data
     */
    public function getRealTimeData(Request $request)
    {
        try {
            return response()->json([
                'stats' => $this->getResellerStats(),
                'recent_activities' => $this->getRecentActivities(),
                'commission_data' => $this->getCommissionData(),
                'timestamp' => now()->format('H:i:s')
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
