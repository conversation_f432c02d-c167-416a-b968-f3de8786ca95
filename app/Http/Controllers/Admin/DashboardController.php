<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Domain;
use App\Models\Database;
use App\Models\DatabaseUser;
use App\Services\SystemInfoService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Get system statistics
        $stats = $this->getSystemStats();

        // Get user statistics
        $userStats = $this->getUserStats();

        // Get recent users
        try {
            $recentUsers = User::latest()
                ->take(5)
                ->get();
        } catch (\Exception $e) {
            $recentUsers = collect(); // Empty collection as fallback
        }

        // Get real system information using SystemInfoService
        $systemInfoService = new SystemInfoService();
        $systemInfo = $systemInfoService->getAllInfo();

        // Get domain statistics
        $domainStats = $this->getDomainStats();

        // Get database statistics
        $databaseStats = $this->getDatabaseStats();

        return view('admin.dashboard', compact(
            'stats',
            'userStats',
            'recentUsers',
            'systemInfo',
            'domainStats',
            'databaseStats'
        ));
    }

    /**
     * Get system performance statistics
     */
    private function getSystemStats()
    {
        return [
            'cpu_usage' => $this->getCpuUsage(),
            'memory_usage' => $this->getMemoryUsage(),
            'disk_usage' => $this->getDiskUsage(),
            'server_load' => $this->getServerLoad(),
        ];
    }

    /**
     * Get user statistics
     */
    private function getUserStats()
    {
        try {
            $totalUsers = User::count();

            return [
                'total_users' => $totalUsers,
                'admin_users' => 1, // Fallback data
                'reseller_users' => 0,
                'client_users' => $totalUsers - 1,
                'verified_users' => User::whereNotNull('email_verified_at')->count(),
                'unverified_users' => User::whereNull('email_verified_at')->count(),
                'users_today' => User::whereDate('created_at', today())->count(),
                'users_this_week' => User::whereBetween('created_at', [
                    now()->startOfWeek(),
                    now()->endOfWeek()
                ])->count(),
                'users_this_month' => User::whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->count(),
            ];
        } catch (\Exception $e) {
            // Fallback data if database queries fail
            return [
                'total_users' => 1,
                'admin_users' => 1,
                'reseller_users' => 0,
                'client_users' => 0,
                'verified_users' => 1,
                'unverified_users' => 0,
                'users_today' => 0,
                'users_this_week' => 1,
                'users_this_month' => 1,
            ];
        }
    }

    /**
     * Get system information
     */
    private function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'operating_system' => PHP_OS,
            'server_time' => now()->format('Y-m-d H:i:s'),
            'timezone' => config('app.timezone'),
            'environment' => app()->environment(),
            'debug_mode' => config('app.debug'),
            'uptime' => $this->getSystemUptime(),
        ];
    }

    /**
     * Get CPU usage percentage
     */
    private function getCpuUsage()
    {
        // Simulate CPU usage for demo purposes
        // In production, you would use system commands or monitoring tools
        return [
            'percentage' => rand(5, 25),
            'cores' => 4,
            'load_average' => [
                '1min' => round(rand(10, 100) / 100, 2),
                '5min' => round(rand(10, 100) / 100, 2),
                '15min' => round(rand(10, 100) / 100, 2),
            ]
        ];
    }

    /**
     * Get memory usage information
     */
    private function getMemoryUsage()
    {
        $memoryLimit = ini_get('memory_limit');
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);

        // Convert to MB
        $memoryLimitMB = $this->convertToMB($memoryLimit);
        $memoryUsageMB = round($memoryUsage / 1024 / 1024, 2);
        $memoryPeakMB = round($memoryPeak / 1024 / 1024, 2);

        return [
            'used' => $memoryUsageMB,
            'peak' => $memoryPeakMB,
            'limit' => $memoryLimitMB,
            'percentage' => $memoryLimitMB > 0 ? round(($memoryUsageMB / $memoryLimitMB) * 100, 2) : 0,
            'available' => $memoryLimitMB - $memoryUsageMB,
        ];
    }

    /**
     * Get disk usage information
     */
    private function getDiskUsage()
    {
        $path = base_path();
        $totalBytes = disk_total_space($path);
        $freeBytes = disk_free_space($path);
        $usedBytes = $totalBytes - $freeBytes;

        return [
            'total' => round($totalBytes / 1024 / 1024 / 1024, 2), // GB
            'used' => round($usedBytes / 1024 / 1024 / 1024, 2), // GB
            'free' => round($freeBytes / 1024 / 1024 / 1024, 2), // GB
            'percentage' => round(($usedBytes / $totalBytes) * 100, 2),
        ];
    }

    /**
     * Get server load average
     */
    private function getServerLoad()
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1min' => round($load[0], 2),
                '5min' => round($load[1], 2),
                '15min' => round($load[2], 2),
            ];
        }

        // Fallback for systems that don't support sys_getloadavg
        return [
            '1min' => round(rand(10, 100) / 100, 2),
            '5min' => round(rand(10, 100) / 100, 2),
            '15min' => round(rand(10, 100) / 100, 2),
        ];
    }

    /**
     * Get system uptime
     */
    private function getSystemUptime()
    {
        // Try to get system uptime on Unix-like systems
        if (function_exists('shell_exec') && PHP_OS_FAMILY !== 'Windows') {
            $uptime = shell_exec('uptime -p 2>/dev/null');
            if ($uptime) {
                return trim($uptime);
            }
        }

        // Fallback - calculate from server start time
        $serverStartTime = $_SERVER['REQUEST_TIME'] ?? time();
        $uptime = time() - $serverStartTime;

        $days = floor($uptime / 86400);
        $hours = floor(($uptime % 86400) / 3600);
        $minutes = floor(($uptime % 3600) / 60);

        return sprintf('%d days, %d hours, %d minutes', $days, $hours, $minutes);
    }

    /**
     * Convert memory limit string to MB
     */
    private function convertToMB($memoryLimit)
    {
        if ($memoryLimit == -1) {
            return 0; // Unlimited
        }

        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) $memoryLimit;

        switch ($unit) {
            case 'g':
                return $value * 1024;
            case 'm':
                return $value;
            case 'k':
                return $value / 1024;
            default:
                return $value / 1024 / 1024; // Bytes to MB
        }
    }

    /**
     * Get domain statistics
     */
    private function getDomainStats()
    {
        try {
            return [
                'total_domains' => Domain::count(),
                'active_domains' => Domain::where('status', 'active')->count(),
                'inactive_domains' => Domain::where('status', 'inactive')->count(),
                'domains_today' => Domain::whereDate('created_at', today())->count(),
                'domains_this_month' => Domain::whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->count(),
            ];
        } catch (\Exception $e) {
            return [
                'total_domains' => 0,
                'active_domains' => 0,
                'inactive_domains' => 0,
                'domains_today' => 0,
                'domains_this_month' => 0,
            ];
        }
    }

    /**
     * Get database statistics
     */
    private function getDatabaseStats()
    {
        try {
            return [
                'total_databases' => Database::count(),
                'databases_today' => Database::whereDate('created_at', today())->count(),
                'databases_this_month' => Database::whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->count(),
                'total_db_users' => DatabaseUser::count(),
            ];
        } catch (\Exception $e) {
            return [
                'total_databases' => 0,
                'databases_today' => 0,
                'databases_this_month' => 0,
                'total_db_users' => 0,
            ];
        }
    }

    /**
     * Get real-time dashboard data via AJAX
     */
    public function getData(Request $request)
    {
        $systemInfoService = new SystemInfoService();

        return response()->json([
            'stats' => $this->getSystemStats(),
            'userStats' => $this->getUserStats(),
            'systemInfo' => $systemInfoService->getAllInfo(),
            'domainStats' => $this->getDomainStats(),
            'databaseStats' => $this->getDatabaseStats(),
            'timestamp' => now()->format('H:i:s'),
        ]);
    }
}
