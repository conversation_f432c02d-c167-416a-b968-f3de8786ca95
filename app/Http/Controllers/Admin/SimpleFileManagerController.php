<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\FileManagerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SimpleFileManagerController extends Controller
{
    private $fileManager;

    public function __construct()
    {
        $this->fileManager = new FileManagerService();
    }

    /**
     * Display file manager interface
     */
    public function index(Request $request)
    {
        $path = $request->get('path', '');
        $result = $this->fileManager->listDirectory($path);
        
        if (!$result['success']) {
            return redirect()->route('admin.simple-file-manager.index')
                ->with('error', $result['message']);
        }

        // Get directory tree for sidebar
        $directoryTree = $this->fileManager->getDirectoryTree();

        return view('admin.simple-file-manager.index', [
            'currentPath' => $path,
            'items' => $result['items'],
            'directoryTree' => $directoryTree,
            'breadcrumbs' => $this->getBreadcrumbs($path)
        ]);
    }

    /**
     * List directory contents (AJAX)
     */
    public function listDirectory(Request $request)
    {
        $path = $request->get('path', '');
        $result = $this->fileManager->listDirectory($path);
        
        return response()->json($result);
    }

    /**
     * Create new directory
     */
    public function createDirectory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
            'name' => 'required|string|max:255|regex:/^[a-zA-Z0-9_\-\s]+$/'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $result = $this->fileManager->createDirectory(
            $request->path,
            $request->name
        );

        return response()->json($result);
    }

    /**
     * Upload files
     */
    public function uploadFiles(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
            'files' => 'required|array',
            'files.*' => 'file|max:102400' // 100MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $results = [];
        $successCount = 0;
        $errorCount = 0;

        foreach ($request->file('files') as $file) {
            $result = $this->fileManager->uploadFile($file, $request->path);
            $results[] = $result;
            
            if ($result['success']) {
                $successCount++;
            } else {
                $errorCount++;
            }
        }

        return response()->json([
            'success' => $errorCount === 0,
            'message' => "{$successCount} files uploaded successfully" . 
                        ($errorCount > 0 ? ", {$errorCount} failed" : ""),
            'results' => $results,
            'success_count' => $successCount,
            'error_count' => $errorCount
        ]);
    }

    /**
     * Delete file or directory
     */
    public function delete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $result = $this->fileManager->delete($request->path);
        
        return response()->json($result);
    }

    /**
     * Show file editor
     */
    public function edit(Request $request)
    {
        $path = $request->get('path');

        if (!$path) {
            return redirect()->route('admin.simple-file-manager.index')
                ->with('error', 'No file specified for editing');
        }

        $result = $this->fileManager->getFileContent($path);

        if (!$result['success']) {
            return redirect()->route('admin.simple-file-manager.index')
                ->with('error', $result['message']);
        }

        // Check if file is editable (text-based)
        if (!$result['is_text']) {
            return redirect()->route('admin.simple-file-manager.index')
                ->with('error', 'This file type cannot be edited');
        }

        return view('admin.simple-file-manager.edit', [
            'path' => $path,
            'content' => $result['content'],
            'fileName' => basename($path),
            'fileSize' => $result['size_human'],
            'mimeType' => $result['mime_type']
        ]);
    }

    /**
     * Rename file or directory
     */
    public function rename(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
            'new_name' => 'required|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $result = $this->fileManager->rename(
            $request->path,
            $request->new_name
        );

        return response()->json($result);
    }

    /**
     * Get file content for editing
     */
    public function getFileContent(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $result = $this->fileManager->getFileContent($request->path);
        
        return response()->json($result);
    }

    /**
     * Save file content
     */
    public function saveFileContent(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
            'content' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $result = $this->fileManager->saveFileContent(
            $request->path,
            $request->content
        );

        return response()->json($result);
    }

    /**
     * Download file
     */
    public function downloadFile(Request $request)
    {
        $path = $request->get('path');
        
        if (!$path) {
            abort(404, 'File not found');
        }

        try {
            $fileManager = new FileManagerService();
            $fullPath = $fileManager->getFullPath($path);
            
            if (!file_exists($fullPath) || is_dir($fullPath)) {
                abort(404, 'File not found');
            }

            return response()->download($fullPath);

        } catch (\Exception $e) {
            abort(404, 'File not found');
        }
    }

    /**
     * Get breadcrumbs for current path
     */
    private function getBreadcrumbs($path)
    {
        $breadcrumbs = [
            ['name' => 'Home', 'path' => '']
        ];

        if (!empty($path)) {
            $parts = explode('/', trim($path, '/'));
            $currentPath = '';

            foreach ($parts as $part) {
                $currentPath .= '/' . $part;
                $breadcrumbs[] = [
                    'name' => $part,
                    'path' => trim($currentPath, '/')
                ];
            }
        }

        return $breadcrumbs;
    }
}
