<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\TwoFactorAuthService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class TwoFactorController extends Controller
{
    protected TwoFactorAuthService $twoFactorService;

    public function __construct(TwoFactorAuthService $twoFactorService)
    {
        $this->twoFactorService = $twoFactorService;
    }

    /**
     * Show 2FA settings page
     */
    public function index(): View
    {
        $user = auth()->user();
        $twoFactorAuth = $this->twoFactorService->getTwoFactorAuth($user);
        $isEnabled = $this->twoFactorService->isEnabled($user);
        
        $qrCode = null;
        $secretKey = null;
        $recoveryCodes = null;

        if (!$isEnabled) {
            $qrCode = $this->twoFactorService->generateQrCode($user);
            $secretKey = $this->twoFactorService->getSecretKey($user);
        } else {
            $recoveryCodes = $this->twoFactorService->getRecoveryCodes($user);
        }

        return view('admin.two-factor.index', compact(
            'twoFactorAuth',
            'isEnabled',
            'qrCode',
            'secretKey',
            'recoveryCodes'
        ));
    }

    /**
     * Enable 2FA
     */
    public function enable(Request $request): JsonResponse
    {
        $request->validate([
            'code' => 'required|string|min:6|max:8',
        ]);

        $user = auth()->user();
        
        if ($this->twoFactorService->enable($user, $request->code)) {
            $recoveryCodes = $this->twoFactorService->getRecoveryCodes($user);
            
            return response()->json([
                'success' => true,
                'message' => 'Two-factor authentication has been enabled successfully.',
                'recovery_codes' => $recoveryCodes
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Invalid verification code. Please try again.'
        ], 422);
    }

    /**
     * Disable 2FA
     */
    public function disable(Request $request): JsonResponse
    {
        $request->validate([
            'password' => 'required|string',
        ]);

        $user = auth()->user();
        
        if (!password_verify($request->password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid password. Please try again.'
            ], 422);
        }

        $this->twoFactorService->disable($user);

        return response()->json([
            'success' => true,
            'message' => 'Two-factor authentication has been disabled.'
        ]);
    }

    /**
     * Generate new QR code
     */
    public function generateQrCode(): JsonResponse
    {
        $user = auth()->user();
        $qrCode = $this->twoFactorService->generateQrCode($user);

        return response()->json([
            'success' => true,
            'qr_code' => $qrCode
        ]);
    }

    /**
     * Generate new recovery codes
     */
    public function generateRecoveryCodes(Request $request): JsonResponse
    {
        $request->validate([
            'password' => 'required|string',
        ]);

        $user = auth()->user();
        
        if (!password_verify($request->password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid password. Please try again.'
            ], 422);
        }

        $recoveryCodes = $this->twoFactorService->generateNewRecoveryCodes($user);

        return response()->json([
            'success' => true,
            'message' => 'New recovery codes have been generated.',
            'recovery_codes' => $recoveryCodes
        ]);
    }

    /**
     * Set backup method
     */
    public function setBackupMethod(Request $request): JsonResponse
    {
        $request->validate([
            'method' => 'required|in:email,sms',
            'contact' => 'required|string',
        ]);

        $user = auth()->user();
        
        // Validate contact based on method
        if ($request->method === 'email' && !filter_var($request->contact, FILTER_VALIDATE_EMAIL)) {
            return response()->json([
                'success' => false,
                'message' => 'Please provide a valid email address.'
            ], 422);
        }

        if ($request->method === 'sms' && !preg_match('/^\+?[1-9]\d{1,14}$/', $request->contact)) {
            return response()->json([
                'success' => false,
                'message' => 'Please provide a valid phone number.'
            ], 422);
        }

        $this->twoFactorService->setBackupMethod($user, $request->method, $request->contact);

        return response()->json([
            'success' => true,
            'message' => 'Backup method has been set successfully.'
        ]);
    }

    /**
     * Send backup code
     */
    public function sendBackupCode(): JsonResponse
    {
        $user = auth()->user();
        
        if ($this->twoFactorService->sendBackupCode($user)) {
            return response()->json([
                'success' => true,
                'message' => 'Backup code has been sent to your backup contact method.'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Unable to send backup code. Please ensure you have set up a backup method.'
        ], 422);
    }

    /**
     * Verify 2FA code (for login)
     */
    public function verify(Request $request): JsonResponse
    {
        $request->validate([
            'code' => 'required|string|min:6|max:8',
        ]);

        $user = auth()->user();
        
        if ($this->twoFactorService->verifyCode($user, $request->code)) {
            // Mark 2FA as verified for this session
            session(['2fa_verified' => true]);
            
            return response()->json([
                'success' => true,
                'message' => 'Two-factor authentication verified successfully.',
                'redirect' => route('admin.dashboard')
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Invalid verification code. Please try again.'
        ], 422);
    }

    /**
     * Show 2FA verification page (for login)
     */
    public function showVerification(): View
    {
        return view('admin.two-factor.verify');
    }

    /**
     * Verify backup code
     */
    public function verifyBackupCode(Request $request): JsonResponse
    {
        $request->validate([
            'code' => 'required|string|size:6',
        ]);

        $user = auth()->user();
        
        if ($this->twoFactorService->verifyBackupCode($user, $request->code)) {
            session(['2fa_verified' => true]);
            
            return response()->json([
                'success' => true,
                'message' => 'Backup code verified successfully.',
                'redirect' => route('admin.dashboard')
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Invalid backup code. Please try again.'
        ], 422);
    }
}
