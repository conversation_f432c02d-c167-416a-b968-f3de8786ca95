<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Backup;
use App\Models\Database;
use App\Models\Domain;
use App\Models\User;
use App\Jobs\ProcessBackupJob;
use App\Services\BackupService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class BackupController extends Controller
{
    /**
     * Display a listing of backups
     */
    public function index(Request $request)
    {
        try {
            $query = Backup::with(['user', 'backupable']);

            // Apply filters
            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('type', 'like', "%{$search}%");
                });
            }

            if ($request->filled('type')) {
                $query->where('type', $request->get('type'));
            }

            if ($request->filled('status')) {
                $query->where('status', $request->get('status'));
            }

            if ($request->filled('user_id')) {
                $query->where('user_id', $request->get('user_id'));
            }

            if ($request->filled('is_automatic')) {
                $isAutomatic = $request->get('is_automatic') === 'true';
                $query->where('is_automatic', $isAutomatic);
            }

            // Sort
            $sortBy = $request->get('sort_by', 'created_at');
            $sortDirection = $request->get('sort_direction', 'desc');
            $query->orderBy($sortBy, $sortDirection);

            $backups = $query->paginate(15);

            // Get statistics
            $stats = $this->getBackupStats();

            // Get users for filter
            $users = User::orderBy('name')->get();

            return view('admin.backups.index', compact('backups', 'stats', 'users'));
        } catch (\Exception $e) {
            return back()->with('error', 'Error loading backups: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new backup
     */
    public function create()
    {
        $users = User::orderBy('name')->get();
        $databases = Database::with('user')->orderBy('name')->get();
        $domains = Domain::with('user')->orderBy('name')->get();

        return view('admin.backups.create', compact('users', 'databases', 'domains'));
    }

    /**
     * Store a newly created backup
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'type' => ['required', 'in:database,files,full'],
            'backupable_type' => ['required_unless:type,full', 'string'],
            'backupable_id' => ['required_unless:type,full', 'integer'],
            'user_id' => ['required', 'exists:users,id'],
            'compression' => ['required', 'in:gzip,zip,none'],
            'encrypted' => ['boolean'],
            'expires_at' => ['nullable', 'date', 'after:today'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            // Handle backupable_type and backupable_id for full backups
            $backupableType = null;
            $backupableId = null;

            if ($request->type !== 'full') {
                $backupableType = $request->backupable_type;
                $backupableId = $request->backupable_id;
            }

            // Create backup record
            $backup = Backup::create([
                'user_id' => $request->user_id,
                'name' => $request->name,
                'type' => $request->type,
                'status' => 'pending',
                'backupable_type' => $backupableType,
                'backupable_id' => $backupableId,
                'storage_path' => $this->generateStoragePath($request->type, $request->name),
                'storage_driver' => 'backups',
                'compression' => $request->compression,
                'encrypted' => $request->boolean('encrypted'),
                'encryption_method' => $request->boolean('encrypted') ? 'AES-256' : null,
                'is_automatic' => false,
                'expires_at' => $request->expires_at ?
                    \Carbon\Carbon::parse($request->expires_at) :
                    Backup::calculateRetentionDate($request->type),
                'metadata' => [
                    'created_by' => auth()->user()->name,
                    'created_via' => 'manual',
                ],
            ]);

            // Dispatch backup job to queue
            ProcessBackupJob::dispatch($backup);

            Log::info("Backup job dispatched", [
                'backup_id' => $backup->id,
                'type' => $backup->type,
                'user_id' => $backup->user_id
            ]);

            return redirect()->route('admin.backups.index')
                ->with('success', 'Backup created and queued for processing. You will be notified when it completes.');
        } catch (\Exception $e) {
            return back()->with('error', 'Error creating backup: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Display the specified backup
     */
    public function show(Backup $backup)
    {
        $backup->load(['user', 'backupable']);
        return view('admin.backups.show', compact('backup'));
    }

    /**
     * Remove the specified backup
     */
    public function destroy(Backup $backup)
    {
        try {
            // Delete backup file from storage
            $backup->deleteFile();

            // Delete backup record
            $backup->delete();

            return redirect()->route('admin.backups.index')
                ->with('success', 'Backup deleted successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Error deleting backup: ' . $e->getMessage());
        }
    }

    /**
     * Download backup file
     */
    public function download(Backup $backup)
    {
        try {
            if (!$backup->isCompleted()) {
                return back()->with('error', 'Backup is not completed yet.');
            }

            $backupPath = storage_path('app/backups/' . $backup->storage_path);

            if (!file_exists($backupPath)) {
                return back()->with('error', 'Backup file not found on disk.');
            }

            $fileName = basename($backup->storage_path);

            // Log download activity
            Log::info("Backup download initiated", [
                'backup_id' => $backup->id,
                'user_id' => auth()->id(),
                'file_name' => $fileName
            ]);

            return response()->download($backupPath, $fileName, [
                'Content-Type' => 'application/octet-stream',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"'
            ]);
        } catch (\Exception $e) {
            Log::error("Backup download failed", [
                'backup_id' => $backup->id,
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'Error downloading backup: ' . $e->getMessage());
        }
    }

    /**
     * Restore backup
     */
    public function restore(Request $request, Backup $backup)
    {
        $validator = Validator::make($request->all(), [
            'confirm' => ['required', 'accepted'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        if (!$backup->isCompleted()) {
            return back()->with('error', 'Cannot restore incomplete backup.');
        }

        if (!$backup->fileExists()) {
            return back()->with('error', 'Backup file not found.');
        }

        try {
            // Log restore activity
            Log::info("Backup restore initiated", [
                'backup_id' => $backup->id,
                'user_id' => auth()->id(),
                'type' => $backup->type
            ]);

            // Process restore (in real implementation, you'd dispatch a job)
            $this->processRestore($backup);

            return back()->with('success', 'Backup restore completed successfully. Please verify your data.');
        } catch (\Exception $e) {
            Log::error("Backup restore failed", [
                'backup_id' => $backup->id,
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'Error restoring backup: ' . $e->getMessage());
        }
    }

    /**
     * Bulk actions for backups
     */
    public function bulkAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => ['required', 'in:delete,download'],
            'backups' => ['required', 'array'],
            'backups.*' => ['exists:backups,id'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $backups = Backup::whereIn('id', $request->backups);

        switch ($request->action) {
            case 'delete':
                $count = 0;
                foreach ($backups->get() as $backup) {
                    try {
                        $backup->deleteFile();
                        $backup->delete();
                        $count++;
                    } catch (\Exception $e) {
                        // Continue with other backups
                    }
                }
                return back()->with('success', "{$count} backups deleted successfully.");

            case 'download':
                // For bulk download, we'd typically create a zip file
                return back()->with('info', 'Bulk download feature coming soon.');

            default:
                return back()->with('error', 'Invalid action.');
        }
    }

    /**
     * Clean up expired backups
     */
    public function cleanup()
    {
        try {
            $expiredBackups = Backup::expired()->get();
            $count = 0;

            foreach ($expiredBackups as $backup) {
                try {
                    $backup->deleteFile();
                    $backup->delete();
                    $count++;
                } catch (\Exception $e) {
                    // Continue with other backups
                }
            }

            return back()->with('success', "Cleaned up {$count} expired backups.");
        } catch (\Exception $e) {
            return back()->with('error', 'Error during cleanup: ' . $e->getMessage());
        }
    }

    /**
     * Get backup statistics
     */
    private function getBackupStats(): array
    {
        return [
            'total_backups' => Backup::count(),
            'completed_backups' => Backup::completed()->count(),
            'failed_backups' => Backup::failed()->count(),
            'running_backups' => Backup::running()->count(),
            'database_backups' => Backup::ofType('database')->count(),
            'file_backups' => Backup::ofType('files')->count(),
            'full_backups' => Backup::ofType('full')->count(),
            'total_size' => Backup::completed()->sum('size_bytes'),
            'automatic_backups' => Backup::automatic()->count(),
            'manual_backups' => Backup::manual()->count(),
            'expired_backups' => Backup::expired()->count(),
            'backups_today' => Backup::whereDate('created_at', today())->count(),
            'backups_this_month' => Backup::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
        ];
    }

    /**
     * Generate storage path for backup
     */
    private function generateStoragePath(string $type, string $name): string
    {
        $filename = Backup::generateFilename($type, Str::slug($name));
        return "{$type}/" . now()->format('Y/m/d') . "/{$filename}";
    }

    /**
     * Process backup (simplified implementation)
     */
    private function processBackup(Backup $backup): void
    {
        try {
            $backup->markAsStarted();

            // Simulate backup process
            switch ($backup->type) {
                case 'database':
                    $this->createDatabaseBackup($backup);
                    break;
                case 'files':
                    $this->createFilesBackup($backup);
                    break;
                case 'full':
                    $this->createFullBackup($backup);
                    break;
            }

            // Simulate file size
            $size = rand(1024 * 1024, 1024 * 1024 * 100); // 1MB to 100MB
            $backup->markAsCompleted($size);

        } catch (\Exception $e) {
            $backup->markAsFailed($e->getMessage());
        }
    }

    /**
     * Create database backup
     */
    private function createDatabaseBackup(Backup $backup): void
    {
        if ($backup->backupable_type !== Database::class) {
            throw new \Exception('Invalid backupable type for database backup');
        }

        $database = $backup->backupable;

        // In real implementation, you'd use mysqldump or similar
        $content = "-- Database backup for {$database->name}\n";
        $content .= "-- Created at: " . now()->toDateTimeString() . "\n";
        $content .= "-- This is a simulated backup\n";

        Storage::disk($backup->storage_driver)->put($backup->storage_path, $content);
    }

    /**
     * Create files backup
     */
    private function createFilesBackup(Backup $backup): void
    {
        if ($backup->backupable_type !== Domain::class) {
            throw new \Exception('Invalid backupable type for files backup');
        }

        $domain = $backup->backupable;

        // In real implementation, you'd create a tar/zip archive
        $content = "Files backup for domain: {$domain->name}\n";
        $content .= "Created at: " . now()->toDateTimeString() . "\n";

        Storage::disk($backup->storage_driver)->put($backup->storage_path, $content);
    }

    /**
     * Create full backup
     */
    private function createFullBackup(Backup $backup): void
    {
        // In real implementation, you'd backup everything
        $content = "Full system backup\n";
        $content .= "Created at: " . now()->toDateTimeString() . "\n";

        Storage::disk($backup->storage_driver)->put($backup->storage_path, $content);
    }

    /**
     * Process restore (simplified implementation)
     */
    private function processRestore(Backup $backup): void
    {
        // In real implementation, you'd restore from the backup file
        // For demo purposes, simulate restore process

        switch ($backup->type) {
            case 'database':
                $this->simulateDatabaseRestore($backup);
                break;
            case 'files':
                $this->simulateFilesRestore($backup);
                break;
            case 'full':
                $this->simulateFullRestore($backup);
                break;
        }

        Log::info("Restore completed for backup: {$backup->name}");
    }

    /**
     * Simulate database restore
     */
    private function simulateDatabaseRestore(Backup $backup): void
    {
        if ($backup->backupable_type === Database::class) {
            $database = $backup->backupable;
            Log::info("Simulating database restore", [
                'database' => $database->name,
                'backup_id' => $backup->id
            ]);

            // Update last restore time
            $database->update(['last_backup_at' => now()]);
        }
    }

    /**
     * Simulate files restore
     */
    private function simulateFilesRestore(Backup $backup): void
    {
        if ($backup->backupable_type === Domain::class) {
            $domain = $backup->backupable;
            Log::info("Simulating files restore", [
                'domain' => $domain->name,
                'backup_id' => $backup->id
            ]);
        }
    }

    /**
     * Simulate full restore
     */
    private function simulateFullRestore(Backup $backup): void
    {
        Log::info("Simulating full system restore", [
            'backup_id' => $backup->id
        ]);
    }

    /**
     * Simulate restore process
     */
    private function simulateRestore(Backup $backup): void
    {
        // This method is used by the updated restore method
        $this->processRestore($backup);
    }

    /**
     * Retry failed backup
     */
    public function retry(Backup $backup)
    {
        try {
            if (!$backup->isFailed()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only failed backups can be retried.'
                ], 400);
            }

            // Reset backup status
            $backup->update([
                'status' => 'pending',
                'error_message' => null,
                'started_at' => null,
                'completed_at' => null,
                'duration_seconds' => null,
            ]);

            // Dispatch backup job again
            ProcessBackupJob::dispatch($backup);

            Log::info("Backup retry initiated", [
                'backup_id' => $backup->id,
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Backup retry initiated successfully.'
            ]);

        } catch (\Exception $e) {
            Log::error("Backup retry failed", [
                'backup_id' => $backup->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retry backup: ' . $e->getMessage()
            ], 500);
        }
    }
}
