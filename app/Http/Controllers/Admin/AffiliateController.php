<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class AffiliateController extends Controller
{
    /**
     * Display the affiliate program dashboard
     */
    public function index()
    {
        // Get affiliate statistics
        $stats = $this->getAffiliateStats();
        
        // Get affiliate programs
        $programs = $this->getAffiliatePrograms();
        
        // Get top affiliates
        $topAffiliates = $this->getTopAffiliates();
        
        // Get recent activities
        $recentActivities = $this->getRecentActivities();
        
        // Get affiliate analytics
        $analytics = $this->getAffiliateAnalytics();
        
        // Get commission structures
        $commissionStructures = $this->getCommissionStructures();

        return view('admin.affiliate.index', compact(
            'stats',
            'programs',
            'topAffiliates',
            'recentActivities',
            'analytics',
            'commissionStructures'
        ));
    }

    /**
     * Get affiliate statistics
     */
    private function getAffiliateStats()
    {
        return [
            'total_affiliates' => 2847,
            'active_affiliates' => 1923,
            'total_programs' => 8,
            'active_programs' => 6,
            'monthly_sales' => 156780.50,
            'total_commissions' => 23517.08,
            'pending_payments' => 12450.75,
            'conversion_rate' => 4.2,
            'average_order_value' => 127.50,
            'top_performing_program' => 'Premium Hosting'
        ];
    }

    /**
     * Get affiliate programs
     */
    private function getAffiliatePrograms()
    {
        return [
            [
                'id' => 1,
                'name' => 'Premium Hosting',
                'description' => 'High-performance hosting solutions',
                'commission_rate' => 25.0,
                'commission_type' => 'percentage',
                'cookie_duration' => 60,
                'min_payout' => 50.00,
                'affiliates_count' => 456,
                'monthly_sales' => 45670.80,
                'monthly_commissions' => 11417.70,
                'status' => 'active',
                'created_at' => now()->subMonths(8)
            ],
            [
                'id' => 2,
                'name' => 'Domain Registration',
                'description' => 'Domain names and DNS services',
                'commission_rate' => 15.0,
                'commission_type' => 'percentage',
                'cookie_duration' => 30,
                'min_payout' => 25.00,
                'affiliates_count' => 234,
                'monthly_sales' => 23450.60,
                'monthly_commissions' => 3517.59,
                'status' => 'active',
                'created_at' => now()->subMonths(6)
            ],
            [
                'id' => 3,
                'name' => 'SSL Certificates',
                'description' => 'Security certificates and encryption',
                'commission_rate' => 30.0,
                'commission_type' => 'percentage',
                'cookie_duration' => 45,
                'min_payout' => 30.00,
                'affiliates_count' => 189,
                'monthly_sales' => 18920.40,
                'monthly_commissions' => 5676.12,
                'status' => 'active',
                'created_at' => now()->subMonths(4)
            ],
            [
                'id' => 4,
                'name' => 'Email Marketing',
                'description' => 'Professional email marketing tools',
                'commission_rate' => 20.0,
                'commission_type' => 'percentage',
                'cookie_duration' => 90,
                'min_payout' => 40.00,
                'affiliates_count' => 167,
                'monthly_sales' => 15670.30,
                'monthly_commissions' => 3134.06,
                'status' => 'active',
                'created_at' => now()->subMonths(3)
            ],
            [
                'id' => 5,
                'name' => 'Website Builder',
                'description' => 'Drag-and-drop website creation',
                'commission_rate' => 35.0,
                'commission_type' => 'percentage',
                'cookie_duration' => 120,
                'min_payout' => 60.00,
                'affiliates_count' => 145,
                'monthly_sales' => 12340.50,
                'monthly_commissions' => 4319.18,
                'status' => 'active',
                'created_at' => now()->subMonths(2)
            ],
            [
                'id' => 6,
                'name' => 'Cloud Storage',
                'description' => 'Secure cloud storage solutions',
                'commission_rate' => 22.0,
                'commission_type' => 'percentage',
                'cookie_duration' => 75,
                'min_payout' => 35.00,
                'affiliates_count' => 123,
                'monthly_sales' => 9870.20,
                'monthly_commissions' => 2171.44,
                'status' => 'active',
                'created_at' => now()->subMonth()
            ],
            [
                'id' => 7,
                'name' => 'Legacy Hosting',
                'description' => 'Basic hosting packages (discontinued)',
                'commission_rate' => 10.0,
                'commission_type' => 'percentage',
                'cookie_duration' => 30,
                'min_payout' => 20.00,
                'affiliates_count' => 67,
                'monthly_sales' => 2340.10,
                'monthly_commissions' => 234.01,
                'status' => 'paused',
                'created_at' => now()->subYears(2)
            ],
            [
                'id' => 8,
                'name' => 'VPS Hosting',
                'description' => 'Virtual private server solutions',
                'commission_rate' => 40.0,
                'commission_type' => 'percentage',
                'cookie_duration' => 180,
                'min_payout' => 100.00,
                'affiliates_count' => 89,
                'monthly_sales' => 18760.40,
                'monthly_commissions' => 7504.16,
                'status' => 'draft',
                'created_at' => now()->subWeeks(2)
            ]
        ];
    }

    /**
     * Get top affiliates
     */
    private function getTopAffiliates()
    {
        return [
            [
                'id' => 1,
                'name' => 'TechReview Pro',
                'email' => '<EMAIL>',
                'programs' => ['Premium Hosting', 'SSL Certificates'],
                'total_sales' => 23450.80,
                'total_commissions' => 5862.70,
                'monthly_sales' => 4567.90,
                'monthly_commissions' => 1141.98,
                'conversion_rate' => 6.8,
                'join_date' => now()->subMonths(12),
                'last_sale' => now()->subHours(3),
                'status' => 'active'
            ],
            [
                'id' => 2,
                'name' => 'WebHostingGuru',
                'email' => '<EMAIL>',
                'programs' => ['Premium Hosting', 'Domain Registration'],
                'total_sales' => 18920.60,
                'total_commissions' => 4730.15,
                'monthly_sales' => 3456.80,
                'monthly_commissions' => 864.20,
                'conversion_rate' => 5.4,
                'join_date' => now()->subMonths(10),
                'last_sale' => now()->subHours(8),
                'status' => 'active'
            ],
            [
                'id' => 3,
                'name' => 'DigitalMarketer',
                'email' => '<EMAIL>',
                'programs' => ['Email Marketing', 'Website Builder'],
                'total_sales' => 15670.40,
                'total_commissions' => 3917.60,
                'monthly_sales' => 2890.70,
                'monthly_commissions' => 722.68,
                'conversion_rate' => 4.9,
                'join_date' => now()->subMonths(8),
                'last_sale' => now()->subDays(1),
                'status' => 'active'
            ],
            [
                'id' => 4,
                'name' => 'CloudExpert',
                'email' => '<EMAIL>',
                'programs' => ['Cloud Storage', 'VPS Hosting'],
                'total_sales' => 12340.20,
                'total_commissions' => 3086.05,
                'monthly_sales' => 2234.50,
                'monthly_commissions' => 558.63,
                'conversion_rate' => 3.7,
                'join_date' => now()->subMonths(6),
                'last_sale' => now()->subDays(2),
                'status' => 'active'
            ]
        ];
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities()
    {
        return [
            [
                'type' => 'sale',
                'description' => 'New sale generated by TechReview Pro',
                'affiliate' => 'TechReview Pro',
                'program' => 'Premium Hosting',
                'amount' => 127.50,
                'commission' => 31.88,
                'timestamp' => now()->subHours(2)
            ],
            [
                'type' => 'signup',
                'description' => 'New affiliate joined Website Builder program',
                'affiliate' => 'StartupBlogger',
                'program' => 'Website Builder',
                'amount' => 0,
                'commission' => 0,
                'timestamp' => now()->subHours(5)
            ],
            [
                'type' => 'payout',
                'description' => 'Commission payout processed',
                'affiliate' => 'WebHostingGuru',
                'program' => 'Multiple Programs',
                'amount' => 864.20,
                'commission' => 864.20,
                'timestamp' => now()->subHours(12)
            ],
            [
                'type' => 'program_launch',
                'description' => 'VPS Hosting program launched',
                'affiliate' => 'System',
                'program' => 'VPS Hosting',
                'amount' => 0,
                'commission' => 0,
                'timestamp' => now()->subDays(1)
            ]
        ];
    }

    /**
     * Get affiliate analytics
     */
    private function getAffiliateAnalytics()
    {
        return [
            'monthly_performance' => [
                'January' => 12450.80,
                'February' => 14670.90,
                'March' => 16890.40,
                'April' => 18920.60,
                'May' => 21340.70,
                'June' => 23517.08
            ],
            'program_performance' => [
                'Premium Hosting' => 35.2,
                'SSL Certificates' => 18.7,
                'Website Builder' => 15.3,
                'Email Marketing' => 12.8,
                'Domain Registration' => 10.4,
                'Cloud Storage' => 7.6
            ],
            'conversion_trends' => [
                'last_month' => 3.8,
                'this_month' => 4.2,
                'improvement' => 10.5
            ]
        ];
    }

    /**
     * Get commission structures
     */
    private function getCommissionStructures()
    {
        return [
            [
                'name' => 'Standard',
                'description' => 'Basic commission structure',
                'tiers' => [
                    ['sales_range' => '0-999', 'rate' => 15],
                    ['sales_range' => '1000-4999', 'rate' => 20],
                    ['sales_range' => '5000+', 'rate' => 25]
                ],
                'programs_count' => 3
            ],
            [
                'name' => 'Premium',
                'description' => 'Enhanced rates for premium products',
                'tiers' => [
                    ['sales_range' => '0-1999', 'rate' => 25],
                    ['sales_range' => '2000-9999', 'rate' => 30],
                    ['sales_range' => '10000+', 'rate' => 35]
                ],
                'programs_count' => 2
            ],
            [
                'name' => 'VIP',
                'description' => 'Highest commission rates',
                'tiers' => [
                    ['sales_range' => '0-4999', 'rate' => 35],
                    ['sales_range' => '5000-19999', 'rate' => 40],
                    ['sales_range' => '20000+', 'rate' => 45]
                ],
                'programs_count' => 1
            ]
        ];
    }

    /**
     * Create new affiliate program
     */
    public function createProgram(Request $request)
    {
        try {
            $programData = $request->all();
            
            // Simulate program creation
            return response()->json([
                'status' => 'success',
                'message' => 'Affiliate program created successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update program status
     */
    public function updateProgramStatus(Request $request)
    {
        try {
            $programId = $request->get('program_id');
            $status = $request->get('status');
            
            // Simulate status update
            return response()->json([
                'status' => 'success',
                'message' => 'Program status updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get real-time affiliate data
     */
    public function getRealTimeData(Request $request)
    {
        try {
            return response()->json([
                'stats' => $this->getAffiliateStats(),
                'recent_activities' => $this->getRecentActivities(),
                'analytics' => $this->getAffiliateAnalytics(),
                'timestamp' => now()->format('H:i:s')
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
