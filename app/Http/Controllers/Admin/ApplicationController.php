<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Application;
use App\Models\Domain;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ApplicationController extends Controller
{
    public function index(Request $request)
    {
        $query = Application::with(['user', 'domain']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('type', 'like', "%{$search}%")
                  ->orWhere('url', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('domain', function($domainQuery) use ($search) {
                      $domainQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $applications = $query->paginate(15)->withQueryString();

        // Statistics
        $stats = [
            'total' => Application::count(),
            'active' => Application::where('status', 'active')->count(),
            'inactive' => Application::where('status', 'inactive')->count(),
            'installing' => Application::where('status', 'installing')->count(),
            'error' => Application::where('status', 'error')->count(),
        ];

        // Type distribution
        $typeStats = Application::select('type', DB::raw('count(*) as count'))
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        // Recent activity
        $recentActivity = Application::with(['user', 'domain'])
            ->orderBy('updated_at', 'desc')
            ->limit(5)
            ->get();

        return view('admin.applications.index', compact(
            'applications',
            'stats',
            'typeStats',
            'recentActivity'
        ));
    }

    public function create()
    {
        $users = User::orderBy('name')->get();
        $domains = Domain::orderBy('name')->get();
        $appTemplates = \App\Models\AppTemplate::active()->popular()->get();

        return view('admin.applications.create', compact(
            'users',
            'domains',
            'appTemplates'
        ));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'domain_id' => 'nullable|exists:domains,id',
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:50',
            'version' => 'nullable|string|max:50',
            'install_path' => 'nullable|string|max:500',
            'url' => 'nullable|url|max:500',
            'admin_url' => 'nullable|url|max:500',
            'admin_username' => 'nullable|string|max:100',
            'admin_email' => 'nullable|email|max:255',
            'auto_update' => 'boolean',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $application = Application::create([
                'user_id' => $request->user_id,
                'domain_id' => $request->domain_id,
                'name' => $request->name,
                'type' => $request->type,
                'version' => $request->version,
                'status' => 'active',
                'install_path' => $request->install_path,
                'url' => $request->url,
                'admin_url' => $request->admin_url,
                'admin_username' => $request->admin_username,
                'admin_email' => $request->admin_email,
                'installed_at' => now(),
                'auto_update' => $request->boolean('auto_update'),
                'notes' => $request->notes
            ]);

            return redirect()->route('admin.applications.index')
                ->with('success', 'Application installed successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to install application: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Install application from template
     */
    public function installFromTemplate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'template_id' => 'required|exists:app_templates,id',
            'domain_id' => 'required|exists:domains,id',
            'admin_email' => 'required|email',
            'admin_user' => 'required|string|max:50',
            'admin_pass' => 'required|string|min:8',
            'site_title' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $template = \App\Models\AppTemplate::find($request->template_id);
            $domain = Domain::find($request->domain_id);

            if (!$domain->server) {
                throw new Exception('Domain server not configured');
            }

            // Initialize installation service
            $installationService = new \App\Services\ApplicationInstallationService($domain->server);

            // Prepare installation options
            $options = [
                'admin_email' => $request->admin_email,
                'admin_user' => $request->admin_user,
                'admin_pass' => $request->admin_pass,
                'title' => $request->site_title ?: $domain->name,
            ];

            // Install application based on template
            $result = $installationService->installApplication($template->slug, $domain, $options);

            if ($result['success']) {
                // Increment template install count
                $template->incrementInstallCount();

                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'application' => $result['application'],
                    'credentials' => $result['credentials'] ?? null,
                    'admin_url' => $result['admin_url'] ?? null,
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => $result['error']
                ], 500);
            }

        } catch (Exception $e) {
            Log::error("Template installation failed: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show(Application $application)
    {
        $application->load(['user', 'domain']);
        
        return view('admin.applications.show', compact('application'));
    }

    public function edit(Application $application)
    {
        $users = User::orderBy('name')->get();
        $domains = Domain::orderBy('name')->get();

        return view('admin.applications.edit', compact(
            'application',
            'users',
            'domains'
        ));
    }

    public function update(Request $request, Application $application)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'domain_id' => 'nullable|exists:domains,id',
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:50',
            'version' => 'nullable|string|max:50',
            'status' => 'required|in:active,inactive,installing,error',
            'install_path' => 'nullable|string|max:500',
            'url' => 'nullable|url|max:500',
            'admin_url' => 'nullable|url|max:500',
            'admin_username' => 'nullable|string|max:100',
            'admin_email' => 'nullable|email|max:255',
            'auto_update' => 'boolean',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $application->update([
                'user_id' => $request->user_id,
                'domain_id' => $request->domain_id,
                'name' => $request->name,
                'type' => $request->type,
                'version' => $request->version,
                'status' => $request->status,
                'install_path' => $request->install_path,
                'url' => $request->url,
                'admin_url' => $request->admin_url,
                'admin_username' => $request->admin_username,
                'admin_email' => $request->admin_email,
                'auto_update' => $request->boolean('auto_update'),
                'notes' => $request->notes
            ]);

            return redirect()->route('admin.applications.index')
                ->with('success', 'Application updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update application: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function destroy(Application $application)
    {
        try {
            $application->delete();
            
            return redirect()->route('admin.applications.index')
                ->with('success', 'Application deleted successfully!');
                
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete application: ' . $e->getMessage());
        }
    }

    public function updateApp(Request $request, Application $application)
    {
        if ($application->status === 'installing') {
            return response()->json([
                'success' => false,
                'message' => 'Application cannot be updated while installing.'
            ], 400);
        }

        try {
            $application->update([
                'status' => 'updating',
                'last_updated_at' => now()
            ]);

            // Simulate update process (in real app, this would trigger actual update)
            sleep(2);

            $application->update([
                'status' => 'active',
                'version' => $request->get('version', $application->version)
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Application updated successfully!'
            ]);

        } catch (\Exception $e) {
            $application->update(['status' => 'failed']);
            
            return response()->json([
                'success' => false,
                'message' => 'Update failed: ' . $e->getMessage()
            ], 500);
        }
    }

    public function restart(Application $application)
    {
        try {
            // Simulate application restart
            $application->update(['status' => 'installing']);

            // In real implementation, this would restart the actual application
            sleep(1);

            $application->update(['status' => 'active']);

            return response()->json([
                'success' => true,
                'message' => 'Application restarted successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Restart failed: ' . $e->getMessage()
            ], 500);
        }
    }

    public function bulkAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:delete,activate,deactivate,update',
            'applications' => 'required|array|min:1',
            'applications.*' => 'exists:apps,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data.'
            ], 400);
        }

        try {
            $applications = Application::whereIn('id', $request->applications);
            $count = $applications->count();

            switch ($request->action) {
                case 'delete':
                    $applications->delete();
                    $message = "{$count} applications deleted successfully.";
                    break;

                case 'activate':
                    $applications->update(['status' => 'active']);
                    $message = "{$count} applications activated successfully.";
                    break;

                case 'deactivate':
                    $applications->update(['status' => 'inactive']);
                    $message = "{$count} applications deactivated successfully.";
                    break;

                case 'update':
                    $applications->update([
                        'status' => 'updating',
                        'last_updated_at' => now()
                    ]);
                    // In real app, trigger actual updates here
                    $applications->update(['status' => 'active']);
                    $message = "{$count} applications updated successfully.";
                    break;
            }

            return response()->json([
                'success' => true,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Bulk action failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check application health
     */
    public function checkHealth(Application $application)
    {
        try {
            $monitoringService = new \App\Services\ApplicationMonitoringService($application);
            $result = $monitoringService->checkApplicationHealth();

            return response()->json($result);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Monitor all applications
     */
    public function monitorAll()
    {
        try {
            $results = \App\Services\ApplicationMonitoringService::monitorAllApplications();

            return response()->json([
                'success' => true,
                'results' => $results,
                'summary' => [
                    'total' => count($results),
                    'healthy' => count(array_filter($results, fn($r) => $r['success'] && $r['health_score'] >= 80)),
                    'warning' => count(array_filter($results, fn($r) => $r['success'] && $r['health_score'] >= 50 && $r['health_score'] < 80)),
                    'critical' => count(array_filter($results, fn($r) => !$r['success'] || $r['health_score'] < 50)),
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Uninstall application
     */
    public function uninstall(Application $application)
    {
        try {
            if (!$application->domain || !$application->domain->server) {
                throw new Exception('Application server not configured');
            }

            $installationService = new \App\Services\ApplicationInstallationService($application->domain->server);
            $result = $installationService->uninstallApplication($application->id);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => $result['error']
                ], 500);
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
