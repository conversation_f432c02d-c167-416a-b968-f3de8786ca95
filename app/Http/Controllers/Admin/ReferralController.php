<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ReferralController extends Controller
{
    /**
     * Display the referral program dashboard
     */
    public function index()
    {
        // Get referral statistics
        $stats = $this->getReferralStats();
        
        // Get referral tiers
        $tiers = $this->getReferralTiers();
        
        // Get top referrers
        $topReferrers = $this->getTopReferrers();
        
        // Get recent referrals
        $recentReferrals = $this->getRecentReferrals();
        
        // Get referral analytics
        $analytics = $this->getReferralAnalytics();
        
        // Get commission payouts
        $payouts = $this->getCommissionPayouts();

        return view('admin.referral.index', compact(
            'stats',
            'tiers',
            'topReferrers',
            'recentReferrals',
            'analytics',
            'payouts'
        ));
    }

    /**
     * Get referral statistics
     */
    private function getReferralStats()
    {
        return [
            'total_referrers' => 1247,
            'active_referrers' => 892,
            'total_referrals' => 5634,
            'successful_conversions' => 3421,
            'conversion_rate' => 60.7,
            'total_commissions_paid' => 125430.50,
            'pending_commissions' => 8750.25,
            'monthly_referrals' => 456,
            'average_commission' => 36.67,
            'top_tier_referrers' => 23
        ];
    }

    /**
     * Get referral tiers
     */
    private function getReferralTiers()
    {
        return [
            [
                'id' => 1,
                'name' => 'Bronze',
                'min_referrals' => 0,
                'max_referrals' => 9,
                'commission_rate' => 15.0,
                'bonus_threshold' => 5,
                'bonus_amount' => 50.00,
                'members_count' => 756,
                'color' => 'orange',
                'benefits' => ['15% Commission', '$50 Bonus at 5 referrals', 'Monthly Reports']
            ],
            [
                'id' => 2,
                'name' => 'Silver',
                'min_referrals' => 10,
                'max_referrals' => 24,
                'commission_rate' => 20.0,
                'bonus_threshold' => 15,
                'bonus_amount' => 150.00,
                'members_count' => 312,
                'color' => 'gray',
                'benefits' => ['20% Commission', '$150 Bonus at 15 referrals', 'Priority Support', 'Marketing Materials']
            ],
            [
                'id' => 3,
                'name' => 'Gold',
                'min_referrals' => 25,
                'max_referrals' => 49,
                'commission_rate' => 25.0,
                'bonus_threshold' => 30,
                'bonus_amount' => 300.00,
                'members_count' => 156,
                'color' => 'yellow',
                'benefits' => ['25% Commission', '$300 Bonus at 30 referrals', 'Dedicated Manager', 'Custom Landing Pages']
            ],
            [
                'id' => 4,
                'name' => 'Platinum',
                'min_referrals' => 50,
                'max_referrals' => null,
                'commission_rate' => 30.0,
                'bonus_threshold' => 75,
                'bonus_amount' => 500.00,
                'members_count' => 23,
                'color' => 'purple',
                'benefits' => ['30% Commission', '$500 Bonus at 75 referrals', 'VIP Support', 'Revenue Sharing']
            ]
        ];
    }

    /**
     * Get top referrers
     */
    private function getTopReferrers()
    {
        return [
            [
                'id' => 1,
                'name' => 'TechBlogger Pro',
                'email' => '<EMAIL>',
                'tier' => 'Platinum',
                'total_referrals' => 127,
                'successful_conversions' => 89,
                'total_earnings' => 4567.80,
                'monthly_earnings' => 892.50,
                'join_date' => now()->subMonths(14),
                'last_referral' => now()->subDays(2),
                'conversion_rate' => 70.1
            ],
            [
                'id' => 2,
                'name' => 'WebDev Solutions',
                'email' => '<EMAIL>',
                'tier' => 'Gold',
                'total_referrals' => 78,
                'successful_conversions' => 52,
                'total_earnings' => 2890.40,
                'monthly_earnings' => 456.75,
                'join_date' => now()->subMonths(10),
                'last_referral' => now()->subDays(1),
                'conversion_rate' => 66.7
            ],
            [
                'id' => 3,
                'name' => 'StartupGuru',
                'email' => '<EMAIL>',
                'tier' => 'Gold',
                'total_referrals' => 65,
                'successful_conversions' => 41,
                'total_earnings' => 2234.60,
                'monthly_earnings' => 378.20,
                'join_date' => now()->subMonths(8),
                'last_referral' => now()->subDays(3),
                'conversion_rate' => 63.1
            ],
            [
                'id' => 4,
                'name' => 'HostingReviews',
                'email' => '<EMAIL>',
                'tier' => 'Silver',
                'total_referrals' => 43,
                'successful_conversions' => 28,
                'total_earnings' => 1567.20,
                'monthly_earnings' => 234.80,
                'join_date' => now()->subMonths(6),
                'last_referral' => now()->subDays(5),
                'conversion_rate' => 65.1
            ]
        ];
    }

    /**
     * Get recent referrals
     */
    private function getRecentReferrals()
    {
        return [
            [
                'id' => 1,
                'referrer_name' => 'TechBlogger Pro',
                'referred_email' => '<EMAIL>',
                'status' => 'converted',
                'commission_amount' => 45.60,
                'created_at' => now()->subHours(2),
                'converted_at' => now()->subHours(1),
                'plan' => 'Business Pro'
            ],
            [
                'id' => 2,
                'referrer_name' => 'WebDev Solutions',
                'referred_email' => '<EMAIL>',
                'status' => 'pending',
                'commission_amount' => 0,
                'created_at' => now()->subHours(6),
                'converted_at' => null,
                'plan' => 'Starter'
            ],
            [
                'id' => 3,
                'referrer_name' => 'StartupGuru',
                'referred_email' => '<EMAIL>',
                'status' => 'converted',
                'commission_amount' => 67.80,
                'created_at' => now()->subDays(1),
                'converted_at' => now()->subHours(8),
                'plan' => 'Enterprise'
            ],
            [
                'id' => 4,
                'referrer_name' => 'HostingReviews',
                'referred_email' => '<EMAIL>',
                'status' => 'expired',
                'commission_amount' => 0,
                'created_at' => now()->subDays(32),
                'converted_at' => null,
                'plan' => 'Basic'
            ]
        ];
    }

    /**
     * Get referral analytics
     */
    private function getReferralAnalytics()
    {
        return [
            'monthly_referrals' => [
                'January' => 234,
                'February' => 267,
                'March' => 298,
                'April' => 345,
                'May' => 389,
                'June' => 456
            ],
            'conversion_by_tier' => [
                'Bronze' => 58.2,
                'Silver' => 62.8,
                'Gold' => 68.5,
                'Platinum' => 74.3
            ],
            'top_traffic_sources' => [
                'Blog Posts' => 34.5,
                'Social Media' => 28.7,
                'Email Marketing' => 19.8,
                'YouTube Videos' => 12.3,
                'Direct Links' => 4.7
            ],
            'commission_trends' => [
                'last_month' => 8934.50,
                'this_month' => 12456.75,
                'growth_rate' => 39.4
            ]
        ];
    }

    /**
     * Get commission payouts
     */
    private function getCommissionPayouts()
    {
        return [
            [
                'id' => 1,
                'referrer_name' => 'TechBlogger Pro',
                'amount' => 892.50,
                'period' => 'June 2024',
                'status' => 'paid',
                'payment_date' => now()->subDays(5),
                'payment_method' => 'PayPal'
            ],
            [
                'id' => 2,
                'referrer_name' => 'WebDev Solutions',
                'amount' => 456.75,
                'period' => 'June 2024',
                'status' => 'pending',
                'payment_date' => null,
                'payment_method' => 'Bank Transfer'
            ],
            [
                'id' => 3,
                'referrer_name' => 'StartupGuru',
                'amount' => 378.20,
                'period' => 'June 2024',
                'status' => 'processing',
                'payment_date' => null,
                'payment_method' => 'PayPal'
            ],
            [
                'id' => 4,
                'referrer_name' => 'HostingReviews',
                'amount' => 234.80,
                'period' => 'June 2024',
                'status' => 'pending',
                'payment_date' => null,
                'payment_method' => 'Stripe'
            ]
        ];
    }

    /**
     * Update referral tier
     */
    public function updateTier(Request $request)
    {
        try {
            $tierId = $request->get('tier_id');
            $updates = $request->get('updates');
            
            // Simulate tier update
            return response()->json([
                'status' => 'success',
                'message' => 'Referral tier updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process commission payout
     */
    public function processPayout(Request $request)
    {
        try {
            $payoutId = $request->get('payout_id');
            
            // Simulate payout processing
            return response()->json([
                'status' => 'success',
                'message' => 'Commission payout processed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get real-time referral data
     */
    public function getRealTimeData(Request $request)
    {
        try {
            return response()->json([
                'stats' => $this->getReferralStats(),
                'recent_referrals' => $this->getRecentReferrals(),
                'analytics' => $this->getReferralAnalytics(),
                'timestamp' => now()->format('H:i:s')
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
