<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class TeamController extends Controller
{
    /**
     * Display the team management dashboard
     */
    public function index()
    {
        // Get team statistics
        $stats = $this->getTeamStats();
        
        // Get team members
        $teamMembers = $this->getTeamMembers();
        
        // Get departments
        $departments = $this->getDepartments();
        
        // Get recent activities
        $recentActivities = $this->getRecentActivities();
        
        // Get team performance data
        $performanceData = $this->getPerformanceData();

        return view('admin.team.index', compact(
            'stats',
            'teamMembers',
            'departments',
            'recentActivities',
            'performanceData'
        ));
    }

    /**
     * Get team statistics
     */
    private function getTeamStats()
    {
        return [
            'total_members' => 28,
            'active_members' => 24,
            'on_leave' => 2,
            'new_this_month' => 3,
            'departments_count' => 6,
            'average_experience' => 3.2,
            'satisfaction_rate' => 94.5,
            'productivity_score' => 87.3
        ];
    }

    /**
     * Get team members
     */
    private function getTeamMembers()
    {
        return [
            [
                'id' => 1,
                'name' => 'Ahmed Hassan',
                'email' => '<EMAIL>',
                'position' => 'Senior Developer',
                'department' => 'Development',
                'status' => 'active',
                'hire_date' => now()->subMonths(18),
                'last_login' => now()->subHours(2),
                'avatar' => null,
                'skills' => ['PHP', 'Laravel', 'Vue.js', 'MySQL'],
                'projects_count' => 12,
                'performance_score' => 92
            ],
            [
                'id' => 2,
                'name' => 'Sara Mohamed',
                'email' => '<EMAIL>',
                'position' => 'UI/UX Designer',
                'department' => 'Design',
                'status' => 'active',
                'hire_date' => now()->subMonths(24),
                'last_login' => now()->subMinutes(30),
                'avatar' => null,
                'skills' => ['Figma', 'Adobe XD', 'Photoshop', 'Illustrator'],
                'projects_count' => 8,
                'performance_score' => 95
            ],
            [
                'id' => 3,
                'name' => 'Omar Ali',
                'email' => '<EMAIL>',
                'position' => 'DevOps Engineer',
                'department' => 'Infrastructure',
                'status' => 'active',
                'hire_date' => now()->subMonths(12),
                'last_login' => now()->subHours(1),
                'avatar' => null,
                'skills' => ['Docker', 'Kubernetes', 'AWS', 'Linux'],
                'projects_count' => 6,
                'performance_score' => 88
            ],
            [
                'id' => 4,
                'name' => 'Fatima Khalil',
                'email' => '<EMAIL>',
                'position' => 'Project Manager',
                'department' => 'Management',
                'status' => 'active',
                'hire_date' => now()->subMonths(30),
                'last_login' => now()->subHours(3),
                'avatar' => null,
                'skills' => ['Agile', 'Scrum', 'JIRA', 'Leadership'],
                'projects_count' => 15,
                'performance_score' => 96
            ],
            [
                'id' => 5,
                'name' => 'Khaled Ibrahim',
                'email' => '<EMAIL>',
                'position' => 'QA Engineer',
                'department' => 'Quality Assurance',
                'status' => 'on_leave',
                'hire_date' => now()->subMonths(8),
                'last_login' => now()->subDays(5),
                'avatar' => null,
                'skills' => ['Selenium', 'Jest', 'Cypress', 'Manual Testing'],
                'projects_count' => 4,
                'performance_score' => 85
            ],
            [
                'id' => 6,
                'name' => 'Nour Hassan',
                'email' => '<EMAIL>',
                'position' => 'Marketing Specialist',
                'department' => 'Marketing',
                'status' => 'active',
                'hire_date' => now()->subMonths(6),
                'last_login' => now()->subMinutes(15),
                'avatar' => null,
                'skills' => ['SEO', 'Social Media', 'Content Writing', 'Analytics'],
                'projects_count' => 7,
                'performance_score' => 90
            ]
        ];
    }

    /**
     * Get departments
     */
    private function getDepartments()
    {
        return [
            [
                'id' => 1,
                'name' => 'Development',
                'head' => 'Ahmed Hassan',
                'members_count' => 8,
                'budget' => 45000,
                'projects_active' => 5,
                'performance' => 89
            ],
            [
                'id' => 2,
                'name' => 'Design',
                'head' => 'Sara Mohamed',
                'members_count' => 4,
                'budget' => 25000,
                'projects_active' => 3,
                'performance' => 94
            ],
            [
                'id' => 3,
                'name' => 'Infrastructure',
                'head' => 'Omar Ali',
                'members_count' => 3,
                'budget' => 35000,
                'projects_active' => 2,
                'performance' => 87
            ],
            [
                'id' => 4,
                'name' => 'Management',
                'head' => 'Fatima Khalil',
                'members_count' => 5,
                'budget' => 40000,
                'projects_active' => 8,
                'performance' => 92
            ],
            [
                'id' => 5,
                'name' => 'Quality Assurance',
                'head' => 'Khaled Ibrahim',
                'members_count' => 4,
                'budget' => 20000,
                'projects_active' => 4,
                'performance' => 85
            ],
            [
                'id' => 6,
                'name' => 'Marketing',
                'head' => 'Nour Hassan',
                'members_count' => 4,
                'budget' => 30000,
                'projects_active' => 6,
                'performance' => 91
            ]
        ];
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities()
    {
        return [
            [
                'member' => 'Ahmed Hassan',
                'action' => 'Completed project milestone',
                'details' => 'E-commerce platform v2.0',
                'timestamp' => now()->subMinutes(30),
                'type' => 'achievement'
            ],
            [
                'member' => 'Sara Mohamed',
                'action' => 'Uploaded new design',
                'details' => 'Mobile app wireframes',
                'timestamp' => now()->subHours(2),
                'type' => 'upload'
            ],
            [
                'member' => 'Fatima Khalil',
                'action' => 'Scheduled team meeting',
                'details' => 'Sprint planning for next week',
                'timestamp' => now()->subHours(4),
                'type' => 'meeting'
            ],
            [
                'member' => 'Omar Ali',
                'action' => 'Deployed to production',
                'details' => 'API v3.2 release',
                'timestamp' => now()->subHours(6),
                'type' => 'deployment'
            ],
            [
                'member' => 'Nour Hassan',
                'action' => 'Published blog post',
                'details' => 'SEO best practices guide',
                'timestamp' => now()->subHours(8),
                'type' => 'content'
            ]
        ];
    }

    /**
     * Get performance data
     */
    private function getPerformanceData()
    {
        return [
            'overall_productivity' => 87.3,
            'team_satisfaction' => 94.5,
            'project_completion_rate' => 96.2,
            'average_response_time' => '2.4 hours',
            'collaboration_score' => 91.8,
            'innovation_index' => 88.7
        ];
    }

    /**
     * Add new team member
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'position' => 'required|string|max:255',
            'department' => 'required|string|max:255',
            'skills' => 'nullable|array',
            'hire_date' => 'required|date'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Simulate adding team member
            return response()->json([
                'status' => 'success',
                'message' => 'Team member added successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update team member status
     */
    public function updateStatus(Request $request)
    {
        $memberId = $request->get('member_id');
        $status = $request->get('status');
        
        try {
            // Simulate status update
            return response()->json([
                'status' => 'success',
                'message' => 'Team member status updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove team member
     */
    public function remove(Request $request)
    {
        $memberId = $request->get('member_id');
        
        try {
            // Simulate member removal
            return response()->json([
                'status' => 'success',
                'message' => 'Team member removed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get real-time team data
     */
    public function getRealTimeData(Request $request)
    {
        try {
            return response()->json([
                'stats' => $this->getTeamStats(),
                'recent_activities' => $this->getRecentActivities(),
                'performance_data' => $this->getPerformanceData(),
                'timestamp' => now()->format('H:i:s')
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
