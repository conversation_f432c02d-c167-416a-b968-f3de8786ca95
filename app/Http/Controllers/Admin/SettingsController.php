<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    public function index()
    {
        // Get system settings
        $systemSettings = $this->getSystemSettings();

        // Get security settings
        $securitySettings = $this->getSecuritySettings();

        // Get email settings
        $emailSettings = $this->getEmailSettings();

        // Get backup settings
        $backupSettings = $this->getBackupSettings();

        // Get maintenance settings
        $maintenanceSettings = $this->getMaintenanceSettings();

        // Get system status
        $systemStatus = $this->getSystemStatus();

        return view('admin.settings.index', compact(
            'systemSettings',
            'securitySettings',
            'emailSettings',
            'backupSettings',
            'maintenanceSettings',
            'systemStatus'
        ));
    }

    /**
     * Get system settings
     */
    private function getSystemSettings()
    {
        return [
            'app_name' => 'HostingPro',
            'app_url' => 'https://hostingpro.com',
            'app_timezone' => 'UTC',
            'app_locale' => 'en',
            'app_debug' => false,
            'app_environment' => 'production',
            'max_upload_size' => '100MB',
            'session_lifetime' => 120,
            'cache_driver' => 'redis',
            'queue_driver' => 'redis'
        ];
    }

    /**
     * Get security settings
     */
    private function getSecuritySettings()
    {
        return [
            'two_factor_enabled' => true,
            'password_min_length' => 8,
            'password_require_uppercase' => true,
            'password_require_lowercase' => true,
            'password_require_numbers' => true,
            'password_require_symbols' => true,
            'login_attempts_limit' => 5,
            'lockout_duration' => 15,
            'session_secure' => true,
            'force_https' => true,
            'api_rate_limit' => 1000,
            'admin_ip_whitelist' => ['***********/24']
        ];
    }

    /**
     * Get email settings
     */
    private function getEmailSettings()
    {
        return [
            'mail_driver' => 'smtp',
            'mail_host' => 'smtp.mailgun.org',
            'mail_port' => 587,
            'mail_username' => '<EMAIL>',
            'mail_encryption' => 'tls',
            'mail_from_address' => '<EMAIL>',
            'mail_from_name' => 'HostingPro',
            'notification_emails' => [
                '<EMAIL>',
                '<EMAIL>'
            ],
            'email_verification_required' => true,
            'welcome_email_enabled' => true,
            'newsletter_enabled' => true
        ];
    }

    /**
     * Get backup settings
     */
    private function getBackupSettings()
    {
        return [
            'backup_enabled' => true,
            'backup_frequency' => 'daily',
            'backup_time' => '02:00',
            'backup_retention_days' => 30,
            'backup_storage' => 's3',
            'backup_encryption' => true,
            'database_backup' => true,
            'files_backup' => true,
            'backup_compression' => true,
            'backup_notifications' => true,
            'last_backup' => now()->subHours(6),
            'backup_size' => '2.4 GB'
        ];
    }

    /**
     * Get maintenance settings
     */
    private function getMaintenanceSettings()
    {
        return [
            'maintenance_mode' => false,
            'maintenance_message' => 'We are currently performing scheduled maintenance. Please check back soon.',
            'maintenance_allowed_ips' => ['127.0.0.1', '*************'],
            'auto_updates' => true,
            'update_channel' => 'stable',
            'log_level' => 'info',
            'log_retention_days' => 14,
            'error_reporting' => true,
            'performance_monitoring' => true,
            'health_checks_enabled' => true,
            'monitoring_interval' => 5
        ];
    }

    /**
     * Get system status
     */
    private function getSystemStatus()
    {
        return [
            'system_health' => 'excellent',
            'uptime' => '99.98%',
            'cpu_usage' => 23.5,
            'memory_usage' => 67.2,
            'disk_usage' => 45.8,
            'active_users' => 1247,
            'database_connections' => 45,
            'cache_hit_ratio' => 94.2,
            'queue_jobs' => 12,
            'last_update' => now()->subMinutes(2),
            'php_version' => '8.2.0',
            'laravel_version' => '10.0.0',
            'database_version' => 'MySQL 8.0.33'
        ];
    }

    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'site_name' => 'required|string|max:255',
            'site_description' => 'nullable|string|max:1000',
            'admin_email' => 'required|email',
            'timezone' => 'required|string',
            'session_timeout' => 'required|integer|min:30|max:1440',
            'password_min_length' => 'required|integer|min:6|max:50',
            'require_uppercase' => 'boolean',
            'require_numbers' => 'boolean',
            'require_special_chars' => 'boolean',
            'require_2fa_admin' => 'boolean',
            'smtp_host' => 'nullable|string',
            'smtp_port' => 'nullable|integer',
            'smtp_encryption' => 'nullable|in:tls,ssl,none',
            'smtp_username' => 'nullable|string',
            'smtp_password' => 'nullable|string',
            'notify_new_user' => 'boolean',
            'notify_system_alerts' => 'boolean',
            'notify_backups' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // General Settings
            Setting::set('site_name', $request->site_name, 'string', 'general', 'Website name');
            Setting::set('site_description', $request->site_description, 'string', 'general', 'Website description');
            Setting::set('admin_email', $request->admin_email, 'string', 'general', 'Administrator email');
            Setting::set('timezone', $request->timezone, 'string', 'general', 'Default timezone');

            // Security Settings
            Setting::set('session_timeout', $request->session_timeout, 'integer', 'security', 'Session timeout in minutes');
            Setting::set('password_min_length', $request->password_min_length, 'integer', 'security', 'Minimum password length');
            Setting::set('require_uppercase', $request->has('require_uppercase'), 'boolean', 'security', 'Require uppercase letters in password');
            Setting::set('require_numbers', $request->has('require_numbers'), 'boolean', 'security', 'Require numbers in password');
            Setting::set('require_special_chars', $request->has('require_special_chars'), 'boolean', 'security', 'Require special characters in password');
            Setting::set('require_2fa_admin', $request->has('require_2fa_admin'), 'boolean', 'security', 'Require 2FA for admin users');

            // Email Settings
            Setting::set('smtp_host', $request->smtp_host, 'string', 'email', 'SMTP host');
            Setting::set('smtp_port', $request->smtp_port, 'integer', 'email', 'SMTP port');
            Setting::set('smtp_encryption', $request->smtp_encryption, 'string', 'email', 'SMTP encryption');
            Setting::set('smtp_username', $request->smtp_username, 'string', 'email', 'SMTP username');
            if ($request->smtp_password) {
                Setting::set('smtp_password', encrypt($request->smtp_password), 'string', 'email', 'SMTP password');
            }
            Setting::set('notify_new_user', $request->has('notify_new_user'), 'boolean', 'email', 'Notify on new user registration');
            Setting::set('notify_system_alerts', $request->has('notify_system_alerts'), 'boolean', 'email', 'Notify on system alerts');
            Setting::set('notify_backups', $request->has('notify_backups'), 'boolean', 'email', 'Notify on backup completion');

            return redirect()->route('admin.settings')
                ->with('success', 'Settings updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update settings: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function store(Request $request)
    {
        try {
            // Basic validation
            $request->validate([
                'site_name' => 'required|string|max:255',
                'admin_email' => 'required|email',
                'site_description' => 'nullable|string|max:1000',
                'timezone' => 'required|string'
            ]);

            // Save settings
            Setting::set('site_name', $request->site_name, 'string', 'general', 'Website name');
            Setting::set('site_description', $request->site_description, 'string', 'general', 'Website description');
            Setting::set('admin_email', $request->admin_email, 'string', 'general', 'Administrator email');
            Setting::set('timezone', $request->timezone, 'string', 'general', 'Default timezone');

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Settings saved successfully!'
                ]);
            }

            return redirect()->route('admin.settings')->with('success', 'Settings saved successfully!');

        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to save settings: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->route('admin.settings')->with('error', 'Failed to save settings: ' . $e->getMessage());
        }
    }

    public function clearCache()
    {
        try {
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('view:clear');
            Artisan::call('route:clear');

            Setting::clearCache();

            return response()->json([
                'success' => true,
                'message' => 'Cache cleared successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage()
            ], 500);
        }
    }

    public function createBackup()
    {
        try {
            // This would integrate with your backup system
            // For now, we'll simulate a backup creation

            $backupName = 'backup_' . date('Y_m_d_H_i_s') . '.sql';

            // Here you would implement actual backup logic
            // For example: mysqldump, file system backup, etc.

            return response()->json([
                'success' => true,
                'message' => 'Backup created successfully!',
                'backup_name' => $backupName
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create backup: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getSystemInfo()
    {
        try {
            $systemInfo = [
                'php_version' => phpversion(),
                'laravel_version' => app()->version(),
                'server_os' => php_uname('s'),
                'memory_limit' => ini_get('memory_limit'),
                'upload_max_filesize' => ini_get('upload_max_filesize'),
                'timezone' => date_default_timezone_get(),
                'disk_space' => $this->getDiskSpace(),
                'database_size' => $this->getDatabaseSize(),
            ];

            return response()->json([
                'success' => true,
                'data' => $systemInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get system info: ' . $e->getMessage()
            ], 500);
        }
    }

    private function getDiskSpace()
    {
        $bytes = disk_free_space('/');
        $total = disk_total_space('/');

        return [
            'free' => $this->formatBytes($bytes),
            'total' => $this->formatBytes($total),
            'used_percentage' => round((($total - $bytes) / $total) * 100, 2)
        ];
    }

    private function getDatabaseSize()
    {
        try {
            $databaseName = config('database.connections.mysql.database');
            $result = \DB::select("
                SELECT
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables
                WHERE table_schema = ?
            ", [$databaseName]);

            return $result[0]->size_mb . ' MB';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    public function exportSettings()
    {
        try {
            $settings = [
                'system' => $this->getSystemSettings(),
                'security' => $this->getSecuritySettings(),
                'email' => $this->getEmailSettings(),
                'backup' => $this->getBackupSettings(),
                'maintenance' => $this->getMaintenanceSettings(),
                'exported_at' => now()->toISOString(),
                'version' => '1.0'
            ];

            $filename = 'settings_export_' . date('Y_m_d_H_i_s') . '.json';

            return response()->json($settings)
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Content-Type', 'application/json');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export settings: ' . $e->getMessage()
            ], 500);
        }
    }
}
