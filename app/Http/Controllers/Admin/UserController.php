<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Display a listing of users
     */
    public function index(Request $request)
    {
        try {
            $query = User::query();

            // Apply filters
            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            }

            if ($request->filled('status')) {
                if ($request->get('status') === 'verified') {
                    $query->whereNotNull('email_verified_at');
                } elseif ($request->get('status') === 'unverified') {
                    $query->whereNull('email_verified_at');
                }
            }

            // Sort by creation date (newest first)
            $users = $query->orderBy('created_at', 'desc')->get();

            return view('admin.users', compact('users'));
        } catch (\Exception $e) {
            // Fallback with empty collection
            $users = collect();
            return view('admin.users', compact('users'));
        }
    }

    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        $roles = Role::all();
        return view('admin.users.create', compact('roles'));
    }

    /**
     * Store a newly created user
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'roles' => ['required', 'array'],
            'roles.*' => ['exists:roles,id'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'email_verified_at' => $request->has('verified') ? now() : null,
        ]);

        // Attach roles
        $user->roles()->attach($request->roles);

        return redirect()->route('admin.users')
            ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified user
     */
    public function show(User $user)
    {
        $user->load('roles');
        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        $user->load('roles');
        return view('admin.users.edit', compact('user', 'roles'));
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => ['nullable', 'string', 'min:8', 'confirmed'],
            'roles' => ['required', 'array'],
            'roles.*' => ['exists:roles,id'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        if ($request->has('verified')) {
            $updateData['email_verified_at'] = $request->verified ? now() : null;
        }

        $user->update($updateData);

        // Sync roles
        $user->roles()->sync($request->roles);

        return redirect()->route('admin.users')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user)
    {
        // Prevent deleting the current admin user
        if ($user->id === auth()->id()) {
            return back()->with('error', 'You cannot delete your own account.');
        }

        $user->delete();

        return redirect()->route('admin.users')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Bulk actions for users
     */
    public function bulkAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => ['required', 'in:delete,verify,unverify,export'],
            'users' => ['required', 'array'],
            'users.*' => ['exists:users,id'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $users = User::whereIn('id', $request->users)->get();
        $currentUserId = auth()->id();

        switch ($request->action) {
            case 'delete':
                // Remove current user from deletion list
                $users = $users->filter(function ($user) use ($currentUserId) {
                    return $user->id !== $currentUserId;
                });

                $count = $users->count();
                User::whereIn('id', $users->pluck('id'))->delete();

                return back()->with('success', "{$count} users deleted successfully.");

            case 'verify':
                User::whereIn('id', $request->users)
                    ->whereNull('email_verified_at')
                    ->update(['email_verified_at' => now()]);

                return back()->with('success', 'Selected users verified successfully.');

            case 'unverify':
                User::whereIn('id', $request->users)
                    ->whereNotNull('email_verified_at')
                    ->update(['email_verified_at' => null]);

                return back()->with('success', 'Selected users unverified successfully.');

            case 'export':
                return $this->exportUsers($users);
        }

        return back()->with('error', 'Invalid action.');
    }

    /**
     * Export users to CSV
     */
    public function export(Request $request)
    {
        $users = User::with('roles')->get();
        return $this->exportUsers($users);
    }

    /**
     * Export users data
     */
    private function exportUsers($users)
    {
        $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($users) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID',
                'Name',
                'Email',
                'Email Verified',
                'Roles',
                'Created At',
                'Updated At'
            ]);

            // CSV data
            foreach ($users as $user) {
                fputcsv($file, [
                    $user->id,
                    $user->name,
                    $user->email,
                    $user->email_verified_at ? 'Yes' : 'No',
                    $user->roles->pluck('name')->implode(', '),
                    $user->created_at->format('Y-m-d H:i:s'),
                    $user->updated_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Send verification email to user
     */
    public function sendVerificationEmail(User $user)
    {
        if ($user->hasVerifiedEmail()) {
            return back()->with('info', 'User email is already verified.');
        }

        $user->sendEmailVerificationNotification();

        return back()->with('success', 'Verification email sent successfully.');
    }

    /**
     * Reset user password
     */
    public function resetPassword(User $user)
    {
        $newPassword = \Str::random(12);
        $user->update([
            'password' => Hash::make($newPassword)
        ]);

        // Here you would typically send the new password via email
        // For demo purposes, we'll just return it
        return back()->with('success', "Password reset successfully. New password: {$newPassword}");
    }

    /**
     * Get user statistics
     */
    public function getStats()
    {
        return response()->json([
            'total_users' => User::count(),
            'verified_users' => User::whereNotNull('email_verified_at')->count(),
            'unverified_users' => User::whereNull('email_verified_at')->count(),
            'admin_users' => User::whereHas('roles', function ($q) {
                $q->where('slug', 'admin');
            })->count(),
            'new_this_month' => User::where('created_at', '>=', now()->startOfMonth())->count(),
            'new_today' => User::whereDate('created_at', today())->count(),
        ]);
    }
}
