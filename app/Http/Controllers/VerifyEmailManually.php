<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class VerifyEmailManually extends Controller
{
    /**
     * Mark the user's email as verified without sending an email
     */
    public function verifyManually(Request $request)
    {
        $user = $request->user();
        
        if (!$user) {
            return redirect()->route('login')
                ->with('error', 'You must be logged in to verify your email.');
        }
        
        if ($user->hasVerifiedEmail()) {
            return redirect()->route('dashboard')
                ->with('success', 'Your email is already verified.');
        }
        
        $user->email_verified_at = Carbon::now();
        $user->save();
        
        return redirect()->route('dashboard')
            ->with('success', 'Your email has been verified successfully.');
    }
}
