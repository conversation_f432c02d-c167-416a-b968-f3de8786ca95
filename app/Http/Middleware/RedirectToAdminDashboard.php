<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RedirectToAdminDashboard
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // If user is authenticated and trying to access regular dashboard
        if (Auth::check() && $request->is('dashboard')) {
            return redirect()->route('admin.dashboard');
        }

        // If user is authenticated and trying to access root
        if (Auth::check() && $request->is('/')) {
            return redirect()->route('admin.dashboard');
        }

        return $next($request);
    }
}
