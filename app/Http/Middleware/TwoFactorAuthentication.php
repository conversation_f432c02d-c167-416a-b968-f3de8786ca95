<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class TwoFactorAuthentication
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // If user is not authenticated, continue
        if (!$user) {
            return $next($request);
        }

        // If 2FA is not enabled for this user, continue
        if (!$user->hasEnabledTwoFactorAuthentication()) {
            return $next($request);
        }

        // If 2FA is already verified in this session, continue
        if (session('2fa_verified')) {
            return $next($request);
        }

        // If this is the 2FA challenge route, continue
        if ($request->routeIs('admin.two-factor.challenge') || 
            $request->routeIs('admin.two-factor.verify')) {
            return $next($request);
        }

        // If this is an AJAX request, return JSON response
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Two factor authentication required.',
                'redirect' => route('admin.two-factor.challenge')
            ], 423);
        }

        // Redirect to 2FA challenge
        return redirect()->route('admin.two-factor.challenge');
    }
}
