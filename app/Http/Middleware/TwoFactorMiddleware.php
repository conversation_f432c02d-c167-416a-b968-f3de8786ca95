<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\TwoFactorAuthService;

class TwoFactorMiddleware
{
    protected TwoFactorAuthService $twoFactorService;

    public function __construct(TwoFactorAuthService $twoFactorService)
    {
        $this->twoFactorService = $twoFactorService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();
        
        // Skip if user is not authenticated
        if (!$user) {
            return $next($request);
        }

        // Skip if 2FA is not enabled for this user
        if (!$this->twoFactorService->isEnabled($user)) {
            return $next($request);
        }

        // Skip if already verified in this session
        if (session('2fa_verified')) {
            return $next($request);
        }

        // Skip for 2FA related routes
        $exemptRoutes = [
            'admin.two-factor.verify',
            'admin.two-factor.verify-code',
            'admin.two-factor.verify-backup-code',
            'admin.two-factor.send-backup-code',
            'logout'
        ];

        if (in_array($request->route()->getName(), $exemptRoutes)) {
            return $next($request);
        }

        // Skip for AJAX requests - return JSON response
        if ($request->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => 'Two-factor authentication required.',
                'redirect' => route('admin.two-factor.verify')
            ], 403);
        }

        // Redirect to 2FA verification page
        return redirect()->route('admin.two-factor.verify')
            ->with('warning', 'Please complete two-factor authentication to continue.');
    }
}
