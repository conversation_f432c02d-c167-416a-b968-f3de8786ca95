<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;

class SSHService
{
    private $hostname;
    private $port;
    private $username;
    private $password;
    private $privateKey;
    private $connected = false;

    public function __construct($server = null)
    {
        // Constructor can accept server model if needed
    }

    /**
     * Connect to server via SSH (store connection details)
     */
    public function connect($hostname, $port, $username, $password = null, $privateKey = null)
    {
        try {
            $this->hostname = $hostname;
            $this->port = $port;
            $this->username = $username;
            $this->password = $password;
            $this->privateKey = $privateKey;

            // Test connection with a simple command
            $testResult = $this->execute('echo "connection_test"');

            if (trim($testResult) === 'connection_test') {
                $this->connected = true;
                return true;
            } else {
                throw new Exception("Connection test failed");
            }
        } catch (Exception $e) {
            Log::error("SSH Connection failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Execute command on remote server
     */
    public function execute($command)
    {
        if (!$this->hostname) {
            throw new Exception("No SSH connection details provided");
        }

        try {
            // Build SSH command
            $sshCommand = $this->buildSSHCommand($command);

            // Execute command using Process facade
            $result = Process::timeout(30)->run($sshCommand);

            if ($result->failed()) {
                throw new Exception("SSH command failed: " . $result->errorOutput());
            }

            return trim($result->output());
        } catch (Exception $e) {
            Log::error("SSH Command execution failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Build SSH command string
     */
    private function buildSSHCommand($command)
    {
        $sshOptions = [
            '-o StrictHostKeyChecking=no',
            '-o UserKnownHostsFile=/dev/null',
            '-o ConnectTimeout=10',
            '-p ' . $this->port
        ];

        if ($this->privateKey) {
            $sshOptions[] = '-i ' . escapeshellarg($this->privateKey);
            $sshCmd = sprintf(
                'ssh %s %s@%s %s',
                implode(' ', $sshOptions),
                escapeshellarg($this->username),
                escapeshellarg($this->hostname),
                escapeshellarg($command)
            );
        } elseif ($this->password) {
            // Use sshpass for password authentication
            $sshCmd = sprintf(
                'sshpass -p %s ssh %s %s@%s %s',
                escapeshellarg($this->password),
                implode(' ', $sshOptions),
                escapeshellarg($this->username),
                escapeshellarg($this->hostname),
                escapeshellarg($command)
            );
        } else {
            throw new Exception("No authentication method available");
        }

        return $sshCmd;
    }

    /**
     * Get server system information
     */
    public function getSystemInfo()
    {
        try {
            $info = [];
            
            // CPU information
            $cpuInfo = $this->execute("cat /proc/cpuinfo | grep 'model name' | head -1 | cut -d':' -f2 | xargs");
            $cpuCores = $this->execute("nproc");
            $info['cpu'] = [
                'model' => $cpuInfo,
                'cores' => (int)$cpuCores
            ];

            // Memory information
            $memInfo = $this->execute("free -m | grep '^Mem:' | awk '{print $2,$3,$7}'");
            $memParts = explode(' ', $memInfo);
            $info['memory'] = [
                'total' => (int)$memParts[0],
                'used' => (int)$memParts[1],
                'available' => (int)$memParts[2],
                'usage_percent' => round(((int)$memParts[1] / (int)$memParts[0]) * 100, 2)
            ];

            // Disk information
            $diskInfo = $this->execute("df -h / | tail -1 | awk '{print $2,$3,$4,$5}'");
            $diskParts = explode(' ', $diskInfo);
            $info['disk'] = [
                'total' => $diskParts[0],
                'used' => $diskParts[1],
                'available' => $diskParts[2],
                'usage_percent' => (int)str_replace('%', '', $diskParts[3])
            ];

            // CPU usage
            $cpuUsage = $this->execute("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1");
            $info['cpu_usage'] = (float)$cpuUsage;

            // Load average
            $loadAvg = $this->execute("uptime | awk -F'load average:' '{print $2}' | cut -d',' -f1 | xargs");
            $info['load_average'] = (float)$loadAvg;

            // Uptime
            $uptime = $this->execute("uptime -p");
            $info['uptime'] = $uptime;

            // Operating system
            $osInfo = $this->execute("lsb_release -d | cut -d':' -f2 | xargs");
            if (empty($osInfo)) {
                $osInfo = $this->execute("cat /etc/os-release | grep PRETTY_NAME | cut -d'=' -f2 | tr -d '\"'");
            }
            $info['os'] = $osInfo;

            // Network interfaces
            $networkInfo = $this->execute("ip -4 addr show | grep inet | grep -v 127.0.0.1 | awk '{print $2}' | cut -d'/' -f1");
            $info['network_interfaces'] = explode("\n", trim($networkInfo));

            return $info;
        } catch (Exception $e) {
            Log::error("Failed to get system info: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Check if server is online
     */
    public function ping()
    {
        try {
            $this->execute('echo "ping"');
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get running services
     */
    public function getServices()
    {
        try {
            $services = [];
            
            // Check common web services
            $webServices = ['apache2', 'nginx', 'httpd'];
            foreach ($webServices as $service) {
                $status = $this->execute("systemctl is-active {$service} 2>/dev/null || echo 'inactive'");
                if ($status === 'active') {
                    $services['web_server'] = $service;
                    break;
                }
            }

            // Check database services
            $dbServices = ['mysql', 'mariadb', 'postgresql'];
            foreach ($dbServices as $service) {
                $status = $this->execute("systemctl is-active {$service} 2>/dev/null || echo 'inactive'");
                if ($status === 'active') {
                    $services['database'] = $service;
                    break;
                }
            }

            // Check other services
            $otherServices = ['ssh', 'fail2ban', 'ufw', 'docker'];
            foreach ($otherServices as $service) {
                $status = $this->execute("systemctl is-active {$service} 2>/dev/null || echo 'inactive'");
                $services[$service] = $status === 'active';
            }

            return $services;
        } catch (Exception $e) {
            Log::error("Failed to get services: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Restart a service
     */
    public function restartService($serviceName)
    {
        try {
            $output = $this->execute("sudo systemctl restart {$serviceName}");
            $status = $this->execute("systemctl is-active {$serviceName}");
            
            return $status === 'active';
        } catch (Exception $e) {
            Log::error("Failed to restart service {$serviceName}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create a new user
     */
    public function createUser($username, $password = null)
    {
        try {
            // Create user
            $this->execute("sudo useradd -m -s /bin/bash {$username}");
            
            // Set password if provided
            if ($password) {
                $this->execute("echo '{$username}:{$password}' | sudo chpasswd");
            }

            return true;
        } catch (Exception $e) {
            Log::error("Failed to create user {$username}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create directory
     */
    public function createDirectory($path, $owner = null, $permissions = '755')
    {
        try {
            $this->execute("sudo mkdir -p {$path}");
            $this->execute("sudo chmod {$permissions} {$path}");
            
            if ($owner) {
                $this->execute("sudo chown {$owner}:{$owner} {$path}");
            }

            return true;
        } catch (Exception $e) {
            Log::error("Failed to create directory {$path}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Upload file via SCP
     */
    public function uploadFile($localFile, $remotePath)
    {
        try {
            if (!$this->hostname) {
                throw new Exception("No SSH connection details provided");
            }

            $scpCommand = $this->buildSCPCommand($localFile, $remotePath, 'upload');
            $result = Process::timeout(60)->run($scpCommand);

            if ($result->failed()) {
                throw new Exception("Failed to upload file: " . $result->errorOutput());
            }

            return true;
        } catch (Exception $e) {
            Log::error("File upload failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Download file via SCP
     */
    public function downloadFile($remotePath, $localFile)
    {
        try {
            if (!$this->hostname) {
                throw new Exception("No SSH connection details provided");
            }

            $scpCommand = $this->buildSCPCommand($localFile, $remotePath, 'download');
            $result = Process::timeout(60)->run($scpCommand);

            if ($result->failed()) {
                throw new Exception("Failed to download file: " . $result->errorOutput());
            }

            return true;
        } catch (Exception $e) {
            Log::error("File download failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Build SCP command string
     */
    private function buildSCPCommand($localFile, $remotePath, $direction)
    {
        $scpOptions = [
            '-o StrictHostKeyChecking=no',
            '-o UserKnownHostsFile=/dev/null',
            '-P ' . $this->port
        ];

        if ($this->privateKey) {
            $scpOptions[] = '-i ' . escapeshellarg($this->privateKey);

            if ($direction === 'upload') {
                $scpCmd = sprintf(
                    'scp %s %s %s@%s:%s',
                    implode(' ', $scpOptions),
                    escapeshellarg($localFile),
                    escapeshellarg($this->username),
                    escapeshellarg($this->hostname),
                    escapeshellarg($remotePath)
                );
            } else {
                $scpCmd = sprintf(
                    'scp %s %s@%s:%s %s',
                    implode(' ', $scpOptions),
                    escapeshellarg($this->username),
                    escapeshellarg($this->hostname),
                    escapeshellarg($remotePath),
                    escapeshellarg($localFile)
                );
            }
        } elseif ($this->password) {
            if ($direction === 'upload') {
                $scpCmd = sprintf(
                    'sshpass -p %s scp %s %s %s@%s:%s',
                    escapeshellarg($this->password),
                    implode(' ', $scpOptions),
                    escapeshellarg($localFile),
                    escapeshellarg($this->username),
                    escapeshellarg($this->hostname),
                    escapeshellarg($remotePath)
                );
            } else {
                $scpCmd = sprintf(
                    'sshpass -p %s scp %s %s@%s:%s %s',
                    escapeshellarg($this->password),
                    implode(' ', $scpOptions),
                    escapeshellarg($this->username),
                    escapeshellarg($this->hostname),
                    escapeshellarg($remotePath),
                    escapeshellarg($localFile)
                );
            }
        } else {
            throw new Exception("No authentication method available");
        }

        return $scpCmd;
    }

    /**
     * Close SSH connection
     */
    public function disconnect()
    {
        // Clear connection details
        $this->hostname = null;
        $this->port = null;
        $this->username = null;
        $this->password = null;
        $this->privateKey = null;
        $this->connected = false;
    }

    /**
     * Destructor to ensure connection is closed
     */
    public function __destruct()
    {
        $this->disconnect();
    }
}
