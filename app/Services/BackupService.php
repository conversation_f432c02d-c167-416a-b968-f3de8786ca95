<?php

namespace App\Services;

use App\Models\Backup;
use App\Models\Database;
use App\Models\Domain;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use ZipArchive;

class BackupService
{
    protected $sshService;
    
    public function __construct(SSHService $sshService)
    {
        $this->sshService = $sshService;
    }

    /**
     * Process backup based on type
     */
    public function processBackup(Backup $backup): bool
    {
        try {
            $backup->markAsStarted();
            
            Log::info("Starting backup process", ['backup_id' => $backup->id, 'type' => $backup->type]);

            switch ($backup->type) {
                case 'database':
                    return $this->processDatabaseBackup($backup);
                case 'files':
                    return $this->processFilesBackup($backup);
                case 'full':
                    return $this->processFullBackup($backup);
                default:
                    throw new \Exception("Unknown backup type: {$backup->type}");
            }
        } catch (\Exception $e) {
            Log::error("Backup failed", [
                'backup_id' => $backup->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $backup->markAsFailed($e->getMessage());
            return false;
        }
    }

    /**
     * Process database backup
     */
    protected function processDatabaseBackup(Backup $backup): bool
    {
        if ($backup->backupable_type !== Database::class) {
            throw new \Exception('Invalid backupable type for database backup');
        }

        $database = $backup->backupable;
        if (!$database) {
            throw new \Exception('Database not found');
        }

        $backupPath = $this->getBackupPath($backup);
        $this->ensureBackupDirectory($backupPath);

        // Create database dump
        $dumpFile = $this->createDatabaseDump($database, $backupPath);
        
        // Compress if needed
        if ($backup->compression !== 'none') {
            $compressedFile = $this->compressFile($dumpFile, $backup->compression);
            unlink($dumpFile); // Remove uncompressed file
            $finalFile = $compressedFile;
        } else {
            $finalFile = $dumpFile;
        }

        // Encrypt if needed
        if ($backup->encrypted) {
            $encryptedFile = $this->encryptFile($finalFile, $backup);
            unlink($finalFile); // Remove unencrypted file
            $finalFile = $encryptedFile;
        }

        // Get file size and update backup
        $fileSize = filesize($finalFile);
        $backup->markAsCompleted($fileSize);

        Log::info("Database backup completed", [
            'backup_id' => $backup->id,
            'database' => $database->name,
            'file_size' => $fileSize
        ]);

        return true;
    }

    /**
     * Process files backup
     */
    protected function processFilesBackup(Backup $backup): bool
    {
        if ($backup->backupable_type !== Domain::class) {
            throw new \Exception('Invalid backupable type for files backup');
        }

        $domain = $backup->backupable;
        if (!$domain) {
            throw new \Exception('Domain not found');
        }

        $backupPath = $this->getBackupPath($backup);
        $this->ensureBackupDirectory($backupPath);

        // Create files archive
        $archiveFile = $this->createFilesArchive($domain, $backupPath, $backup->compression);

        // Encrypt if needed
        if ($backup->encrypted) {
            $encryptedFile = $this->encryptFile($archiveFile, $backup);
            unlink($archiveFile); // Remove unencrypted file
            $archiveFile = $encryptedFile;
        }

        // Get file size and update backup
        $fileSize = filesize($archiveFile);
        $backup->markAsCompleted($fileSize);

        Log::info("Files backup completed", [
            'backup_id' => $backup->id,
            'domain' => $domain->name,
            'file_size' => $fileSize
        ]);

        return true;
    }

    /**
     * Process full system backup
     */
    protected function processFullBackup(Backup $backup): bool
    {
        $backupPath = $this->getBackupPath($backup);
        $this->ensureBackupDirectory($backupPath);

        // Create full system backup (databases + files)
        $archiveFile = $this->createFullSystemArchive($backupPath, $backup->compression);

        // Encrypt if needed
        if ($backup->encrypted) {
            $encryptedFile = $this->encryptFile($archiveFile, $backup);
            unlink($archiveFile); // Remove unencrypted file
            $archiveFile = $encryptedFile;
        }

        // Get file size and update backup
        $fileSize = filesize($archiveFile);
        $backup->markAsCompleted($fileSize);

        Log::info("Full backup completed", [
            'backup_id' => $backup->id,
            'file_size' => $fileSize
        ]);

        return true;
    }

    /**
     * Create database dump
     */
    protected function createDatabaseDump(Database $database, string $backupPath): string
    {
        $dumpFile = $backupPath . '/' . $database->name . '_' . now()->format('Y-m-d_H-i-s') . '.sql';

        switch ($database->type) {
            case 'mysql':
                return $this->createMySQLDump($database, $dumpFile);
            case 'postgresql':
                return $this->createPostgreSQLDump($database, $dumpFile);
            default:
                throw new \Exception("Unsupported database type: {$database->type}");
        }
    }

    /**
     * Create MySQL dump
     */
    protected function createMySQLDump(Database $database, string $dumpFile): string
    {
        // For demo purposes, create a realistic SQL dump
        $content = $this->generateMySQLDumpContent($database);
        file_put_contents($dumpFile, $content);
        
        return $dumpFile;
    }

    /**
     * Create PostgreSQL dump
     */
    protected function createPostgreSQLDump(Database $database, string $dumpFile): string
    {
        // For demo purposes, create a realistic SQL dump
        $content = $this->generatePostgreSQLDumpContent($database);
        file_put_contents($dumpFile, $content);
        
        return $dumpFile;
    }

    /**
     * Generate realistic MySQL dump content
     */
    protected function generateMySQLDumpContent(Database $database): string
    {
        $content = "-- MySQL dump for database: {$database->name}\n";
        $content .= "-- Generated on: " . now()->toDateTimeString() . "\n";
        $content .= "-- Host: {$database->host}:{$database->port}\n";
        $content .= "-- Database: {$database->name}\n\n";
        
        $content .= "SET NAMES utf8mb4;\n";
        $content .= "SET time_zone = '+00:00';\n";
        $content .= "SET foreign_key_checks = 0;\n";
        $content .= "SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';\n\n";
        
        // Add some sample table structures and data
        $tables = ['users', 'posts', 'categories', 'comments', 'settings'];
        
        foreach ($tables as $table) {
            $content .= $this->generateMySQLTableDump($table);
        }
        
        $content .= "SET foreign_key_checks = 1;\n";
        $content .= "-- Dump completed on " . now()->toDateTimeString() . "\n";
        
        return $content;
    }

    /**
     * Generate realistic PostgreSQL dump content
     */
    protected function generatePostgreSQLDumpContent(Database $database): string
    {
        $content = "-- PostgreSQL database dump for: {$database->name}\n";
        $content .= "-- Generated on: " . now()->toDateTimeString() . "\n";
        $content .= "-- Host: {$database->host}:{$database->port}\n";
        $content .= "-- Database: {$database->name}\n\n";
        
        $content .= "SET statement_timeout = 0;\n";
        $content .= "SET lock_timeout = 0;\n";
        $content .= "SET client_encoding = 'UTF8';\n";
        $content .= "SET standard_conforming_strings = on;\n\n";
        
        // Add some sample table structures and data
        $tables = ['users', 'posts', 'categories', 'comments', 'settings'];
        
        foreach ($tables as $table) {
            $content .= $this->generatePostgreSQLTableDump($table);
        }
        
        $content .= "-- Dump completed on " . now()->toDateTimeString() . "\n";
        
        return $content;
    }

    /**
     * Generate MySQL table dump
     */
    protected function generateMySQLTableDump(string $tableName): string
    {
        $content = "-- Table structure for table `{$tableName}`\n";
        $content .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
        
        switch ($tableName) {
            case 'users':
                $content .= "CREATE TABLE `users` (\n";
                $content .= "  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n";
                $content .= "  `name` varchar(255) NOT NULL,\n";
                $content .= "  `email` varchar(255) NOT NULL,\n";
                $content .= "  `created_at` timestamp NULL DEFAULT NULL,\n";
                $content .= "  PRIMARY KEY (`id`)\n";
                $content .= ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;\n\n";
                break;
            default:
                $content .= "CREATE TABLE `{$tableName}` (\n";
                $content .= "  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n";
                $content .= "  `name` varchar(255) NOT NULL,\n";
                $content .= "  `created_at` timestamp NULL DEFAULT NULL,\n";
                $content .= "  PRIMARY KEY (`id`)\n";
                $content .= ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;\n\n";
        }
        
        // Add some sample data
        $content .= "-- Dumping data for table `{$tableName}`\n";
        $content .= "INSERT INTO `{$tableName}` VALUES\n";
        for ($i = 1; $i <= 5; $i++) {
            $content .= "({$i}, 'Sample {$tableName} {$i}', '" . now()->subDays($i)->toDateTimeString() . "')";
            $content .= $i < 5 ? ",\n" : ";\n\n";
        }
        
        return $content;
    }

    /**
     * Generate PostgreSQL table dump
     */
    protected function generatePostgreSQLTableDump(string $tableName): string
    {
        $content = "-- Table: {$tableName}\n";
        $content .= "DROP TABLE IF EXISTS {$tableName};\n";
        
        switch ($tableName) {
            case 'users':
                $content .= "CREATE TABLE users (\n";
                $content .= "    id SERIAL PRIMARY KEY,\n";
                $content .= "    name VARCHAR(255) NOT NULL,\n";
                $content .= "    email VARCHAR(255) NOT NULL,\n";
                $content .= "    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n";
                $content .= ");\n\n";
                break;
            default:
                $content .= "CREATE TABLE {$tableName} (\n";
                $content .= "    id SERIAL PRIMARY KEY,\n";
                $content .= "    name VARCHAR(255) NOT NULL,\n";
                $content .= "    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n";
                $content .= ");\n\n";
        }
        
        // Add some sample data
        $content .= "-- Data for table {$tableName}\n";
        for ($i = 1; $i <= 5; $i++) {
            $content .= "INSERT INTO {$tableName} (name, created_at) VALUES ('Sample {$tableName} {$i}', '" . now()->subDays($i)->toDateTimeString() . "');\n";
        }
        $content .= "\n";
        
        return $content;
    }

    /**
     * Get backup storage path
     */
    protected function getBackupPath(Backup $backup): string
    {
        return storage_path('app/backups/' . dirname($backup->storage_path));
    }

    /**
     * Ensure backup directory exists
     */
    protected function ensureBackupDirectory(string $path): void
    {
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
    }

    /**
     * Create files archive
     */
    protected function createFilesArchive(Domain $domain, string $backupPath, string $compression): string
    {
        $archiveFile = $backupPath . '/' . $domain->name . '_files_' . now()->format('Y-m-d_H-i-s');

        if ($compression === 'zip') {
            $archiveFile .= '.zip';
            return $this->createZipArchive($domain, $archiveFile);
        } else {
            $archiveFile .= '.tar';
            $tarFile = $this->createTarArchive($domain, $archiveFile);

            if ($compression === 'gzip') {
                return $this->compressFile($tarFile, 'gzip');
            }

            return $tarFile;
        }
    }

    /**
     * Create ZIP archive
     */
    protected function createZipArchive(Domain $domain, string $archiveFile): string
    {
        $zip = new ZipArchive();

        if ($zip->open($archiveFile, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception("Cannot create ZIP archive: {$archiveFile}");
        }

        // Add domain files (simulated)
        $this->addDomainFilesToZip($zip, $domain);

        $zip->close();

        return $archiveFile;
    }

    /**
     * Create TAR archive
     */
    protected function createTarArchive(Domain $domain, string $archiveFile): string
    {
        // For demo purposes, create a simple tar-like content
        $content = $this->generateTarContent($domain);
        file_put_contents($archiveFile, $content);

        return $archiveFile;
    }

    /**
     * Add domain files to ZIP
     */
    protected function addDomainFilesToZip(ZipArchive $zip, Domain $domain): void
    {
        // Simulate domain files
        $files = [
            'index.html' => $this->generateSampleHTML($domain),
            'style.css' => $this->generateSampleCSS(),
            'script.js' => $this->generateSampleJS(),
            'config.php' => $this->generateSamplePHP($domain),
            'images/logo.png' => 'Binary image data placeholder',
            'uploads/file1.txt' => 'Sample upload file content',
        ];

        foreach ($files as $filename => $content) {
            $zip->addFromString($filename, $content);
        }
    }

    /**
     * Generate TAR content
     */
    protected function generateTarContent(Domain $domain): string
    {
        $content = "TAR Archive for domain: {$domain->name}\n";
        $content .= "Created: " . now()->toDateTimeString() . "\n";
        $content .= "Files included:\n";
        $content .= "- index.html\n";
        $content .= "- style.css\n";
        $content .= "- script.js\n";
        $content .= "- config.php\n";
        $content .= "- images/\n";
        $content .= "- uploads/\n";
        $content .= "\nTotal files: 6\n";

        return $content;
    }

    /**
     * Create full system archive
     */
    protected function createFullSystemArchive(string $backupPath, string $compression): string
    {
        $archiveFile = $backupPath . '/full_system_' . now()->format('Y-m-d_H-i-s');

        if ($compression === 'zip') {
            $archiveFile .= '.zip';
            return $this->createFullSystemZip($archiveFile);
        } else {
            $archiveFile .= '.tar';
            $tarFile = $this->createFullSystemTar($archiveFile);

            if ($compression === 'gzip') {
                return $this->compressFile($tarFile, 'gzip');
            }

            return $tarFile;
        }
    }

    /**
     * Create full system ZIP
     */
    protected function createFullSystemZip(string $archiveFile): string
    {
        $zip = new ZipArchive();

        if ($zip->open($archiveFile, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception("Cannot create ZIP archive: {$archiveFile}");
        }

        // Add system files (simulated)
        $this->addSystemFilesToZip($zip);

        $zip->close();

        return $archiveFile;
    }

    /**
     * Create full system TAR
     */
    protected function createFullSystemTar(string $archiveFile): string
    {
        $content = "Full System Backup\n";
        $content .= "Created: " . now()->toDateTimeString() . "\n";
        $content .= "Includes:\n";
        $content .= "- All databases\n";
        $content .= "- All domain files\n";
        $content .= "- System configuration\n";
        $content .= "- Application data\n";

        file_put_contents($archiveFile, $content);

        return $archiveFile;
    }

    /**
     * Add system files to ZIP
     */
    protected function addSystemFilesToZip(ZipArchive $zip): void
    {
        // Add databases
        $databases = Database::all();
        foreach ($databases as $database) {
            $dumpContent = $this->generateMySQLDumpContent($database);
            $zip->addFromString("databases/{$database->name}.sql", $dumpContent);
        }

        // Add domain files
        $domains = Domain::all();
        foreach ($domains as $domain) {
            $zip->addFromString("domains/{$domain->name}/index.html", $this->generateSampleHTML($domain));
            $zip->addFromString("domains/{$domain->name}/config.php", $this->generateSamplePHP($domain));
        }

        // Add system config
        $zip->addFromString("system/config.txt", "System configuration backup");
    }

    /**
     * Compress file
     */
    protected function compressFile(string $filePath, string $compression): string
    {
        switch ($compression) {
            case 'gzip':
                $compressedFile = $filePath . '.gz';
                $this->gzipFile($filePath, $compressedFile);
                return $compressedFile;
            case 'zip':
                $compressedFile = $filePath . '.zip';
                $this->zipFile($filePath, $compressedFile);
                return $compressedFile;
            default:
                return $filePath;
        }
    }

    /**
     * GZIP compress file
     */
    protected function gzipFile(string $source, string $destination): void
    {
        $sourceHandle = fopen($source, 'rb');
        $destHandle = gzopen($destination, 'wb9');

        while (!feof($sourceHandle)) {
            gzwrite($destHandle, fread($sourceHandle, 8192));
        }

        fclose($sourceHandle);
        gzclose($destHandle);
    }

    /**
     * ZIP compress file
     */
    protected function zipFile(string $source, string $destination): void
    {
        $zip = new ZipArchive();

        if ($zip->open($destination, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception("Cannot create ZIP file: {$destination}");
        }

        $zip->addFile($source, basename($source));
        $zip->close();
    }

    /**
     * Encrypt file
     */
    protected function encryptFile(string $filePath, Backup $backup): string
    {
        $encryptedFile = $filePath . '.enc';
        $key = $this->generateEncryptionKey($backup);

        $data = file_get_contents($filePath);
        $encryptedData = $this->encryptData($data, $key);

        file_put_contents($encryptedFile, $encryptedData);

        // Store encryption key in metadata
        $metadata = $backup->metadata ?? [];
        $metadata['encryption_key'] = base64_encode($key);
        $backup->update(['metadata' => $metadata]);

        return $encryptedFile;
    }

    /**
     * Generate encryption key
     */
    protected function generateEncryptionKey(Backup $backup): string
    {
        return hash('sha256', $backup->id . $backup->name . config('app.key'), true);
    }

    /**
     * Encrypt data using AES-256
     */
    protected function encryptData(string $data, string $key): string
    {
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }

    /**
     * Generate sample HTML content
     */
    protected function generateSampleHTML(Domain $domain): string
    {
        return "<!DOCTYPE html>
<html>
<head>
    <title>{$domain->name}</title>
    <link rel=\"stylesheet\" href=\"style.css\">
</head>
<body>
    <h1>Welcome to {$domain->name}</h1>
    <p>This is a sample website for domain {$domain->name}</p>
    <script src=\"script.js\"></script>
</body>
</html>";
    }

    /**
     * Generate sample CSS content
     */
    protected function generateSampleCSS(): string
    {
        return "body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f4f4f4;
}

h1 {
    color: #333;
    text-align: center;
}

p {
    line-height: 1.6;
    color: #666;
}";
    }

    /**
     * Generate sample JavaScript content
     */
    protected function generateSampleJS(): string
    {
        return "document.addEventListener('DOMContentLoaded', function() {
    console.log('Website loaded successfully');

    // Add some interactivity
    const title = document.querySelector('h1');
    if (title) {
        title.addEventListener('click', function() {
            alert('Welcome to our website!');
        });
    }
});";
    }

    /**
     * Generate sample PHP content
     */
    protected function generateSamplePHP(Domain $domain): string
    {
        return "<?php
// Configuration file for {$domain->name}

define('SITE_NAME', '{$domain->name}');
define('SITE_URL', 'https://{$domain->name}');
define('DB_HOST', 'localhost');
define('DB_NAME', 'database_name');
define('DB_USER', 'database_user');
define('DB_PASS', 'database_password');

// Site settings
\$config = [
    'site_name' => SITE_NAME,
    'site_url' => SITE_URL,
    'timezone' => 'UTC',
    'debug' => false,
];

// Database connection
try {
    \$pdo = new PDO(
        'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME,
        DB_USER,
        DB_PASS
    );
    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException \$e) {
    die('Database connection failed: ' . \$e->getMessage());
}
?>";
    }
}
