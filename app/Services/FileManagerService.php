<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Http\UploadedFile;

class FileManagerService
{
    private $basePath;
    private $allowedExtensions;
    private $maxFileSize;

    public function __construct($basePath = null)
    {
        $this->basePath = $basePath ?: storage_path('app/file-manager');
        $this->allowedExtensions = [
            'txt', 'html', 'css', 'js', 'json', 'xml', 'php', 'py', 'java', 'cpp', 'c',
            'jpg', 'jpeg', 'png', 'gif', 'svg', 'webp',
            'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
            'zip', 'rar', '7z', 'tar', 'gz',
            'mp3', 'mp4', 'avi', 'mov', 'wmv'
        ];
        $this->maxFileSize = 100 * 1024 * 1024; // 100MB

        // Ensure base directory exists
        if (!File::exists($this->basePath)) {
            File::makeDirectory($this->basePath, 0755, true);
        }
    }

    /**
     * List directory contents
     */
    public function listDirectory($path = '')
    {
        try {
            $fullPath = $this->getFullPath($path);
            
            if (!File::exists($fullPath)) {
                throw new Exception("Directory does not exist: {$path}");
            }

            if (!File::isDirectory($fullPath)) {
                throw new Exception("Path is not a directory: {$path}");
            }

            $items = [];
            $files = File::glob($fullPath . '/*');

            foreach ($files as $file) {
                $relativePath = str_replace($this->basePath . '/', '', $file);
                $isDirectory = File::isDirectory($file);
                
                $items[] = [
                    'name' => basename($file),
                    'path' => $relativePath,
                    'type' => $isDirectory ? 'directory' : 'file',
                    'size' => $isDirectory ? null : File::size($file),
                    'size_human' => $isDirectory ? null : $this->formatBytes(File::size($file)),
                    'modified' => File::lastModified($file),
                    'modified_human' => date('Y-m-d H:i:s', File::lastModified($file)),
                    'permissions' => substr(sprintf('%o', fileperms($file)), -4),
                    'extension' => $isDirectory ? null : File::extension($file),
                    'mime_type' => $isDirectory ? null : File::mimeType($file),
                    'is_readable' => File::isReadable($file),
                    'is_writable' => File::isWritable($file)
                ];
            }

            // Sort: directories first, then files
            usort($items, function($a, $b) {
                if ($a['type'] !== $b['type']) {
                    return $a['type'] === 'directory' ? -1 : 1;
                }
                return strcasecmp($a['name'], $b['name']);
            });

            return [
                'success' => true,
                'path' => $path,
                'items' => $items,
                'total_items' => count($items)
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Create new directory
     */
    public function createDirectory($path, $name)
    {
        try {
            $fullPath = $this->getFullPath($path . '/' . $name);
            
            if (File::exists($fullPath)) {
                throw new Exception("Directory already exists: {$name}");
            }

            File::makeDirectory($fullPath, 0755, true);

            return [
                'success' => true,
                'message' => "Directory '{$name}' created successfully",
                'path' => $path . '/' . $name
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Upload file
     */
    public function uploadFile(UploadedFile $file, $path = '')
    {
        try {
            $fileName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            
            // Validate file extension
            if (!in_array(strtolower($extension), $this->allowedExtensions)) {
                throw new Exception("File type not allowed: {$extension}");
            }

            // Validate file size
            if ($file->getSize() > $this->maxFileSize) {
                throw new Exception("File size exceeds maximum allowed size");
            }

            $fullPath = $this->getFullPath($path);
            $targetPath = $fullPath . '/' . $fileName;

            // Handle duplicate names
            $counter = 1;
            $originalName = pathinfo($fileName, PATHINFO_FILENAME);
            $extension = pathinfo($fileName, PATHINFO_EXTENSION);
            
            while (File::exists($targetPath)) {
                $fileName = $originalName . '_' . $counter . '.' . $extension;
                $targetPath = $fullPath . '/' . $fileName;
                $counter++;
            }

            $file->move($fullPath, $fileName);

            return [
                'success' => true,
                'message' => "File '{$fileName}' uploaded successfully",
                'file_name' => $fileName,
                'file_path' => $path . '/' . $fileName,
                'file_size' => File::size($targetPath),
                'file_size_human' => $this->formatBytes(File::size($targetPath))
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete file or directory
     */
    public function delete($path)
    {
        try {
            $fullPath = $this->getFullPath($path);
            
            if (!File::exists($fullPath)) {
                throw new Exception("File or directory does not exist: {$path}");
            }

            if (File::isDirectory($fullPath)) {
                File::deleteDirectory($fullPath);
                $message = "Directory deleted successfully";
            } else {
                File::delete($fullPath);
                $message = "File deleted successfully";
            }

            return [
                'success' => true,
                'message' => $message
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Rename file or directory
     */
    public function rename($path, $newName)
    {
        try {
            $fullPath = $this->getFullPath($path);
            $newPath = dirname($fullPath) . '/' . $newName;
            
            if (!File::exists($fullPath)) {
                throw new Exception("File or directory does not exist: {$path}");
            }

            if (File::exists($newPath)) {
                throw new Exception("A file or directory with that name already exists");
            }

            File::move($fullPath, $newPath);

            return [
                'success' => true,
                'message' => "Renamed successfully",
                'new_path' => str_replace($this->basePath . '/', '', $newPath)
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get file content
     */
    public function getFileContent($path)
    {
        try {
            $fullPath = $this->getFullPath($path);
            
            if (!File::exists($fullPath)) {
                throw new Exception("File does not exist: {$path}");
            }

            if (File::isDirectory($fullPath)) {
                throw new Exception("Cannot read directory as file: {$path}");
            }

            $content = File::get($fullPath);
            $mimeType = File::mimeType($fullPath);
            
            return [
                'success' => true,
                'content' => $content,
                'mime_type' => $mimeType,
                'size' => File::size($fullPath),
                'size_human' => $this->formatBytes(File::size($fullPath)),
                'is_text' => str_starts_with($mimeType, 'text/') || in_array($mimeType, [
                    'application/json',
                    'application/xml',
                    'application/javascript'
                ])
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Save file content
     */
    public function saveFileContent($path, $content)
    {
        try {
            $fullPath = $this->getFullPath($path);
            
            File::put($fullPath, $content);

            return [
                'success' => true,
                'message' => "File saved successfully",
                'size' => File::size($fullPath),
                'size_human' => $this->formatBytes(File::size($fullPath))
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get full system path
     */
    public function getFullPath($path)
    {
        $path = ltrim($path, '/');
        $fullPath = $this->basePath . '/' . $path;
        
        // Security check: ensure path is within base directory
        $realPath = realpath($fullPath) ?: $fullPath;
        $realBasePath = realpath($this->basePath);
        
        if (!str_starts_with($realPath, $realBasePath)) {
            throw new Exception("Access denied: Path outside allowed directory");
        }

        return $fullPath;
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Get directory tree structure
     */
    public function getDirectoryTree($path = '', $maxDepth = 3, $currentDepth = 0)
    {
        if ($currentDepth >= $maxDepth) {
            return [];
        }

        try {
            $fullPath = $this->getFullPath($path);
            $tree = [];
            
            if (!File::isDirectory($fullPath)) {
                return $tree;
            }

            $directories = File::directories($fullPath);
            
            foreach ($directories as $dir) {
                $relativePath = str_replace($this->basePath . '/', '', $dir);
                $name = basename($dir);
                
                $tree[] = [
                    'name' => $name,
                    'path' => $relativePath,
                    'children' => $this->getDirectoryTree($relativePath, $maxDepth, $currentDepth + 1)
                ];
            }

            return $tree;

        } catch (Exception $e) {
            return [];
        }
    }
}
