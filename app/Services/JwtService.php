<?php

namespace App\Services;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class JwtService
{
    private string $key;
    private string $algorithm;
    private int $expirationTime;

    public function __construct()
    {
        $this->key = env('JWT_SECRET', Str::random(32));
        $this->algorithm = 'HS256';
        $this->expirationTime = 60 * 60; // 1 hour
    }

    public function generateToken(User $user): string
    {
        $issuedAt = time();
        $expirationTime = $issuedAt + $this->expirationTime;

        $payload = [
            'iss' => config('app.url'),
            'iat' => $issuedAt,
            'exp' => $expirationTime,
            'nbf' => $issuedAt,
            'sub' => $user->id,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
            ],
        ];

        return JWT::encode($payload, $this->key, $this->algorithm);
    }

    public function validateToken(string $token)
    {
        try {
            $decoded = JWT::decode($token, new Key($this->key, $this->algorithm));
            return $decoded;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function getUserFromToken(string $token)
    {
        $decoded = $this->validateToken($token);
        
        if (!$decoded) {
            return null;
        }

        return User::find($decoded->sub);
    }

    public function refreshToken(string $token): ?string
    {
        $user = $this->getUserFromToken($token);
        
        if (!$user) {
            return null;
        }

        return $this->generateToken($user);
    }
}
