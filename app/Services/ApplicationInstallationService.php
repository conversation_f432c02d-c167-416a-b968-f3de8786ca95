<?php

namespace App\Services;

use App\Models\Application;
use App\Models\Domain;
use App\Models\Database;
use App\Models\Server;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use phpseclib3\Net\SSH2;

class ApplicationInstallationService
{
    private $ssh;
    private $server;
    private $cpanelService;

    public function __construct(Server $server = null)
    {
        $this->server = $server;
        if ($server) {
            $this->initializeSSH();
            $this->cpanelService = new CpanelService(
                $server->ip_address,
                $server->cpanel_username,
                $server->cpanel_password
            );
        }
    }

    /**
     * Initialize SSH connection
     */
    private function initializeSSH()
    {
        try {
            $this->ssh = new SSH2($this->server->ip_address, $this->server->ssh_port ?? 22);
            
            if ($this->server->ssh_key) {
                $key = \phpseclib3\Crypt\PublicKeyLoader::load($this->server->ssh_key);
                if (!$this->ssh->login($this->server->ssh_username, $key)) {
                    throw new Exception('SSH key authentication failed');
                }
            } else {
                if (!$this->ssh->login($this->server->ssh_username, $this->server->ssh_password)) {
                    throw new Exception('SSH password authentication failed');
                }
            }
        } catch (Exception $e) {
            Log::error("SSH connection failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Install application based on type
     */
    public function installApplication($type, $domain, $options = [])
    {
        try {
            switch ($type) {
                case 'wordpress':
                    return $this->installWordPress($domain, $options);
                case 'laravel':
                    return $this->installLaravel($domain, $options);
                case 'drupal':
                    return $this->installDrupal($domain, $options);
                case 'joomla':
                    return $this->installJoomla($domain, $options);
                case 'magento':
                    return $this->installMagento($domain, $options);
                case 'prestashop':
                    return $this->installPrestaShop($domain, $options);
                default:
                    throw new Exception("Unsupported application type: {$type}");
            }
        } catch (Exception $e) {
            Log::error("Application installation failed: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Install WordPress application
     */
    public function installWordPress($domain, $options = [])
    {
        try {
            $installPath = "/var/www/{$domain->name}/public_html";
            $dbName = $this->generateDatabaseName($domain->name, 'wp');
            $dbUser = $this->generateDatabaseUser($domain->name);
            $dbPass = Str::random(16);

            $adminEmail = $options['admin_email'] ?? $domain->user->email;
            $adminUser = $options['admin_user'] ?? 'admin';
            $adminPass = $options['admin_pass'] ?? Str::random(12);
            $title = $options['title'] ?? $domain->name;

            // Create database
            $dbResult = $this->cpanelService->createDatabase($dbName, $dbUser, $dbPass);
            if (!$dbResult['database']['status']) {
                throw new Exception('Failed to create database');
            }

            // Create installation directory
            $this->ssh->exec("mkdir -p {$installPath}");

            // Download WordPress
            $this->ssh->exec("cd {$installPath} && wget https://wordpress.org/latest.tar.gz");
            $this->ssh->exec("cd {$installPath} && tar -xzf latest.tar.gz --strip-components=1");
            $this->ssh->exec("cd {$installPath} && rm latest.tar.gz");

            // Create wp-config.php
            $wpConfig = $this->generateWordPressConfig($dbName, $dbUser, $dbPass, $domain->name);
            $this->ssh->exec("echo '{$wpConfig}' > {$installPath}/wp-config.php");

            // Set permissions
            $this->ssh->exec("chown -R {$domain->user->username}:{$domain->user->username} {$installPath}");
            $this->ssh->exec("find {$installPath} -type d -exec chmod 755 {} \\;");
            $this->ssh->exec("find {$installPath} -type f -exec chmod 644 {} \\;");

            // Install WordPress via WP-CLI if available
            $wpCliInstalled = trim($this->ssh->exec("which wp")) !== '';
            if ($wpCliInstalled) {
                $installCommand = "cd {$installPath} && wp core install --url=https://{$domain->name} --title='{$title}' --admin_user={$adminUser} --admin_password={$adminPass} --admin_email={$adminEmail} --allow-root";
                $this->ssh->exec($installCommand);
            }

            // Create application record
            $application = Application::create([
                'user_id' => $domain->user_id,
                'domain_id' => $domain->id,
                'name' => 'WordPress',
                'type' => 'cms',
                'version' => $this->getWordPressVersion($installPath),
                'status' => 'active',
                'install_path' => $installPath,
                'url' => "https://{$domain->name}",
                'admin_url' => "https://{$domain->name}/wp-admin",
                'admin_username' => $adminUser,
                'admin_email' => $adminEmail,
                'database_config' => [
                    'name' => $dbName,
                    'user' => $dbUser,
                    'host' => 'localhost',
                    'type' => 'mysql'
                ],
                'installed_at' => now(),
                'auto_update' => false,
                'notes' => 'Automatically installed via hosting panel'
            ]);

            return [
                'success' => true,
                'application' => $application,
                'credentials' => [
                    'admin_user' => $adminUser,
                    'admin_pass' => $adminPass,
                    'admin_email' => $adminEmail
                ],
                'database' => [
                    'name' => $dbName,
                    'user' => $dbUser
                ],
                'admin_url' => "https://{$domain->name}/wp-admin",
                'message' => 'WordPress installed successfully'
            ];

        } catch (Exception $e) {
            Log::error("WordPress installation failed: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Install Laravel application
     */
    public function installLaravel($domain, $options = [])
    {
        try {
            $projectName = $options['project_name'] ?? Str::slug($domain->name);
            $installPath = "/var/www/{$domain->name}";
            $dbName = $this->generateDatabaseName($domain->name, 'laravel');
            $dbUser = $this->generateDatabaseUser($domain->name);
            $dbPass = Str::random(16);

            // Create database
            $dbResult = $this->cpanelService->createDatabase($dbName, $dbUser, $dbPass);
            if (!$dbResult['database']['status']) {
                throw new Exception('Failed to create database');
            }

            // Check if Composer is available
            $composerPath = trim($this->ssh->exec("which composer")) ?: '/usr/local/bin/composer';

            // Install Laravel via Composer
            $commands = [
                "cd /var/www",
                "{$composerPath} create-project laravel/laravel {$domain->name} --prefer-dist",
                "cd {$installPath}",
                "chmod -R 775 storage bootstrap/cache",
                "chown -R {$domain->user->username}:{$domain->user->username} {$installPath}"
            ];

            foreach ($commands as $command) {
                $result = $this->ssh->exec($command);
                Log::info("Laravel install command: {$command} - Result: " . $result);
            }

            // Configure .env file
            $envConfig = $this->generateLaravelEnv($dbName, $dbUser, $dbPass, $domain->name);
            $this->ssh->exec("echo '{$envConfig}' > {$installPath}/.env");

            // Generate application key and run migrations
            $this->ssh->exec("cd {$installPath} && php artisan key:generate");
            $this->ssh->exec("cd {$installPath} && php artisan migrate --force");

            // Create application record
            $application = Application::create([
                'user_id' => $domain->user_id,
                'domain_id' => $domain->id,
                'name' => 'Laravel',
                'type' => 'framework',
                'version' => $this->getLaravelVersion($installPath),
                'status' => 'active',
                'install_path' => $installPath,
                'url' => "https://{$domain->name}",
                'admin_url' => null,
                'admin_username' => null,
                'admin_email' => $domain->user->email,
                'database_config' => [
                    'name' => $dbName,
                    'user' => $dbUser,
                    'host' => 'localhost',
                    'type' => 'mysql'
                ],
                'environment_config' => [
                    'APP_ENV' => 'production',
                    'APP_DEBUG' => false,
                    'APP_URL' => "https://{$domain->name}"
                ],
                'installed_at' => now(),
                'auto_update' => false,
                'notes' => 'Laravel framework installed automatically'
            ]);

            return [
                'success' => true,
                'application' => $application,
                'database' => [
                    'name' => $dbName,
                    'user' => $dbUser
                ],
                'message' => 'Laravel installed successfully'
            ];

        } catch (Exception $e) {
            Log::error("Laravel installation failed: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate database name
     */
    private function generateDatabaseName($domainName, $prefix = '')
    {
        $cleanDomain = preg_replace('/[^a-zA-Z0-9]/', '', $domainName);
        $dbName = $prefix ? "{$prefix}_{$cleanDomain}" : $cleanDomain;
        return substr($dbName, 0, 64); // MySQL database name limit
    }

    /**
     * Generate database user
     */
    private function generateDatabaseUser($domainName)
    {
        $cleanDomain = preg_replace('/[^a-zA-Z0-9]/', '', $domainName);
        return substr("user_{$cleanDomain}", 0, 32); // MySQL username limit
    }

    /**
     * Generate WordPress configuration
     */
    private function generateWordPressConfig($dbName, $dbUser, $dbPass, $domain)
    {
        $authKeys = $this->generateWordPressKeys();
        
        return "<?php
define('DB_NAME', '{$dbName}');
define('DB_USER', '{$dbUser}');
define('DB_PASSWORD', '{$dbPass}');
define('DB_HOST', 'localhost');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', '');

{$authKeys}

\$table_prefix = 'wp_';

define('WP_DEBUG', false);
define('WP_HOME', 'https://{$domain}');
define('WP_SITEURL', 'https://{$domain}');

if ( ! defined( 'ABSPATH' ) ) {
    define( 'ABSPATH', __DIR__ . '/' );
}

require_once ABSPATH . 'wp-settings.php';";
    }

    /**
     * Generate WordPress security keys
     */
    private function generateWordPressKeys()
    {
        $keys = [
            'AUTH_KEY', 'SECURE_AUTH_KEY', 'LOGGED_IN_KEY', 'NONCE_KEY',
            'AUTH_SALT', 'SECURE_AUTH_SALT', 'LOGGED_IN_SALT', 'NONCE_SALT'
        ];

        $keyStrings = [];
        foreach ($keys as $key) {
            $keyStrings[] = "define('{$key}', '" . Str::random(64) . "');";
        }

        return implode("\n", $keyStrings);
    }

    /**
     * Generate Laravel .env configuration
     */
    private function generateLaravelEnv($dbName, $dbUser, $dbPass, $domain)
    {
        return "APP_NAME=Laravel
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://{$domain}

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE={$dbName}
DB_USERNAME={$dbUser}
DB_PASSWORD={$dbPass}

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=\"hello@{$domain}\"
MAIL_FROM_NAME=\"\${APP_NAME}\"";
    }

    /**
     * Get WordPress version
     */
    private function getWordPressVersion($installPath)
    {
        try {
            $versionFile = "{$installPath}/wp-includes/version.php";
            $versionContent = $this->ssh->exec("grep '\$wp_version' {$versionFile} | head -1");

            if (preg_match("/wp_version = '([^']+)'/", $versionContent, $matches)) {
                return $matches[1];
            }

            return 'Unknown';
        } catch (Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Get Laravel version
     */
    private function getLaravelVersion($installPath)
    {
        try {
            $composerFile = "{$installPath}/composer.json";
            $composerContent = $this->ssh->exec("cat {$composerFile}");
            $composer = json_decode($composerContent, true);

            if (isset($composer['require']['laravel/framework'])) {
                return $composer['require']['laravel/framework'];
            }

            return 'Unknown';
        } catch (Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Install Drupal
     */
    public function installDrupal($domain, $options = [])
    {
        try {
            $installPath = "/var/www/{$domain->name}/public_html";
            $dbName = $this->generateDatabaseName($domain->name, 'drupal');
            $dbUser = $this->generateDatabaseUser($domain->name);
            $dbPass = Str::random(16);

            // Create database
            $dbResult = $this->cpanelService->createDatabase($dbName, $dbUser, $dbPass);
            if (!$dbResult['database']['status']) {
                throw new Exception('Failed to create database');
            }

            // Download and extract Drupal
            $this->ssh->exec("mkdir -p {$installPath}");
            $this->ssh->exec("cd {$installPath} && wget https://www.drupal.org/download-latest/tar.gz -O drupal.tar.gz");
            $this->ssh->exec("cd {$installPath} && tar -xzf drupal.tar.gz --strip-components=1");
            $this->ssh->exec("cd {$installPath} && rm drupal.tar.gz");

            // Set permissions
            $this->ssh->exec("chown -R {$domain->user->username}:{$domain->user->username} {$installPath}");
            $this->ssh->exec("chmod -R 755 {$installPath}");

            // Create application record
            $application = Application::create([
                'user_id' => $domain->user_id,
                'domain_id' => $domain->id,
                'name' => 'Drupal',
                'type' => 'cms',
                'version' => $this->getDrupalVersion($installPath),
                'status' => 'active',
                'install_path' => $installPath,
                'url' => "https://{$domain->name}",
                'admin_url' => "https://{$domain->name}/admin",
                'admin_username' => null,
                'admin_email' => $domain->user->email,
                'database_config' => [
                    'name' => $dbName,
                    'user' => $dbUser,
                    'host' => 'localhost',
                    'type' => 'mysql'
                ],
                'installed_at' => now(),
                'auto_update' => false,
                'notes' => 'Drupal CMS installed - Complete setup via web interface'
            ]);

            return [
                'success' => true,
                'application' => $application,
                'database' => [
                    'name' => $dbName,
                    'user' => $dbUser,
                    'password' => $dbPass
                ],
                'setup_url' => "https://{$domain->name}/install.php",
                'message' => 'Drupal installed successfully. Complete setup via web interface.'
            ];

        } catch (Exception $e) {
            Log::error("Drupal installation failed: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get Drupal version
     */
    private function getDrupalVersion($installPath)
    {
        try {
            $versionContent = $this->ssh->exec("grep 'version' {$installPath}/core/lib/Drupal.php | head -1");

            if (preg_match("/const VERSION = '([^']+)'/", $versionContent, $matches)) {
                return $matches[1];
            }

            return 'Unknown';
        } catch (Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Uninstall application
     */
    public function uninstallApplication($applicationId)
    {
        try {
            $application = Application::find($applicationId);
            if (!$application) {
                throw new Exception('Application not found');
            }

            // Remove files
            if ($application->install_path) {
                $this->ssh->exec("rm -rf {$application->install_path}");
            }

            // Remove database if configured
            if ($application->database_config && isset($application->database_config['name'])) {
                $dbName = $application->database_config['name'];
                $this->cpanelService->deleteDatabase($dbName);
            }

            // Delete application record
            $application->delete();

            return [
                'success' => true,
                'message' => 'Application uninstalled successfully'
            ];

        } catch (Exception $e) {
            Log::error("Application uninstall failed: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update application
     */
    public function updateApplication($applicationId)
    {
        try {
            $application = Application::find($applicationId);
            if (!$application) {
                throw new Exception('Application not found');
            }

            $application->update(['status' => 'updating']);

            switch ($application->type) {
                case 'cms':
                    if ($application->name === 'WordPress') {
                        $result = $this->updateWordPress($application);
                    } elseif ($application->name === 'Drupal') {
                        $result = $this->updateDrupal($application);
                    }
                    break;
                case 'framework':
                    if ($application->name === 'Laravel') {
                        $result = $this->updateLaravel($application);
                    }
                    break;
                default:
                    throw new Exception('Update not supported for this application type');
            }

            if ($result['success']) {
                $application->update([
                    'status' => 'active',
                    'version' => $result['new_version'] ?? $application->version,
                    'last_updated_at' => now()
                ]);
            } else {
                $application->update(['status' => 'error']);
            }

            return $result;

        } catch (Exception $e) {
            Log::error("Application update failed: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update WordPress
     */
    private function updateWordPress($application)
    {
        try {
            $installPath = $application->install_path;

            // Create backup before update
            $backupPath = "/tmp/wp_backup_" . time();
            $this->ssh->exec("cp -r {$installPath} {$backupPath}");

            // Update WordPress via WP-CLI if available
            $wpCliInstalled = trim($this->ssh->exec("which wp")) !== '';
            if ($wpCliInstalled) {
                $updateResult = $this->ssh->exec("cd {$installPath} && wp core update --allow-root");
                $newVersion = $this->getWordPressVersion($installPath);

                return [
                    'success' => true,
                    'new_version' => $newVersion,
                    'message' => 'WordPress updated successfully',
                    'backup_path' => $backupPath
                ];
            } else {
                throw new Exception('WP-CLI not available for automatic updates');
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
