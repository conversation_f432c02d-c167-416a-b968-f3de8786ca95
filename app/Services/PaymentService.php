<?php

namespace App\Services;

use App\Models\User;
use App\Models\PaymentMethod;
use App\Models\Transaction;
use App\Models\PaymentGateway;
use App\Models\Subscription;
use App\Models\Invoice;
use App\Services\Gateways\StripeGatewayService;
use App\Services\Gateways\PayPalGatewayService;
use Illuminate\Support\Facades\Log;
use Exception;

class PaymentService
{
    protected $gatewayServices = [];

    public function __construct()
    {
        $this->gatewayServices = [
            'stripe' => new StripeGatewayService(),
            'paypal' => new PayPalGatewayService(),
        ];
    }

    /**
     * Process payment for subscription
     */
    public function processSubscriptionPayment(User $user, Subscription $subscription, PaymentMethod $paymentMethod = null): array
    {
        try {
            // Use default payment method if none provided
            if (!$paymentMethod) {
                $paymentMethod = $user->defaultPaymentMethod();
                if (!$paymentMethod) {
                    throw new Exception('No payment method available');
                }
            }

            // Get gateway service
            $gatewayService = $this->getGatewayService($paymentMethod->gateway);

            // Create transaction record
            $transaction = Transaction::create([
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'payment_method_id' => $paymentMethod->id,
                'type' => 'payment',
                'status' => 'pending',
                'amount' => $subscription->amount,
                'currency' => $subscription->currency,
                'gateway' => $paymentMethod->gateway,
                'payment_method_type' => $paymentMethod->type,
                'description' => "Subscription payment for {$subscription->plan->name}",
            ]);

            // Process payment through gateway
            $result = $gatewayService->processPayment($paymentMethod, $subscription->amount, [
                'description' => $transaction->description,
                'metadata' => [
                    'transaction_id' => $transaction->transaction_id,
                    'subscription_id' => $subscription->id,
                    'user_id' => $user->id,
                ],
            ]);

            if ($result['success']) {
                // Update transaction as successful
                $transaction->markAsCompleted($result['gateway_response']);
                
                // Update subscription
                $subscription->renew();

                Log::info('Subscription payment processed successfully', [
                    'transaction_id' => $transaction->transaction_id,
                    'subscription_id' => $subscription->id,
                    'amount' => $subscription->amount,
                ]);

                return [
                    'success' => true,
                    'transaction' => $transaction,
                    'message' => 'Payment processed successfully',
                ];
            } else {
                // Mark transaction as failed
                $transaction->markAsFailed($result['error'], $result['error_code'] ?? null);

                return [
                    'success' => false,
                    'transaction' => $transaction,
                    'error' => $result['error'],
                ];
            }

        } catch (Exception $e) {
            Log::error('Subscription payment failed', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Process one-time payment
     */
    public function processOneTimePayment(User $user, float $amount, PaymentMethod $paymentMethod, array $options = []): array
    {
        try {
            // Get gateway service
            $gatewayService = $this->getGatewayService($paymentMethod->gateway);

            // Create transaction record
            $transaction = Transaction::create([
                'user_id' => $user->id,
                'payment_method_id' => $paymentMethod->id,
                'type' => 'payment',
                'status' => 'pending',
                'amount' => $amount,
                'currency' => $options['currency'] ?? 'USD',
                'gateway' => $paymentMethod->gateway,
                'payment_method_type' => $paymentMethod->type,
                'description' => $options['description'] ?? 'One-time payment',
            ]);

            // Process payment through gateway
            $result = $gatewayService->processPayment($paymentMethod, $amount, array_merge($options, [
                'metadata' => [
                    'transaction_id' => $transaction->transaction_id,
                    'user_id' => $user->id,
                ],
            ]));

            if ($result['success']) {
                $transaction->markAsCompleted($result['gateway_response']);

                return [
                    'success' => true,
                    'transaction' => $transaction,
                    'message' => 'Payment processed successfully',
                ];
            } else {
                $transaction->markAsFailed($result['error'], $result['error_code'] ?? null);

                return [
                    'success' => false,
                    'transaction' => $transaction,
                    'error' => $result['error'],
                ];
            }

        } catch (Exception $e) {
            Log::error('One-time payment failed', [
                'user_id' => $user->id,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Add payment method for user
     */
    public function addPaymentMethod(User $user, array $paymentData, string $gateway = 'stripe'): array
    {
        try {
            $gatewayService = $this->getGatewayService($gateway);

            // Create payment method through gateway
            $result = $gatewayService->createPaymentMethod($user, $paymentData);

            if ($result['success']) {
                // Store payment method in database
                $paymentMethod = PaymentMethod::create([
                    'user_id' => $user->id,
                    'type' => $result['type'],
                    'gateway' => $gateway,
                    'gateway_payment_method_id' => $result['gateway_payment_method_id'],
                    'gateway_customer_id' => $result['gateway_customer_id'],
                    'last_four' => $result['last_four'] ?? null,
                    'brand' => $result['brand'] ?? null,
                    'exp_month' => $result['exp_month'] ?? null,
                    'exp_year' => $result['exp_year'] ?? null,
                    'cardholder_name' => $result['cardholder_name'] ?? null,
                    'paypal_email' => $result['paypal_email'] ?? null,
                    'is_default' => $user->paymentMethods()->count() === 0, // First payment method is default
                ]);

                return [
                    'success' => true,
                    'payment_method' => $paymentMethod,
                    'message' => 'Payment method added successfully',
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result['error'],
                ];
            }

        } catch (Exception $e) {
            Log::error('Failed to add payment method', [
                'user_id' => $user->id,
                'gateway' => $gateway,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Remove payment method
     */
    public function removePaymentMethod(PaymentMethod $paymentMethod): array
    {
        try {
            $gatewayService = $this->getGatewayService($paymentMethod->gateway);

            // Remove from gateway
            $result = $gatewayService->deletePaymentMethod($paymentMethod);

            if ($result['success']) {
                // If this was the default payment method, set another as default
                if ($paymentMethod->is_default) {
                    $nextPaymentMethod = $paymentMethod->user->paymentMethods()
                        ->where('id', '!=', $paymentMethod->id)
                        ->where('is_active', true)
                        ->first();
                    
                    if ($nextPaymentMethod) {
                        $nextPaymentMethod->setAsDefault();
                    }
                }

                // Remove from database
                $paymentMethod->delete();

                return [
                    'success' => true,
                    'message' => 'Payment method removed successfully',
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result['error'],
                ];
            }

        } catch (Exception $e) {
            Log::error('Failed to remove payment method', [
                'payment_method_id' => $paymentMethod->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Process refund
     */
    public function processRefund(Transaction $transaction, float $amount = null): array
    {
        try {
            $refundAmount = $amount ?? $transaction->amount;
            $gatewayService = $this->getGatewayService($transaction->gateway);

            // Process refund through gateway
            $result = $gatewayService->processRefund($transaction, $refundAmount);

            if ($result['success']) {
                // Create refund transaction
                $refundTransaction = Transaction::create([
                    'user_id' => $transaction->user_id,
                    'subscription_id' => $transaction->subscription_id,
                    'invoice_id' => $transaction->invoice_id,
                    'type' => 'refund',
                    'status' => 'completed',
                    'amount' => -$refundAmount, // Negative amount for refund
                    'currency' => $transaction->currency,
                    'gateway' => $transaction->gateway,
                    'gateway_transaction_id' => $result['gateway_transaction_id'],
                    'payment_method_type' => $transaction->payment_method_type,
                    'description' => "Refund for transaction {$transaction->transaction_id}",
                    'processed_at' => now(),
                ]);

                // Update original transaction if fully refunded
                if ($refundAmount >= $transaction->amount) {
                    $transaction->markAsRefunded();
                }

                return [
                    'success' => true,
                    'refund_transaction' => $refundTransaction,
                    'message' => 'Refund processed successfully',
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result['error'],
                ];
            }

        } catch (Exception $e) {
            Log::error('Refund processing failed', [
                'transaction_id' => $transaction->transaction_id,
                'amount' => $refundAmount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get gateway service instance
     */
    protected function getGatewayService(string $gateway)
    {
        if (!isset($this->gatewayServices[$gateway])) {
            throw new Exception("Unsupported payment gateway: {$gateway}");
        }

        return $this->gatewayServices[$gateway];
    }

    /**
     * Get available payment gateways
     */
    public function getAvailableGateways(): array
    {
        return PaymentGateway::active()->ordered()->get()->toArray();
    }

    /**
     * Calculate fees for amount and gateway
     */
    public function calculateFees(float $amount, string $gateway): array
    {
        $paymentGateway = PaymentGateway::where('slug', $gateway)->first();
        
        if (!$paymentGateway) {
            return ['total_fee' => 0, 'net_amount' => $amount];
        }

        return $paymentGateway->calculateFees($amount);
    }
}
