<?php

namespace App\Services;

use App\Models\Server;
use Exception;
use Illuminate\Support\Facades\Log;

class ServerManagementService
{
    private $sshService;

    public function __construct()
    {
        $this->sshService = new SSHService();
    }

    /**
     * Connect to a server and update its real-time data
     */
    public function connectAndUpdateServer(Server $server)
    {
        try {
            // Decrypt password if exists
            $password = $server->password ? decrypt($server->password) : null;
            
            // Connect to server
            $this->sshService->connect(
                $server->ip_address,
                $server->port,
                $server->username,
                $password,
                $server->ssh_key
            );

            // Get real system information
            $systemInfo = $this->sshService->getSystemInfo();
            $services = $this->sshService->getServices();

            // Update server with real data
            $server->update([
                'status' => 'online',
                'last_ping' => now(),
                'operating_system' => $systemInfo['os'] ?? $server->operating_system,
                'monitoring_data' => [
                    'cpu_usage' => $systemInfo['cpu_usage'] ?? 0,
                    'ram_usage' => $systemInfo['memory']['usage_percent'] ?? 0,
                    'disk_usage' => $systemInfo['disk']['usage_percent'] ?? 0,
                    'uptime' => $systemInfo['uptime'] ?? 'Unknown',
                    'load_average' => $systemInfo['load_average'] ?? 0,
                    'memory_total' => $systemInfo['memory']['total'] ?? 0,
                    'memory_used' => $systemInfo['memory']['used'] ?? 0,
                    'memory_available' => $systemInfo['memory']['available'] ?? 0,
                    'disk_total' => $systemInfo['disk']['total'] ?? 'Unknown',
                    'disk_used' => $systemInfo['disk']['used'] ?? 'Unknown',
                    'disk_available' => $systemInfo['disk']['available'] ?? 'Unknown',
                    'cpu_model' => $systemInfo['cpu']['model'] ?? 'Unknown',
                    'cpu_cores' => $systemInfo['cpu']['cores'] ?? 0,
                    'network_interfaces' => $systemInfo['network_interfaces'] ?? [],
                    'services' => $services,
                    'last_updated' => now()->toISOString()
                ]
            ]);

            return [
                'success' => true,
                'message' => 'Server connected and updated successfully',
                'data' => $systemInfo
            ];

        } catch (Exception $e) {
            // Update server as offline
            $server->update([
                'status' => 'offline',
                'last_ping' => now()
            ]);

            Log::error("Failed to connect to server {$server->name}: " . $e->getMessage());

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Test server connection
     */
    public function testConnection(Server $server)
    {
        try {
            $password = $server->password ? decrypt($server->password) : null;
            
            $this->sshService->connect(
                $server->ip_address,
                $server->port,
                $server->username,
                $password,
                $server->ssh_key
            );

            $ping = $this->sshService->ping();
            
            return [
                'success' => true,
                'message' => 'Connection successful',
                'online' => $ping
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'online' => false
            ];
        }
    }

    /**
     * Restart a service on the server
     */
    public function restartService(Server $server, $serviceName)
    {
        try {
            $password = $server->password ? decrypt($server->password) : null;
            
            $this->sshService->connect(
                $server->ip_address,
                $server->port,
                $server->username,
                $password,
                $server->ssh_key
            );

            $result = $this->sshService->restartService($serviceName);
            
            // Update monitoring data after restart
            $this->connectAndUpdateServer($server);

            return [
                'success' => $result,
                'message' => $result ? "Service {$serviceName} restarted successfully" : "Failed to restart service {$serviceName}"
            ];

        } catch (Exception $e) {
            Log::error("Failed to restart service {$serviceName} on server {$server->name}: " . $e->getMessage());
            
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Execute custom command on server
     */
    public function executeCommand(Server $server, $command)
    {
        try {
            $password = $server->password ? decrypt($server->password) : null;
            
            $this->sshService->connect(
                $server->ip_address,
                $server->port,
                $server->username,
                $password,
                $server->ssh_key
            );

            $output = $this->sshService->execute($command);
            
            return [
                'success' => true,
                'output' => $output,
                'command' => $command
            ];

        } catch (Exception $e) {
            Log::error("Failed to execute command on server {$server->name}: " . $e->getMessage());
            
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'command' => $command
            ];
        }
    }

    /**
     * Create a new website on the server
     */
    public function createWebsite(Server $server, $domain, $username, $password = null)
    {
        try {
            $serverPassword = $server->password ? decrypt($server->password) : null;
            
            $this->sshService->connect(
                $server->ip_address,
                $server->port,
                $server->username,
                $serverPassword,
                $server->ssh_key
            );

            // Create user for the website
            if ($password) {
                $this->sshService->createUser($username, $password);
            } else {
                $this->sshService->createUser($username);
            }

            // Create website directory
            $webRoot = "/var/www/{$domain}";
            $this->sshService->createDirectory($webRoot, $username, '755');
            $this->sshService->createDirectory("{$webRoot}/public_html", $username, '755');
            $this->sshService->createDirectory("{$webRoot}/logs", $username, '755');

            // Create basic index.html
            $indexContent = "<!DOCTYPE html>\n<html>\n<head>\n    <title>Welcome to {$domain}</title>\n</head>\n<body>\n    <h1>Website is ready!</h1>\n    <p>Your website {$domain} has been created successfully.</p>\n</body>\n</html>";
            
            // Write index file (simplified - in real implementation you'd use proper file upload)
            $this->sshService->execute("echo '{$indexContent}' | sudo tee {$webRoot}/public_html/index.html");
            $this->sshService->execute("sudo chown {$username}:{$username} {$webRoot}/public_html/index.html");

            return [
                'success' => true,
                'message' => "Website {$domain} created successfully",
                'web_root' => $webRoot,
                'username' => $username
            ];

        } catch (Exception $e) {
            Log::error("Failed to create website on server {$server->name}: " . $e->getMessage());
            
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Create database on server
     */
    public function createDatabase(Server $server, $dbName, $dbUser, $dbPassword)
    {
        try {
            $serverPassword = $server->password ? decrypt($server->password) : null;
            
            $this->sshService->connect(
                $server->ip_address,
                $server->port,
                $server->username,
                $serverPassword,
                $server->ssh_key
            );

            // Create MySQL database and user
            $commands = [
                "sudo mysql -e \"CREATE DATABASE IF NOT EXISTS {$dbName};\"",
                "sudo mysql -e \"CREATE USER IF NOT EXISTS '{$dbUser}'@'localhost' IDENTIFIED BY '{$dbPassword}';\"",
                "sudo mysql -e \"GRANT ALL PRIVILEGES ON {$dbName}.* TO '{$dbUser}'@'localhost';\"",
                "sudo mysql -e \"FLUSH PRIVILEGES;\""
            ];

            foreach ($commands as $command) {
                $this->sshService->execute($command);
            }

            return [
                'success' => true,
                'message' => "Database {$dbName} created successfully",
                'database' => $dbName,
                'username' => $dbUser
            ];

        } catch (Exception $e) {
            Log::error("Failed to create database on server {$server->name}: " . $e->getMessage());
            
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Install SSL certificate
     */
    public function installSSL(Server $server, $domain)
    {
        try {
            $serverPassword = $server->password ? decrypt($server->password) : null;
            
            $this->sshService->connect(
                $server->ip_address,
                $server->port,
                $server->username,
                $serverPassword,
                $server->ssh_key
            );

            // Install Let's Encrypt SSL using certbot
            $commands = [
                "sudo apt update",
                "sudo apt install -y certbot python3-certbot-apache",
                "sudo certbot --apache -d {$domain} --non-interactive --agree-tos --email admin@{$domain}"
            ];

            foreach ($commands as $command) {
                $output = $this->sshService->execute($command);
                Log::info("SSL installation command output: " . $output);
            }

            return [
                'success' => true,
                'message' => "SSL certificate installed successfully for {$domain}"
            ];

        } catch (Exception $e) {
            Log::error("Failed to install SSL on server {$server->name}: " . $e->getMessage());
            
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get server logs
     */
    public function getServerLogs(Server $server, $logType = 'system', $lines = 100)
    {
        try {
            $serverPassword = $server->password ? decrypt($server->password) : null;
            
            $this->sshService->connect(
                $server->ip_address,
                $server->port,
                $server->username,
                $serverPassword,
                $server->ssh_key
            );

            $logFiles = [
                'system' => '/var/log/syslog',
                'apache' => '/var/log/apache2/error.log',
                'nginx' => '/var/log/nginx/error.log',
                'mysql' => '/var/log/mysql/error.log',
                'auth' => '/var/log/auth.log'
            ];

            $logFile = $logFiles[$logType] ?? $logFiles['system'];
            $output = $this->sshService->execute("sudo tail -{$lines} {$logFile}");

            return [
                'success' => true,
                'logs' => $output,
                'log_type' => $logType,
                'lines' => $lines
            ];

        } catch (Exception $e) {
            Log::error("Failed to get logs from server {$server->name}: " . $e->getMessage());
            
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}
