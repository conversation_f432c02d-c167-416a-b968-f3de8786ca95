<?php

namespace App\Services;

use App\Models\Application;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use phpseclib3\Net\SSH2;

class ApplicationMonitoringService
{
    private $ssh;
    private $application;

    public function __construct(Application $application = null)
    {
        $this->application = $application;
        if ($application && $application->domain && $application->domain->server) {
            $this->initializeSSH();
        }
    }

    /**
     * Initialize SSH connection
     */
    private function initializeSSH()
    {
        try {
            $server = $this->application->domain->server;
            $this->ssh = new SSH2($server->ip_address, $server->ssh_port ?? 22);
            
            if ($server->ssh_key) {
                $key = \phpseclib3\Crypt\PublicKeyLoader::load($server->ssh_key);
                if (!$this->ssh->login($server->ssh_username, $key)) {
                    throw new Exception('SSH key authentication failed');
                }
            } else {
                if (!$this->ssh->login($server->ssh_username, $server->ssh_password)) {
                    throw new Exception('SSH password authentication failed');
                }
            }
        } catch (Exception $e) {
            Log::error("SSH connection failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Check application health
     */
    public function checkApplicationHealth($applicationId = null)
    {
        try {
            $application = $applicationId ? Application::find($applicationId) : $this->application;
            
            if (!$application) {
                throw new Exception('Application not found');
            }

            $healthChecks = [
                'url_accessible' => $this->checkUrlAccessibility($application),
                'files_exist' => $this->checkFilesExist($application),
                'database_connected' => $this->checkDatabaseConnection($application),
                'permissions_correct' => $this->checkFilePermissions($application),
                'disk_space' => $this->checkDiskSpace($application),
            ];

            // Calculate overall health score
            $healthScore = $this->calculateHealthScore($healthChecks);
            
            // Update application status based on health
            $status = $this->determineApplicationStatus($healthScore, $healthChecks);
            
            $application->update([
                'status' => $status,
                'last_health_check' => now(),
            ]);

            return [
                'success' => true,
                'application_id' => $application->id,
                'health_score' => $healthScore,
                'status' => $status,
                'checks' => $healthChecks,
                'recommendations' => $this->generateRecommendations($healthChecks),
            ];

        } catch (Exception $e) {
            Log::error("Health check failed: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check if application URL is accessible
     */
    private function checkUrlAccessibility($application)
    {
        try {
            if (!$application->url) {
                return ['status' => 'skipped', 'message' => 'No URL configured'];
            }

            $response = Http::timeout(10)->get($application->url);
            
            if ($response->successful()) {
                return [
                    'status' => 'pass',
                    'message' => 'URL accessible',
                    'response_code' => $response->status(),
                    'response_time' => $response->transferStats->getTransferTime() ?? 0,
                ];
            } else {
                return [
                    'status' => 'fail',
                    'message' => 'URL not accessible',
                    'response_code' => $response->status(),
                ];
            }
        } catch (Exception $e) {
            return [
                'status' => 'fail',
                'message' => 'URL check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check if application files exist
     */
    private function checkFilesExist($application)
    {
        try {
            if (!$application->install_path || !$this->ssh) {
                return ['status' => 'skipped', 'message' => 'No install path or SSH connection'];
            }

            $installPath = $application->install_path;
            $pathExists = trim($this->ssh->exec("test -d '{$installPath}' && echo 'exists'")) === 'exists';
            
            if ($pathExists) {
                // Check for key files based on application type
                $keyFiles = $this->getKeyFilesForApplication($application);
                $missingFiles = [];
                
                foreach ($keyFiles as $file) {
                    $fileExists = trim($this->ssh->exec("test -f '{$installPath}/{$file}' && echo 'exists'")) === 'exists';
                    if (!$fileExists) {
                        $missingFiles[] = $file;
                    }
                }
                
                if (empty($missingFiles)) {
                    return ['status' => 'pass', 'message' => 'All key files exist'];
                } else {
                    return [
                        'status' => 'warning',
                        'message' => 'Some key files missing',
                        'missing_files' => $missingFiles,
                    ];
                }
            } else {
                return ['status' => 'fail', 'message' => 'Install directory does not exist'];
            }
        } catch (Exception $e) {
            return [
                'status' => 'fail',
                'message' => 'File check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check database connection
     */
    private function checkDatabaseConnection($application)
    {
        try {
            if (!$application->database_config) {
                return ['status' => 'skipped', 'message' => 'No database configured'];
            }

            $dbConfig = $application->database_config;
            
            if ($dbConfig['type'] === 'mysql') {
                $command = "mysql -h{$dbConfig['host']} -u{$dbConfig['user']} -p{$dbConfig['password']} -e 'SELECT 1' {$dbConfig['name']} 2>/dev/null";
                $result = trim($this->ssh->exec($command));
                
                if (strpos($result, '1') !== false) {
                    return ['status' => 'pass', 'message' => 'Database connection successful'];
                } else {
                    return ['status' => 'fail', 'message' => 'Database connection failed'];
                }
            }
            
            return ['status' => 'skipped', 'message' => 'Database type not supported for health check'];
        } catch (Exception $e) {
            return [
                'status' => 'fail',
                'message' => 'Database check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check file permissions
     */
    private function checkFilePermissions($application)
    {
        try {
            if (!$application->install_path || !$this->ssh) {
                return ['status' => 'skipped', 'message' => 'No install path or SSH connection'];
            }

            $installPath = $application->install_path;
            $issues = [];

            // Check writable directories based on application type
            $writableDirs = $this->getWritableDirectories($application);
            
            foreach ($writableDirs as $dir) {
                $fullPath = "{$installPath}/{$dir}";
                $isWritable = trim($this->ssh->exec("test -w '{$fullPath}' && echo 'writable'")) === 'writable';
                
                if (!$isWritable) {
                    $issues[] = "{$dir} is not writable";
                }
            }

            if (empty($issues)) {
                return ['status' => 'pass', 'message' => 'File permissions are correct'];
            } else {
                return [
                    'status' => 'warning',
                    'message' => 'Permission issues found',
                    'issues' => $issues,
                ];
            }
        } catch (Exception $e) {
            return [
                'status' => 'fail',
                'message' => 'Permission check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check available disk space
     */
    private function checkDiskSpace($application)
    {
        try {
            if (!$application->install_path || !$this->ssh) {
                return ['status' => 'skipped', 'message' => 'No install path or SSH connection'];
            }

            $installPath = $application->install_path;
            $diskUsage = trim($this->ssh->exec("df '{$installPath}' | tail -1 | awk '{print $5}' | sed 's/%//'"));
            
            if (is_numeric($diskUsage)) {
                $usagePercent = (int) $diskUsage;
                
                if ($usagePercent < 80) {
                    return [
                        'status' => 'pass',
                        'message' => 'Sufficient disk space',
                        'usage_percent' => $usagePercent,
                    ];
                } elseif ($usagePercent < 95) {
                    return [
                        'status' => 'warning',
                        'message' => 'Disk space running low',
                        'usage_percent' => $usagePercent,
                    ];
                } else {
                    return [
                        'status' => 'fail',
                        'message' => 'Disk space critically low',
                        'usage_percent' => $usagePercent,
                    ];
                }
            } else {
                return ['status' => 'fail', 'message' => 'Could not determine disk usage'];
            }
        } catch (Exception $e) {
            return [
                'status' => 'fail',
                'message' => 'Disk space check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get key files for application type
     */
    private function getKeyFilesForApplication($application)
    {
        switch ($application->name) {
            case 'WordPress':
                return ['wp-config.php', 'wp-load.php', 'wp-settings.php'];
            case 'Laravel':
                return ['.env', 'artisan', 'composer.json'];
            case 'Drupal':
                return ['index.php', 'core/lib/Drupal.php'];
            default:
                return ['index.php', 'index.html'];
        }
    }

    /**
     * Get writable directories for application type
     */
    private function getWritableDirectories($application)
    {
        switch ($application->name) {
            case 'WordPress':
                return ['wp-content/uploads', 'wp-content/themes', 'wp-content/plugins'];
            case 'Laravel':
                return ['storage', 'bootstrap/cache'];
            case 'Drupal':
                return ['sites/default/files', 'sites/default'];
            default:
                return [];
        }
    }

    /**
     * Calculate overall health score
     */
    private function calculateHealthScore($healthChecks)
    {
        $totalChecks = 0;
        $passedChecks = 0;
        $warningChecks = 0;

        foreach ($healthChecks as $check) {
            if ($check['status'] !== 'skipped') {
                $totalChecks++;
                if ($check['status'] === 'pass') {
                    $passedChecks++;
                } elseif ($check['status'] === 'warning') {
                    $warningChecks++;
                }
            }
        }

        if ($totalChecks === 0) {
            return 0;
        }

        // Pass = 100%, Warning = 50%, Fail = 0%
        $score = (($passedChecks * 100) + ($warningChecks * 50)) / $totalChecks;
        return round($score);
    }

    /**
     * Determine application status based on health score
     */
    private function determineApplicationStatus($healthScore, $healthChecks)
    {
        // Check for critical failures
        foreach ($healthChecks as $check) {
            if ($check['status'] === 'fail' && in_array($check, ['url_accessible', 'files_exist'])) {
                return 'error';
            }
        }

        if ($healthScore >= 80) {
            return 'active';
        } elseif ($healthScore >= 50) {
            return 'warning';
        } else {
            return 'error';
        }
    }

    /**
     * Generate recommendations based on health checks
     */
    private function generateRecommendations($healthChecks)
    {
        $recommendations = [];

        foreach ($healthChecks as $checkName => $check) {
            if ($check['status'] === 'fail' || $check['status'] === 'warning') {
                switch ($checkName) {
                    case 'url_accessible':
                        $recommendations[] = 'Check web server configuration and DNS settings';
                        break;
                    case 'files_exist':
                        $recommendations[] = 'Restore missing application files from backup';
                        break;
                    case 'database_connected':
                        $recommendations[] = 'Check database credentials and server status';
                        break;
                    case 'permissions_correct':
                        $recommendations[] = 'Fix file permissions using chmod/chown commands';
                        break;
                    case 'disk_space':
                        $recommendations[] = 'Free up disk space or upgrade storage';
                        break;
                }
            }
        }

        return $recommendations;
    }

    /**
     * Monitor all applications
     */
    public static function monitorAllApplications()
    {
        $applications = Application::where('status', '!=', 'inactive')->get();
        $results = [];

        foreach ($applications as $application) {
            $monitor = new self($application);
            $result = $monitor->checkApplicationHealth();
            $results[] = $result;
        }

        return $results;
    }
}
