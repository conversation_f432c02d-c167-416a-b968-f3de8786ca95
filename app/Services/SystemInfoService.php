<?php

namespace App\Services;

class SystemInfoService
{
    /**
     * Get CPU usage percentage
     *
     * @return float
     */
    public function getCpuUsage(): float
    {
        try {
            // First try with 'top' command (works on most Linux systems)
            $cmd = "top -bn1 | grep 'Cpu(s)' | sed 's/.*, *\\([0-9.]*\\)%* id.*/\\1/' | awk '{print 100 - $1}' 2>/dev/null";
            $output = shell_exec($cmd);
            
            // If that fails, try alternative method (might work better in containers)
            if ($output === null || !is_numeric(trim($output))) {
                // Try using /proc/stat which is available in most containers
                $cmd = "grep 'cpu ' /proc/stat | awk '{usage=($2+$4)*100/($2+$4+$5)} END {print usage}' 2>/dev/null";
                $output = shell_exec($cmd);
            }
            
            if ($output === null || !is_numeric(trim($output))) {
                return 0.0;
            }
            
            $cpuUsage = (float) trim($output);
            return round($cpuUsage, 2);
        } catch (\Exception $e) {
            return 0.0;
        }
    }

    /**
     * Get memory usage information
     *
     * @return array
     */
    public function getMemoryUsage(): array
    {
        try {
            $memInfo = shell_exec("free -m 2>/dev/null");
            
            
            if (empty($memInfo)) {
                $memData = shell_exec("cat /proc/meminfo 2>/dev/null");
                if (!empty($memData)) {
                    $lines = explode("\n", $memData);
                    $memTotal = 0;
                    $memFree = 0;
                    $memAvailable = 0;
                    
                    foreach ($lines as $line) {
                        if (strpos($line, 'MemTotal:') !== false) {
                            $memTotal = (int)(preg_replace('/[^0-9]/', '', $line)) / 1024;
                        } else if (strpos($line, 'MemFree:') !== false) {
                            $memFree = (int)(preg_replace('/[^0-9]/', '', $line)) / 1024;
                        } else if (strpos($line, 'MemAvailable:') !== false) {
                            $memAvailable = (int)(preg_replace('/[^0-9]/', '', $line)) / 1024;
                        }
                    }
                    
                    $usedMem = $memTotal - $memAvailable;
                    $percentUsed = $memTotal > 0 ? round(($usedMem / $memTotal) * 100, 2) : 0;
                    
                    return [
                        'total' => round($memTotal),
                        'used' => round($usedMem),
                        'free' => round($memAvailable),
                        'percent_used' => $percentUsed,
                    ];
                }
                
                return [
                    'total' => 0,
                    'used' => 0,
                    'free' => 0,
                    'percent_used' => 0,
                ];
            }
            
            $lines = explode("\n", $memInfo);
            
            if (!isset($lines[1])) {
                return [
                    'total' => 0,
                    'used' => 0,
                    'free' => 0,
                    'percent_used' => 0,
                ];
            }
            
            
            $parts = array_values(array_filter(explode(' ', trim($lines[1])), 'strlen'));
            
            
            if (count($parts) < 3) {
                return [
                    'total' => 0,
                    'used' => 0,
                    'free' => 0,
                    'percent_used' => 0,
                ];
            }
            
            $totalMem = (int) $parts[1];
            $usedMem = (int) $parts[2];
            $percentUsed = $totalMem > 0 ? round(($usedMem / $totalMem) * 100, 2) : 0;
            
            return [
                'total' => $totalMem,
                'used' => $usedMem,
                'free' => $totalMem - $usedMem,
                'percent_used' => $percentUsed,
            ];
        } catch (\Exception $e) {
            return [
                'total' => 0,
                'used' => 0,
                'free' => 0,
                'percent_used' => 0,
            ];
        }
    }

    /**
     * Get disk usage information
     *
     * @return array
     */
    public function getDiskUsage(): array
    {
        try {
            
            $diskInfo = shell_exec("df -h / 2>/dev/null | tail -n 1");
            
            
            if (empty($diskInfo)) {
                $diskInfo = shell_exec("df -h /app 2>/dev/null | tail -n 1");
            }
            
            
            if (empty($diskInfo)) {
                $diskInfo = shell_exec("df -h 2>/dev/null | grep -v tmpfs | grep -v 'none' | tail -n 1");
            }
            
            if (empty($diskInfo)) {
                throw new \Exception("Unable to retrieve disk information");
            }
            
            $parts = preg_split('/\s+/', trim($diskInfo));
            
        
            if (count($parts) < 6) {
                throw new \Exception("Disk information format is not as expected");
            }
            
            return [
                'filesystem' => $parts[0] ?? 'Unknown',
                'size' => $parts[1] ?? '0',
                'used' => $parts[2] ?? '0',
                'available' => $parts[3] ?? '0',
                'percent_used' => isset($parts[4]) ? (int) str_replace('%', '', $parts[4]) : 0,
                'mounted_on' => $parts[5] ?? '/',
            ];
        } catch (\Exception $e) {
            return [
                'filesystem' => 'Docker Volume',
                'size' => '0',
                'used' => '0',
                'available' => '0',
                'percent_used' => 0,
                'mounted_on' => '/',
            ];
        }
    }

    /**
     * Get system uptime
     *
     * @return string
     */
    public function getUptime(): string
    {
        try {
            
            $uptime = shell_exec("uptime -p 2>/dev/null");
            
            
            if (empty(trim($uptime))) {
                $rawUptime = shell_exec("cat /proc/uptime 2>/dev/null");
                if (!empty($rawUptime)) {
                    $uptimeSeconds = (float) explode(' ', $rawUptime)[0];
                    
                    
                    $days = floor($uptimeSeconds / 86400);
                    $hours = floor(($uptimeSeconds % 86400) / 3600);
                    $minutes = floor(($uptimeSeconds % 3600) / 60);
                    
                    $uptime = '';
                    if ($days > 0) {
                        $uptime .= "$days day" . ($days > 1 ? 's' : '') . ' ';
                    }
                    if ($hours > 0 || $days > 0) {
                        $uptime .= "$hours hour" . ($hours > 1 ? 's' : '') . ' ';
                    }
                    $uptime .= "$minutes minute" . ($minutes > 1 ? 's' : '');
                    
                    return trim($uptime);
                }
            }
            
            return $uptime ? trim($uptime) : 'Docker container (uptime unknown)';
        } catch (\Exception $e) {
            return 'Docker container (uptime unknown)';
        }
    }

    /**
     * Get Linux version information
     *
     * @return string
     */
    public function getLinuxVersion(): string
    {
        try {
            
            $version = shell_exec("cat /etc/os-release 2>/dev/null | grep PRETTY_NAME | cut -d '\"' -f 2");
            
        
            if (empty(trim($version))) {
                
                $version = shell_exec("cat /proc/version 2>/dev/null");
            }
            
            // If still empty, try uname command
            if (empty(trim($version))) {
                $version = shell_exec("uname -a 2>/dev/null");
            }
            
            return $version ? trim($version) : 'Unknown (Docker)';
        } catch (\Exception $e) {
            return 'Unknown (Docker)';
        }
    }

    /**
     * Get PHP version
     *
     * @return string
     */
    public function getPhpVersion(): string
    {
        return phpversion();
    }

    /**
     * Get all system information
     *
     * @return array
     */
    public function getAllInfo(): array
    {
        try {
            return [
                'cpu' => $this->getCpuUsage(),
                'memory' => $this->getMemoryUsage(),
                'disk' => $this->getDiskUsage(),
                'uptime' => $this->getUptime(),
                'linux_version' => $this->getLinuxVersion(),
                'php_version' => $this->getPhpVersion(),
            ];
        } catch (\Exception $e) {
            // Provide default values if something goes wrong
            return [
                'cpu' => 0,
                'memory' => [
                    'total' => 0,
                    'used' => 0,
                    'free' => 0,
                    'percent_used' => 0,
                ],
                'disk' => [
                    'filesystem' => 'Unknown',
                    'size' => '0',
                    'used' => '0',
                    'available' => '0',
                    'percent_used' => 0,
                    'mounted_on' => '/',
                ],
                'uptime' => 'Unknown',
                'linux_version' => 'Unknown',
                'php_version' => phpversion(),
            ];
        }
    }
}
