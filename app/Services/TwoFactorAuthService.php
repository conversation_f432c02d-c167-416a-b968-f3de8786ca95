<?php

namespace App\Services;

use App\Models\User;
use App\Models\TwoFactorAuth;
use PragmaRX\Google2FA\Google2FA;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use BaconQrCode\Renderer\ImageRenderer;
use BaconQrCode\Renderer\Image\ImagickImageBackEnd;
use BaconQrCode\Renderer\RendererStyle\RendererStyle;
use BaconQrCode\Writer;

class TwoFactorAuthService
{
    protected Google2FA $google2fa;

    public function __construct()
    {
        $this->google2fa = new Google2FA();
    }

    /**
     * Generate a secret key for the user
     */
    public function generateSecretKey(): string
    {
        return $this->google2fa->generateSecretKey();
    }

    /**
     * Get or create 2FA settings for user
     */
    public function getTwoFactorAuth(User $user): TwoFactorAuth
    {
        return TwoFactorAuth::firstOrCreate(
            ['user_id' => $user->id],
            [
                'secret_key' => $this->generateSecretKey(),
                'enabled' => false,
            ]
        );
    }

    /**
     * Generate QR code for 2FA setup
     */
    public function generateQrCode(User $user): string
    {
        $twoFactorAuth = $this->getTwoFactorAuth($user);

        $companyName = config('app.name', 'HostingPro');
        $companyEmail = $user->email;

        $qrCodeUrl = $this->google2fa->getQRCodeUrl(
            $companyName,
            $companyEmail,
            $twoFactorAuth->secret_key
        );

        // Use Google Charts API as fallback for QR code generation
        $qrCodeImageUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' . urlencode($qrCodeUrl);

        return $qrCodeImageUrl;
    }

    /**
     * Get the secret key for manual entry
     */
    public function getSecretKey(User $user): string
    {
        $twoFactorAuth = $this->getTwoFactorAuth($user);
        return $twoFactorAuth->secret_key;
    }

    /**
     * Verify 2FA code
     */
    public function verifyCode(User $user, string $code): bool
    {
        $twoFactorAuth = $this->getTwoFactorAuth($user);
        
        if (!$twoFactorAuth->secret_key) {
            return false;
        }

        // Check if it's a recovery code
        if (strlen($code) === 8 && ctype_alnum($code)) {
            return $twoFactorAuth->useRecoveryCode($code);
        }

        // Verify TOTP code
        $isValid = $this->google2fa->verifyKey(
            $twoFactorAuth->secret_key,
            $code,
            2 // 2 * 30 seconds = 60 seconds window
        );

        if ($isValid) {
            $twoFactorAuth->last_used_at = now();
            $twoFactorAuth->save();
            
            // Prevent code reuse within the same time window
            $cacheKey = "2fa_used_{$user->id}_{$code}";
            if (Cache::has($cacheKey)) {
                return false;
            }
            Cache::put($cacheKey, true, 90); // 90 seconds
        }

        return $isValid;
    }

    /**
     * Enable 2FA for user
     */
    public function enable(User $user, string $code): bool
    {
        if (!$this->verifyCode($user, $code)) {
            return false;
        }

        $twoFactorAuth = $this->getTwoFactorAuth($user);
        $twoFactorAuth->enable();
        
        // Generate recovery codes
        $twoFactorAuth->generateRecoveryCodes();

        // Update user model
        $user->update([
            'two_factor_enabled' => true,
            'two_factor_confirmed_at' => now(),
        ]);

        Log::info("2FA enabled for user: {$user->email}");
        
        return true;
    }

    /**
     * Disable 2FA for user
     */
    public function disable(User $user): bool
    {
        $twoFactorAuth = $this->getTwoFactorAuth($user);
        $twoFactorAuth->disable();

        // Update user model
        $user->update([
            'two_factor_enabled' => false,
            'two_factor_confirmed_at' => null,
        ]);

        Log::info("2FA disabled for user: {$user->email}");
        
        return true;
    }

    /**
     * Check if user has 2FA enabled
     */
    public function isEnabled(User $user): bool
    {
        $twoFactorAuth = TwoFactorAuth::where('user_id', $user->id)->first();
        return $twoFactorAuth && $twoFactorAuth->isEnabled();
    }

    /**
     * Generate new recovery codes
     */
    public function generateNewRecoveryCodes(User $user): array
    {
        $twoFactorAuth = $this->getTwoFactorAuth($user);
        return $twoFactorAuth->generateRecoveryCodes();
    }

    /**
     * Get recovery codes for user
     */
    public function getRecoveryCodes(User $user): ?array
    {
        $twoFactorAuth = TwoFactorAuth::where('user_id', $user->id)->first();
        return $twoFactorAuth ? $twoFactorAuth->recovery_codes : null;
    }

    /**
     * Set backup method for 2FA
     */
    public function setBackupMethod(User $user, string $method, string $contact): bool
    {
        $twoFactorAuth = $this->getTwoFactorAuth($user);
        $twoFactorAuth->backup_method = $method;
        $twoFactorAuth->backup_contact = $contact;
        $twoFactorAuth->save();

        return true;
    }

    /**
     * Send backup code via email or SMS
     */
    public function sendBackupCode(User $user): bool
    {
        $twoFactorAuth = $this->getTwoFactorAuth($user);
        
        if (!$twoFactorAuth->backup_method || !$twoFactorAuth->backup_contact) {
            return false;
        }

        // Generate temporary backup code
        $backupCode = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6));
        
        // Store in cache for 10 minutes
        Cache::put("backup_2fa_{$user->id}", $backupCode, 600);

        if ($twoFactorAuth->backup_method === 'email') {
            // Send email with backup code
            // TODO: Implement email sending
            Log::info("Backup 2FA code sent via email to: {$twoFactorAuth->backup_contact}");
        } elseif ($twoFactorAuth->backup_method === 'sms') {
            // Send SMS with backup code
            // TODO: Implement SMS sending
            Log::info("Backup 2FA code sent via SMS to: {$twoFactorAuth->backup_contact}");
        }

        return true;
    }

    /**
     * Verify backup code
     */
    public function verifyBackupCode(User $user, string $code): bool
    {
        $cachedCode = Cache::get("backup_2fa_{$user->id}");
        
        if ($cachedCode && strtoupper($code) === $cachedCode) {
            Cache::forget("backup_2fa_{$user->id}");
            return true;
        }

        return false;
    }
}
