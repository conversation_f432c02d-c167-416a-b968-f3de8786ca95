<?php

namespace App\Services;

use App\Models\Database;
use App\Models\DatabaseUser;
use App\Models\Server;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class DatabaseManagementService
{
    private $sshService;

    public function __construct()
    {
        $this->sshService = new SSHService();
    }

    /**
     * Create real database on server
     */
    public function createRealDatabase(Database $database, Server $server, $dbPassword = null)
    {
        try {
            // Connect to server
            $password = $server->password ? decrypt($server->password) : null;
            $this->sshService->connect(
                $server->hostname,
                $server->port,
                $server->username,
                $password,
                $server->ssh_key
            );

            switch ($database->type) {
                case 'mysql':
                    return $this->createMySQLDatabase($database, $dbPassword);
                case 'postgresql':
                    return $this->createPostgreSQLDatabase($database, $dbPassword);
                default:
                    throw new Exception("Unsupported database type: {$database->type}");
            }

        } catch (Exception $e) {
            Log::error("Failed to create database {$database->name}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create MySQL database
     */
    private function createMySQLDatabase(Database $database, $dbPassword = null)
    {
        try {
            $dbName = $database->name;
            $charset = $database->charset ?: 'utf8mb4';
            $collation = $database->collation ?: 'utf8mb4_unicode_ci';

            // Create database
            $createDbCommand = "sudo mysql -e \"CREATE DATABASE IF NOT EXISTS \`{$dbName}\` CHARACTER SET {$charset} COLLATE {$collation};\"";
            $this->sshService->execute($createDbCommand);

            // Create database user if password provided
            if ($dbPassword) {
                $dbUser = $dbName . '_user';
                $createUserCommand = "sudo mysql -e \"CREATE USER IF NOT EXISTS '{$dbUser}'@'localhost' IDENTIFIED BY '{$dbPassword}';\"";
                $this->sshService->execute($createUserCommand);

                // Grant privileges
                $grantCommand = "sudo mysql -e \"GRANT ALL PRIVILEGES ON \`{$dbName}\`.* TO '{$dbUser}'@'localhost';\"";
                $this->sshService->execute($grantCommand);

                // Flush privileges
                $flushCommand = "sudo mysql -e \"FLUSH PRIVILEGES;\"";
                $this->sshService->execute($flushCommand);

                // Create database user record
                DatabaseUser::create([
                    'user_id' => $database->user_id,
                    'username' => $dbUser,
                    'password_hash' => Hash::make($dbPassword),
                    'host' => 'localhost',
                    'is_active' => true,
                    'global_privileges' => ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'INDEX']
                ]);
            }

            // Update database stats
            $this->updateDatabaseStats($database);

            return [
                'success' => true,
                'message' => "MySQL database '{$dbName}' created successfully",
                'database_name' => $dbName,
                'database_user' => $dbPassword ? $dbUser : null
            ];

        } catch (Exception $e) {
            throw new Exception("MySQL database creation failed: " . $e->getMessage());
        }
    }

    /**
     * Create PostgreSQL database
     */
    private function createPostgreSQLDatabase(Database $database, $dbPassword = null)
    {
        try {
            $dbName = $database->name;

            // Create database
            $createDbCommand = "sudo -u postgres createdb {$dbName}";
            $this->sshService->execute($createDbCommand);

            // Create database user if password provided
            if ($dbPassword) {
                $dbUser = $dbName . '_user';
                $createUserCommand = "sudo -u postgres createuser {$dbUser}";
                $this->sshService->execute($createUserCommand);

                // Set password
                $setPasswordCommand = "sudo -u postgres psql -c \"ALTER USER {$dbUser} PASSWORD '{$dbPassword}';\"";
                $this->sshService->execute($setPasswordCommand);

                // Grant privileges
                $grantCommand = "sudo -u postgres psql -c \"GRANT ALL PRIVILEGES ON DATABASE {$dbName} TO {$dbUser};\"";
                $this->sshService->execute($grantCommand);

                // Create database user record
                DatabaseUser::create([
                    'user_id' => $database->user_id,
                    'username' => $dbUser,
                    'password_hash' => Hash::make($dbPassword),
                    'host' => 'localhost',
                    'is_active' => true,
                    'global_privileges' => ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP']
                ]);
            }

            // Update database stats
            $this->updateDatabaseStats($database);

            return [
                'success' => true,
                'message' => "PostgreSQL database '{$dbName}' created successfully",
                'database_name' => $dbName,
                'database_user' => $dbPassword ? $dbUser : null
            ];

        } catch (Exception $e) {
            throw new Exception("PostgreSQL database creation failed: " . $e->getMessage());
        }
    }

    /**
     * Delete real database from server
     */
    public function deleteRealDatabase(Database $database, Server $server)
    {
        try {
            // Connect to server
            $password = $server->password ? decrypt($server->password) : null;
            $this->sshService->connect(
                $server->hostname,
                $server->port,
                $server->username,
                $password,
                $server->ssh_key
            );

            switch ($database->type) {
                case 'mysql':
                    return $this->deleteMySQLDatabase($database);
                case 'postgresql':
                    return $this->deletePostgreSQLDatabase($database);
                default:
                    throw new Exception("Unsupported database type: {$database->type}");
            }

        } catch (Exception $e) {
            Log::error("Failed to delete database {$database->name}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete MySQL database
     */
    private function deleteMySQLDatabase(Database $database)
    {
        try {
            $dbName = $database->name;

            // Drop database
            $dropDbCommand = "sudo mysql -e \"DROP DATABASE IF EXISTS \`{$dbName}\`;\"";
            $this->sshService->execute($dropDbCommand);

            // Drop associated users
            $dbUsers = DatabaseUser::where('user_id', $database->user_id)->get();
            foreach ($dbUsers as $dbUser) {
                $dropUserCommand = "sudo mysql -e \"DROP USER IF EXISTS '{$dbUser->username}'@'localhost';\"";
                $this->sshService->execute($dropUserCommand);
                $dbUser->delete();
            }

            // Flush privileges
            $flushCommand = "sudo mysql -e \"FLUSH PRIVILEGES;\"";
            $this->sshService->execute($flushCommand);

            return [
                'success' => true,
                'message' => "MySQL database '{$dbName}' deleted successfully"
            ];

        } catch (Exception $e) {
            throw new Exception("MySQL database deletion failed: " . $e->getMessage());
        }
    }

    /**
     * Delete PostgreSQL database
     */
    private function deletePostgreSQLDatabase(Database $database)
    {
        try {
            $dbName = $database->name;

            // Drop database
            $dropDbCommand = "sudo -u postgres dropdb {$dbName}";
            $this->sshService->execute($dropDbCommand);

            // Drop associated users
            $dbUsers = DatabaseUser::where('user_id', $database->user_id)->get();
            foreach ($dbUsers as $dbUser) {
                $dropUserCommand = "sudo -u postgres dropuser {$dbUser->username}";
                $this->sshService->execute($dropUserCommand);
                $dbUser->delete();
            }

            return [
                'success' => true,
                'message' => "PostgreSQL database '{$dbName}' deleted successfully"
            ];

        } catch (Exception $e) {
            throw new Exception("PostgreSQL database deletion failed: " . $e->getMessage());
        }
    }

    /**
     * Update database statistics
     */
    public function updateDatabaseStats(Database $database)
    {
        try {
            switch ($database->type) {
                case 'mysql':
                    return $this->updateMySQLStats($database);
                case 'postgresql':
                    return $this->updatePostgreSQLStats($database);
                default:
                    return false;
            }
        } catch (Exception $e) {
            Log::error("Failed to update database stats for {$database->name}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update MySQL database statistics
     */
    private function updateMySQLStats(Database $database)
    {
        try {
            $dbName = $database->name;

            // Get database size
            $sizeQuery = "sudo mysql -e \"SELECT ROUND(SUM(data_length + index_length), 0) AS 'size_bytes' FROM information_schema.tables WHERE table_schema='{$dbName}';\" --skip-column-names";
            $sizeResult = $this->sshService->execute($sizeQuery);
            $sizeBytes = (int)trim($sizeResult) ?: 0;

            // Get table count
            $tableQuery = "sudo mysql -e \"SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='{$dbName}';\" --skip-column-names";
            $tableResult = $this->sshService->execute($tableQuery);
            $tableCount = (int)trim($tableResult) ?: 0;

            // Update database record
            $database->update([
                'size_bytes' => $sizeBytes,
                'table_count' => $tableCount,
                'is_active' => true
            ]);

            return [
                'success' => true,
                'size_bytes' => $sizeBytes,
                'table_count' => $tableCount
            ];

        } catch (Exception $e) {
            throw new Exception("Failed to update MySQL stats: " . $e->getMessage());
        }
    }

    /**
     * Update PostgreSQL database statistics
     */
    private function updatePostgreSQLStats(Database $database)
    {
        try {
            $dbName = $database->name;

            // Get database size
            $sizeQuery = "sudo -u postgres psql -d {$dbName} -t -c \"SELECT pg_database_size('{$dbName}');\"";
            $sizeResult = $this->sshService->execute($sizeQuery);
            $sizeBytes = (int)trim($sizeResult) ?: 0;

            // Get table count
            $tableQuery = "sudo -u postgres psql -d {$dbName} -t -c \"SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='public';\"";
            $tableResult = $this->sshService->execute($tableQuery);
            $tableCount = (int)trim($tableResult) ?: 0;

            // Update database record
            $database->update([
                'size_bytes' => $sizeBytes,
                'table_count' => $tableCount,
                'is_active' => true
            ]);

            return [
                'success' => true,
                'size_bytes' => $sizeBytes,
                'table_count' => $tableCount
            ];

        } catch (Exception $e) {
            throw new Exception("Failed to update PostgreSQL stats: " . $e->getMessage());
        }
    }

    /**
     * Create database backup
     */
    public function createDatabaseBackup(Database $database, Server $server, $backupPath = null)
    {
        try {
            // Connect to server
            $password = $server->password ? decrypt($server->password) : null;
            $this->sshService->connect(
                $server->hostname,
                $server->port,
                $server->username,
                $password,
                $server->ssh_key
            );

            $backupPath = $backupPath ?: "/tmp/{$database->name}_backup_" . date('Y-m-d_H-i-s');

            switch ($database->type) {
                case 'mysql':
                    return $this->createMySQLBackup($database, $backupPath);
                case 'postgresql':
                    return $this->createPostgreSQLBackup($database, $backupPath);
                default:
                    throw new Exception("Unsupported database type: {$database->type}");
            }

        } catch (Exception $e) {
            Log::error("Failed to backup database {$database->name}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create MySQL backup
     */
    private function createMySQLBackup(Database $database, $backupPath)
    {
        try {
            $dbName = $database->name;
            $backupFile = $backupPath . '.sql';

            $backupCommand = "sudo mysqldump {$dbName} > {$backupFile}";
            $this->sshService->execute($backupCommand);

            // Compress backup
            $compressCommand = "gzip {$backupFile}";
            $this->sshService->execute($compressCommand);

            // Update last backup time
            $database->update(['last_backup_at' => now()]);

            return [
                'success' => true,
                'message' => "MySQL backup created successfully",
                'backup_file' => $backupFile . '.gz'
            ];

        } catch (Exception $e) {
            throw new Exception("MySQL backup failed: " . $e->getMessage());
        }
    }

    /**
     * Create PostgreSQL backup
     */
    private function createPostgreSQLBackup(Database $database, $backupPath)
    {
        try {
            $dbName = $database->name;
            $backupFile = $backupPath . '.sql';

            $backupCommand = "sudo -u postgres pg_dump {$dbName} > {$backupFile}";
            $this->sshService->execute($backupCommand);

            // Compress backup
            $compressCommand = "gzip {$backupFile}";
            $this->sshService->execute($compressCommand);

            // Update last backup time
            $database->update(['last_backup_at' => now()]);

            return [
                'success' => true,
                'message' => "PostgreSQL backup created successfully",
                'backup_file' => $backupFile . '.gz'
            ];

        } catch (Exception $e) {
            throw new Exception("PostgreSQL backup failed: " . $e->getMessage());
        }
    }

    /**
     * Restore database from backup
     */
    public function restoreDatabase(Database $database, Server $server, $backupFile)
    {
        try {
            // Connect to server
            $password = $server->password ? decrypt($server->password) : null;
            $this->sshService->connect(
                $server->hostname,
                $server->port,
                $server->username,
                $password,
                $server->ssh_key
            );

            switch ($database->type) {
                case 'mysql':
                    return $this->restoreMySQLDatabase($database, $backupFile);
                case 'postgresql':
                    return $this->restorePostgreSQLDatabase($database, $backupFile);
                default:
                    throw new Exception("Unsupported database type: {$database->type}");
            }

        } catch (Exception $e) {
            Log::error("Failed to restore database {$database->name}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Restore MySQL database
     */
    private function restoreMySQLDatabase(Database $database, $backupFile)
    {
        try {
            $dbName = $database->name;

            // Decompress if needed
            if (str_ends_with($backupFile, '.gz')) {
                $decompressCommand = "gunzip {$backupFile}";
                $this->sshService->execute($decompressCommand);
                $backupFile = str_replace('.gz', '', $backupFile);
            }

            // Restore database
            $restoreCommand = "sudo mysql {$dbName} < {$backupFile}";
            $this->sshService->execute($restoreCommand);

            // Update database stats
            $this->updateDatabaseStats($database);

            return [
                'success' => true,
                'message' => "MySQL database restored successfully"
            ];

        } catch (Exception $e) {
            throw new Exception("MySQL restore failed: " . $e->getMessage());
        }
    }

    /**
     * Restore PostgreSQL database
     */
    private function restorePostgreSQLDatabase(Database $database, $backupFile)
    {
        try {
            $dbName = $database->name;

            // Decompress if needed
            if (str_ends_with($backupFile, '.gz')) {
                $decompressCommand = "gunzip {$backupFile}";
                $this->sshService->execute($decompressCommand);
                $backupFile = str_replace('.gz', '', $backupFile);
            }

            // Restore database
            $restoreCommand = "sudo -u postgres psql {$dbName} < {$backupFile}";
            $this->sshService->execute($restoreCommand);

            // Update database stats
            $this->updateDatabaseStats($database);

            return [
                'success' => true,
                'message' => "PostgreSQL database restored successfully"
            ];

        } catch (Exception $e) {
            throw new Exception("PostgreSQL restore failed: " . $e->getMessage());
        }
    }
}
