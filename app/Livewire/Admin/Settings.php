<?php

namespace App\Livewire\Admin;

use App\Models\Setting;
use Livewire\Component;

class Settings extends Component
{
    // Basic properties
    public $activeTab = 'general';
    public $site_name = '';
    public $site_description = '';
    public $admin_email = '';
    public $timezone = 'UTC';

    public function mount()
    {
        $this->loadSettings();
    }

    public function loadSettings()
    {
        $this->site_name = Setting::get('site_name', 'Hosting Platform');
        $this->site_description = Setting::get('site_description', '');
        $this->admin_email = Setting::get('admin_email', '<EMAIL>');
        $this->timezone = Setting::get('timezone', 'UTC');
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function saveSettings()
    {
        try {
            // Basic validation
            if (empty($this->site_name)) {
                session()->flash('error', 'Site name is required');
                return;
            }

            if (empty($this->admin_email) || !filter_var($this->admin_email, FILTER_VALIDATE_EMAIL)) {
                session()->flash('error', 'Valid admin email is required');
                return;
            }

            Setting::set('site_name', $this->site_name, 'string', 'general', 'Website name');
            Setting::set('site_description', $this->site_description, 'string', 'general', 'Website description');
            Setting::set('admin_email', $this->admin_email, 'string', 'general', 'Administrator email');
            Setting::set('timezone', $this->timezone, 'string', 'general', 'Default timezone');

            session()->flash('success', 'Settings saved successfully!');

            // Refresh the page to show the flash message
            return redirect()->route('admin.settings');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to save settings: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.admin.settings-simple')
            ->layout('components.layouts.admin', [
                'title' => 'System Settings',
                'header' => 'System Settings'
            ]);
    }
}
