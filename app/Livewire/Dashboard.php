<?php

namespace App\Livewire;

use App\Models\User;
use App\Models\Role;
use App\Services\SystemInfoService;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;

class Dashboard extends Component
{
    use WithPagination;
    
    public $refreshInterval = 30000; 

    public function mount()
    {
        $this->dispatch('serverMetricsRefresh');
    }
    
    public function refreshData()
    {
        
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Dashboard data refreshed successfully!'
        ]);
    }

    #[Computed]
    public function stats()
    {
        return [
            'total_users' => User::count(),
            'total_admins' => $this->getRoleUserCount('admin'),
            'total_resellers' => $this->getRoleUserCount('reseller'),
            'total_clients' => $this->getRoleUserCount('client'),
        ];
    }
    
    /**
     * Get the count of users with a specific role
     *
     * @param string $roleSlug
     * @return int
     */
    private function getRoleUserCount(string $roleSlug): int
    {
        $role = Role::where('slug', $roleSlug)->first();
        return $role ? $role->users()->count() : 0;
    }

    #[Computed]
    public function recentUsers()
    {
        return User::latest()->take(5)->get();
    }

    #[Computed]
    public function systemInfo()
    {
        $systemInfoService = new SystemInfoService();
        return $systemInfoService->getAllInfo();
    }

    public function render()
    {
        return view('livewire.dashboard');
    }
}
