<?php

namespace App\Livewire\Auth;

use Livewire\Component;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class Register extends Component
{
    public $name = '';
    public $email = '';
    public $password = '';
    public $password_confirmation = '';

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|string|email|max:255|unique:users',
        'password' => 'required|string|min:8|confirmed',
    ];

    public function register()
    {
        $this->validate();

        $user = User::create([
            'name' => $this->name,
            'email' => $this->email,
            'password' => Hash::make($this->password),
        ]);

        auth()->login($user);

        try {
            
            $token = $user->createToken('Personal Access Token')->accessToken;
            session()->put('access_token', $token);
        } catch (\Exception $e) {
            
            \Log::error('Failed to create access token: ' . $e->getMessage());
        }

        try {
        
            $user->sendEmailVerificationNotification();
            \Log::info('Verification email sent to: ' . $user->email);
            session()->flash('message', 'A verification link has been sent to your email address.');
        } catch (\Exception $e) {
            
            \Log::error('Failed to send verification email: ' . $e->getMessage());

            
            $mailDriver = config('mail.default');
            \Log::error('Mail Driver: ' . $mailDriver);

            if ($mailDriver === 'smtp') {
                \Log::error('SMTP Configuration: ' . config('mail.mailers.smtp.host') . ':' . config('mail.mailers.smtp.port'));
            }

            \Log::error('From Address: ' . config('mail.from.address'));

            
            if (app()->environment('local')) {
                
                session()->flash('message', 'Could not send verification email. In development mode, you can use the "Verify Manually" button on the verification page.');
            } else {
                session()->flash('message', 'Your account has been created but we could not send the verification email. Please try requesting a new verification email.');
            }
        }

        
        try {
            $user->assignRole('user');
            \Log::info('Default role assigned to user: ' . $user->email);
        } catch (\Exception $e) {
            \Log::error('Failed to assign default role: ' . $e->getMessage());
        }

        return redirect()->route('verification.notice');
    }

    public function render()
    {
        return view('livewire.auth.register')
            ->layout('components.layouts.guest', ['title' => 'Register']);
    }
}
