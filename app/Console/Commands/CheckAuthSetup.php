<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CheckAuthSetup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auth:check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check the authentication and role-based access setup';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking authentication and role-based access setup...');
        
        // Check database tables
        $this->checkDatabaseTables();
        
        // Check roles
        $this->checkRoles();
        
        // Check users
        $this->checkUsers();
        
        // Check email configuration
        $this->checkEmailConfig();
        
        $this->info('Authentication check completed.');
    }
    
    private function checkDatabaseTables()
    {
        $this->info('Checking database tables...');
        
        $requiredTables = ['users', 'roles', 'role_user', 'password_reset_tokens', 'sessions'];
        
        foreach ($requiredTables as $table) {
            if (Schema::hasTable($table)) {
                $this->info("✅ Table '{$table}' exists");
            } else {
                $this->error("❌ Table '{$table}' does not exist");
            }
        }
    }
    
    private function checkRoles()
    {
        $this->info('Checking roles...');
        
        $roles = Role::all();
        
        if ($roles->isEmpty()) {
            $this->error('❌ No roles found in the database');
            return;
        }
        
        $this->info('Found ' . $roles->count() . ' roles:');
        
        foreach ($roles as $role) {
            $this->info("- {$role->name} (slug: {$role->slug})");
        }
        
        // Check if admin and user roles exist
        $adminRole = Role::where('slug', 'admin')->first();
        $userRole = Role::where('slug', 'user')->first();
        
        if (!$adminRole) {
            $this->error('❌ Admin role does not exist');
        }
        
        if (!$userRole) {
            $this->error('❌ User role does not exist');
        }
    }
    
    private function checkUsers()
    {
        $this->info('Checking users...');
        
        $users = User::with('roles')->get();
        
        if ($users->isEmpty()) {
            $this->error('❌ No users found in the database');
            return;
        }
        
        $this->info('Found ' . $users->count() . ' users:');
        
        foreach ($users as $user) {
            $roleNames = $user->roles->pluck('name')->implode(', ');
            $verifiedStatus = $user->email_verified_at ? 'verified' : 'not verified';
            
            $this->info("- {$user->name} ({$user->email}) - Roles: {$roleNames} - Email: {$verifiedStatus}");
        }
    }
    
    private function checkEmailConfig()
    {
        $this->info('Checking email configuration...');
        
        $mailer = config('mail.default');
        $fromAddress = config('mail.from.address');
        $fromName = config('mail.from.name');
        
        $this->info("Mail Driver: {$mailer}");
        $this->info("From Address: {$fromAddress}");
        $this->info("From Name: {$fromName}");
        
        if ($mailer === 'smtp') {
            $host = config('mail.mailers.smtp.host');
            $port = config('mail.mailers.smtp.port');
            $encryption = config('mail.mailers.smtp.encryption');
            
            $this->info("SMTP Host: {$host}");
            $this->info("SMTP Port: {$port}");
            $this->info("SMTP Encryption: {$encryption}");
        }
    }
}
