<?php

namespace App\Console\Commands;

use App\Models\Backup;
use App\Models\Database;
use App\Models\Domain;
use App\Models\User;
use App\Jobs\ProcessBackupJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessAutomaticBackups extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'backups:auto {--type=all : Type of backup (database|files|all)}';

    /**
     * The console command description.
     */
    protected $description = 'Process automatic backups for databases and files';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $type = $this->option('type');
        
        $this->info("Starting automatic backup process for type: {$type}");
        
        $backupsCreated = 0;
        
        try {
            if ($type === 'database' || $type === 'all') {
                $backupsCreated += $this->createDatabaseBackups();
            }
            
            if ($type === 'files' || $type === 'all') {
                $backupsCreated += $this->createFilesBackups();
            }
            
            $this->info("Automatic backup process completed. Created {$backupsCreated} backups.");
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error("Automatic backup process failed: " . $e->getMessage());
            Log::error("Automatic backup process failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return Command::FAILURE;
        }
    }

    /**
     * Create automatic database backups
     */
    protected function createDatabaseBackups(): int
    {
        $databases = Database::where('backup_enabled', true)
            ->whereHas('user', function ($query) {
                $query->where('status', 'active');
            })
            ->get();

        $count = 0;

        foreach ($databases as $database) {
            try {
                // Check if backup is needed (daily backup)
                if ($this->shouldCreateBackup($database, 'database')) {
                    $backup = $this->createBackup($database, 'database');
                    ProcessBackupJob::dispatch($backup);
                    $count++;
                    
                    $this->line("Queued database backup for: {$database->name}");
                }
            } catch (\Exception $e) {
                $this->error("Failed to create backup for database {$database->name}: " . $e->getMessage());
                Log::error("Failed to create automatic database backup", [
                    'database_id' => $database->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $count;
    }

    /**
     * Create automatic files backups
     */
    protected function createFilesBackups(): int
    {
        $domains = Domain::where('backup_enabled', true)
            ->whereHas('user', function ($query) {
                $query->where('status', 'active');
            })
            ->get();

        $count = 0;

        foreach ($domains as $domain) {
            try {
                // Check if backup is needed (weekly backup for files)
                if ($this->shouldCreateBackup($domain, 'files')) {
                    $backup = $this->createBackup($domain, 'files');
                    ProcessBackupJob::dispatch($backup);
                    $count++;
                    
                    $this->line("Queued files backup for: {$domain->name}");
                }
            } catch (\Exception $e) {
                $this->error("Failed to create backup for domain {$domain->name}: " . $e->getMessage());
                Log::error("Failed to create automatic files backup", [
                    'domain_id' => $domain->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $count;
    }

    /**
     * Check if backup should be created
     */
    protected function shouldCreateBackup($model, string $type): bool
    {
        $lastBackup = Backup::where('backupable_type', get_class($model))
            ->where('backupable_id', $model->id)
            ->where('type', $type)
            ->where('is_automatic', true)
            ->where('status', 'completed')
            ->latest('created_at')
            ->first();

        if (!$lastBackup) {
            return true; // No previous backup
        }

        // Check backup frequency based on type
        $frequency = match($type) {
            'database' => 1, // Daily
            'files' => 7,    // Weekly
            'full' => 30,    // Monthly
            default => 1,
        };

        return $lastBackup->created_at->addDays($frequency)->isPast();
    }

    /**
     * Create backup record
     */
    protected function createBackup($model, string $type): Backup
    {
        $user = $model->user ?? User::where('role', 'admin')->first();
        
        return Backup::create([
            'user_id' => $user->id,
            'name' => $this->generateBackupName($model, $type),
            'type' => $type,
            'status' => 'pending',
            'backupable_type' => get_class($model),
            'backupable_id' => $model->id,
            'storage_path' => $this->generateStoragePath($type, $model->name ?? $model->id),
            'storage_driver' => 'backups',
            'compression' => 'gzip',
            'encrypted' => true,
            'encryption_method' => 'AES-256',
            'is_automatic' => true,
            'expires_at' => Backup::calculateRetentionDate($type),
            'metadata' => [
                'created_by' => 'system',
                'created_via' => 'automatic',
                'model_type' => get_class($model),
                'model_name' => $model->name ?? "ID: {$model->id}",
            ],
        ]);
    }

    /**
     * Generate backup name
     */
    protected function generateBackupName($model, string $type): string
    {
        $modelName = $model->name ?? "ID-{$model->id}";
        $timestamp = now()->format('Y-m-d_H-i-s');
        
        return "auto_{$type}_{$modelName}_{$timestamp}";
    }

    /**
     * Generate storage path
     */
    protected function generateStoragePath(string $type, string $name): string
    {
        $filename = Backup::generateFilename($type, $name, 'gzip');
        return "automatic/{$type}/" . now()->format('Y/m/d') . "/{$filename}";
    }
}
