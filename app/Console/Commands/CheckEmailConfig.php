<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckEmailConfig extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check email configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking email configuration...');
        
        // Check mail driver
        $driver = config('mail.default');
        $this->info("Mail Driver: {$driver}");
        
        // Check mail configuration based on driver
        if ($driver === 'smtp') {
            $this->checkSmtpConfig();
        } elseif ($driver === 'log') {
            $this->info('Using log driver - emails will be written to log files instead of being sent.');
            $this->info('Log Channel: ' . config('mail.mailers.log.channel', 'default'));
        } elseif ($driver === 'array') {
            $this->info('Using array driver - emails will be stored in an array for testing.');
        } else {
            $this->info("Using {$driver} driver.");
        }
        
        // Check from address
        $fromAddress = config('mail.from.address');
        $fromName = config('mail.from.name');
        $this->info("From Address: {$fromName} <{$fromAddress}>");
        
        // Suggest next steps
        $this->line('');
        $this->info('Next steps:');
        $this->line('1. Run "php artisan email:test <EMAIL>" to send a test email');
        $this->line('2. Check your inbox (and spam folder) for the test email');
        $this->line('3. If you encounter issues, check the logs: "tail -f storage/logs/laravel.log"');
    }
    
    /**
     * Check SMTP configuration.
     */
    private function checkSmtpConfig()
    {
        $host = config('mail.mailers.smtp.host');
        $port = config('mail.mailers.smtp.port');
        $encryption = config('mail.mailers.smtp.encryption');
        $username = config('mail.mailers.smtp.username');
        $password = config('mail.mailers.smtp.password');
        
        $this->info("SMTP Host: {$host}");
        $this->info("SMTP Port: {$port}");
        $this->info("SMTP Encryption: {$encryption}");
        
        // Check if username and password are set
        if (empty($username)) {
            $this->warn('⚠️ SMTP Username is not set!');
        } else {
            $this->info("SMTP Username: {$username}");
        }
        
        if (empty($password)) {
            $this->warn('⚠️ SMTP Password is not set!');
        } else {
            $this->info("SMTP Password: " . str_repeat('*', strlen($password)));
        }
        
        // Check common email providers
        if (strpos($host, 'gmail') !== false) {
            $this->line('');
            $this->info('Gmail SMTP detected:');
            $this->line('- Make sure 2-Step Verification is enabled on your Google account');
            $this->line('- Use an App Password instead of your regular password');
            $this->line('- Create an App Password at: https://myaccount.google.com/apppasswords');
        } elseif (strpos($host, 'mailtrap') !== false) {
            $this->line('');
            $this->info('Mailtrap detected:');
            $this->line('- Mailtrap is a testing service that captures emails instead of sending them');
            $this->line('- Check your Mailtrap inbox at: https://mailtrap.io/inboxes');
        }
    }
}
