<?php

namespace App\Console\Commands;

use App\Models\Domain;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class CheckDomainExpirations extends Command
{
    protected $signature = 'domains:check-expirations {--days=30 : Days before expiration to send notification}';
    protected $description = 'Check for domains expiring soon and send notifications';

    public function handle()
    {
        $days = (int) $this->option('days');
        $this->info("Checking for domains expiring within {$days} days...");

        // Get domains expiring soon
        $expiringDomains = Domain::whereNotNull('expires_at')
            ->where('expires_at', '>', now())
            ->where('expires_at', '<=', now()->addDays($days))
            ->where('status', 'active')
            ->with('user')
            ->get();

        // Get expired domains
        $expiredDomains = Domain::whereNotNull('expires_at')
            ->where('expires_at', '<', now())
            ->where('status', '!=', 'suspended')
            ->with('user')
            ->get();

        $this->info("Found {$expiringDomains->count()} domains expiring soon");
        $this->info("Found {$expiredDomains->count()} expired domains");

        // Process expiring domains
        foreach ($expiringDomains as $domain) {
            $this->processExpiringDomain($domain);
        }

        // Process expired domains
        foreach ($expiredDomains as $domain) {
            $this->processExpiredDomain($domain);
        }

        // Create notifications for admin
        $this->createAdminNotifications($expiringDomains, $expiredDomains);

        $this->info('Domain expiration check completed!');
        return 0;
    }

    private function processExpiringDomain(Domain $domain)
    {
        $daysUntilExpiry = now()->diffInDays($domain->expires_at);
        
        $this->line("Processing expiring domain: {$domain->name} (expires in {$daysUntilExpiry} days)");

        // Log the expiring domain
        Log::info("Domain expiring soon", [
            'domain' => $domain->name,
            'expires_at' => $domain->expires_at,
            'days_until_expiry' => $daysUntilExpiry,
            'user_id' => $domain->user_id,
            'auto_renew' => $domain->auto_renew
        ]);

        // Send email notification to domain owner
        if ($domain->user && $domain->user->email) {
            try {
                // Here you would send an email notification
                // Mail::to($domain->user->email)->send(new DomainExpiringNotification($domain));
                $this->line("  → Email notification sent to {$domain->user->email}");
            } catch (\Exception $e) {
                $this->error("  → Failed to send email to {$domain->user->email}: " . $e->getMessage());
            }
        }

        // If auto-renew is enabled, attempt renewal
        if ($domain->auto_renew) {
            $this->line("  → Auto-renew enabled, attempting renewal...");
            // Here you would implement auto-renewal logic
            // $this->attemptAutoRenewal($domain);
        }
    }

    private function processExpiredDomain(Domain $domain)
    {
        $daysExpired = $domain->expires_at->diffInDays(now());
        
        $this->line("Processing expired domain: {$domain->name} (expired {$daysExpired} days ago)");

        // Log the expired domain
        Log::warning("Domain expired", [
            'domain' => $domain->name,
            'expires_at' => $domain->expires_at,
            'days_expired' => $daysExpired,
            'user_id' => $domain->user_id,
            'current_status' => $domain->status
        ]);

        // Suspend domain if expired for more than 7 days
        if ($daysExpired > 7 && $domain->status !== 'suspended') {
            $domain->update(['status' => 'suspended']);
            $this->line("  → Domain suspended due to expiration");
            
            // Send suspension notification
            if ($domain->user && $domain->user->email) {
                try {
                    // Mail::to($domain->user->email)->send(new DomainSuspendedNotification($domain));
                    $this->line("  → Suspension notification sent to {$domain->user->email}");
                } catch (\Exception $e) {
                    $this->error("  → Failed to send suspension email: " . $e->getMessage());
                }
            }
        }
    }

    private function createAdminNotifications($expiringDomains, $expiredDomains)
    {
        // Get admin users
        $adminUsers = User::whereHas('roles', function ($query) {
            $query->where('slug', 'admin');
        })->get();

        foreach ($adminUsers as $admin) {
            // Create notification record in database
            // You can implement a notifications table for this
            $this->line("Creating admin notification for {$admin->email}");
        }

        // Send summary email to admins
        if ($expiringDomains->count() > 0 || $expiredDomains->count() > 0) {
            foreach ($adminUsers as $admin) {
                try {
                    // Mail::to($admin->email)->send(new DomainExpirationSummary($expiringDomains, $expiredDomains));
                    $this->line("Summary email sent to admin: {$admin->email}");
                } catch (\Exception $e) {
                    $this->error("Failed to send summary email to {$admin->email}: " . $e->getMessage());
                }
            }
        }
    }

    private function attemptAutoRenewal(Domain $domain)
    {
        // This would integrate with domain registrar APIs
        // For now, just log the attempt
        Log::info("Auto-renewal attempted for domain: {$domain->name}");
        
        // Simulate renewal (in real implementation, this would call registrar API)
        $renewalSuccess = true; // This would be the actual API response
        
        if ($renewalSuccess) {
            // Extend expiration date by 1 year
            $domain->update([
                'expires_at' => $domain->expires_at->addYear(),
                'updated_at' => now()
            ]);
            
            $this->line("  → Auto-renewal successful, expiration extended to {$domain->expires_at->format('Y-m-d')}");
            
            // Log successful renewal
            Log::info("Domain auto-renewed successfully", [
                'domain' => $domain->name,
                'new_expiration' => $domain->expires_at,
                'user_id' => $domain->user_id
            ]);
        } else {
            $this->error("  → Auto-renewal failed for {$domain->name}");
            Log::error("Domain auto-renewal failed", [
                'domain' => $domain->name,
                'user_id' => $domain->user_id
            ]);
        }
    }
}
