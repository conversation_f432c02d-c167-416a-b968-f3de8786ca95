<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Domain;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateTestDomains extends Command
{
    protected $signature = 'app:create-test-domains';
    protected $description = 'Create test domains with different users and statuses';

    public function handle()
    {
        $this->info('Creating test users and domains...');

        // Create test users
        $users = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'user',
                'status' => 'active',
                'hosting_status' => 'active',
                'disk_quota' => 5000,
                'bandwidth_quota' => 50000,
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'user',
                'status' => 'active',
                'hosting_status' => 'active',
                'disk_quota' => 10000,
                'bandwidth_quota' => 100000,
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'user',
                'status' => 'active',
                'hosting_status' => 'suspended',
                'disk_quota' => 2000,
                'bandwidth_quota' => 20000,
            ],
            [
                'name' => 'Lisa Brown',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'user',
                'status' => 'inactive',
                'hosting_status' => 'inactive',
                'disk_quota' => 1000,
                'bandwidth_quota' => 10000,
            ],
        ];

        $createdUsers = [];
        foreach ($users as $userData) {
            $user = User::firstOrCreate(
                ['email' => $userData['email']],
                $userData
            );
            $createdUsers[] = $user;
            $this->info("Created/Found user: {$user->name} ({$user->email})");
        }

        // Create test domains
        $domains = [
            [
                'name' => 'example.com',
                'status' => 'active',
                'registrar' => 'GoDaddy',
                'registered_at' => now()->subYears(2),
                'expires_at' => now()->addMonths(6),
                'auto_renew' => true,
                'nameserver1' => 'ns1.example.com',
                'nameserver2' => 'ns2.example.com',
                'dns_managed' => true,
                'document_root' => '/var/www/example.com',
                'php_version' => '8.3',
                'ssl_enabled' => true,
                'ssl_provider' => 'Let\'s Encrypt',
                'notes' => 'Main company website',
            ],
            [
                'name' => 'testsite.org',
                'status' => 'active',
                'registrar' => 'Namecheap',
                'registered_at' => now()->subYear(),
                'expires_at' => now()->addDays(15), // Expiring soon
                'auto_renew' => false,
                'nameserver1' => 'ns1.namecheap.com',
                'nameserver2' => 'ns2.namecheap.com',
                'dns_managed' => false,
                'document_root' => '/var/www/testsite.org',
                'php_version' => '8.2',
                'ssl_enabled' => true,
                'ssl_provider' => 'Cloudflare',
                'notes' => 'Test website for development',
            ],
            [
                'name' => 'myblog.net',
                'status' => 'inactive',
                'registrar' => 'Domain.com',
                'registered_at' => now()->subMonths(8),
                'expires_at' => now()->addMonths(4),
                'auto_renew' => true,
                'nameserver1' => 'ns1.domain.com',
                'nameserver2' => 'ns2.domain.com',
                'dns_managed' => true,
                'document_root' => '/var/www/myblog.net',
                'php_version' => '8.1',
                'ssl_enabled' => false,
                'ssl_provider' => null,
                'notes' => 'Personal blog - currently inactive',
            ],
            [
                'name' => 'shop.co',
                'status' => 'suspended',
                'registrar' => 'Hover',
                'registered_at' => now()->subMonths(6),
                'expires_at' => now()->subDays(5), // Expired
                'auto_renew' => false,
                'nameserver1' => 'ns1.hover.com',
                'nameserver2' => 'ns2.hover.com',
                'dns_managed' => false,
                'document_root' => '/var/www/shop.co',
                'php_version' => '7.4',
                'ssl_enabled' => false,
                'ssl_provider' => null,
                'notes' => 'E-commerce site - suspended due to payment issues',
            ],
            [
                'name' => 'portfolio.dev',
                'status' => 'active',
                'registrar' => 'Google Domains',
                'registered_at' => now()->subMonths(3),
                'expires_at' => now()->addMonths(9),
                'auto_renew' => true,
                'nameserver1' => 'ns1.googledomains.com',
                'nameserver2' => 'ns2.googledomains.com',
                'nameserver3' => 'ns3.googledomains.com',
                'nameserver4' => 'ns4.googledomains.com',
                'dns_managed' => true,
                'document_root' => '/var/www/portfolio.dev',
                'php_version' => '8.3',
                'ssl_enabled' => true,
                'ssl_provider' => 'Let\'s Encrypt',
                'notes' => 'Developer portfolio website',
            ],
            [
                'name' => 'startup.io',
                'status' => 'active',
                'registrar' => 'Porkbun',
                'registered_at' => now()->subWeeks(2),
                'expires_at' => now()->addYear(),
                'auto_renew' => true,
                'nameserver1' => 'ns1.porkbun.com',
                'nameserver2' => 'ns2.porkbun.com',
                'dns_managed' => true,
                'document_root' => '/var/www/startup.io',
                'php_version' => '8.3',
                'ssl_enabled' => true,
                'ssl_provider' => 'Cloudflare',
                'notes' => 'New startup landing page',
            ],
            [
                'name' => 'oldsite.biz',
                'status' => 'inactive',
                'registrar' => 'Network Solutions',
                'registered_at' => now()->subYears(5),
                'expires_at' => now()->addMonths(2),
                'auto_renew' => false,
                'nameserver1' => 'ns1.networksolutions.com',
                'nameserver2' => 'ns2.networksolutions.com',
                'dns_managed' => false,
                'document_root' => '/var/www/oldsite.biz',
                'php_version' => '7.4',
                'ssl_enabled' => false,
                'ssl_provider' => null,
                'notes' => 'Legacy website - needs migration',
            ],
            [
                'name' => 'newproject.app',
                'status' => 'active',
                'registrar' => 'Cloudflare',
                'registered_at' => now()->subDays(5),
                'expires_at' => now()->addYear(),
                'auto_renew' => true,
                'nameserver1' => 'ns1.cloudflare.com',
                'nameserver2' => 'ns2.cloudflare.com',
                'dns_managed' => true,
                'document_root' => '/var/www/newproject.app',
                'php_version' => '8.3',
                'ssl_enabled' => true,
                'ssl_provider' => 'Cloudflare',
                'notes' => 'Brand new project just launched',
            ],
        ];

        foreach ($domains as $index => $domainData) {
            // Assign to different users
            $user = $createdUsers[$index % count($createdUsers)];
            $domainData['user_id'] = $user->id;

            $domain = Domain::firstOrCreate(
                ['name' => $domainData['name']],
                $domainData
            );

            $this->info("Created/Found domain: {$domain->name} (Owner: {$user->name}, Status: {$domain->status})");
        }

        $this->info('Test domains created successfully!');
        $this->info('Total domains: ' . Domain::count());
        $this->info('Active domains: ' . Domain::where('status', 'active')->count());
        $this->info('Inactive domains: ' . Domain::where('status', 'inactive')->count());
        $this->info('Suspended domains: ' . Domain::where('status', 'suspended')->count());

        return 0;
    }
}
