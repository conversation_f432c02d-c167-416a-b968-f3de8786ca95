<?php

namespace App\Console\Commands;

use App\Models\Backup;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanupExpiredBackups extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'backups:cleanup {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     */
    protected $description = 'Clean up expired backup files and records';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('Running in dry-run mode. No files will be deleted.');
        }
        
        $this->info('Starting backup cleanup process...');
        
        try {
            $expiredBackups = Backup::expired()->get();
            
            if ($expiredBackups->isEmpty()) {
                $this->info('No expired backups found.');
                return Command::SUCCESS;
            }
            
            $this->info("Found {$expiredBackups->count()} expired backups.");
            
            $deletedCount = 0;
            $failedCount = 0;
            $totalSize = 0;
            
            foreach ($expiredBackups as $backup) {
                try {
                    $size = $backup->size_bytes ?? 0;
                    $totalSize += $size;
                    
                    if ($dryRun) {
                        $this->line("Would delete: {$backup->name} (ID: {$backup->id}) - " . $this->formatBytes($size));
                        $deletedCount++;
                    } else {
                        // Delete backup file
                        if ($backup->fileExists()) {
                            $backup->deleteFile();
                        }
                        
                        // Delete backup record
                        $backup->delete();
                        
                        $this->line("Deleted: {$backup->name} (ID: {$backup->id}) - " . $this->formatBytes($size));
                        $deletedCount++;
                        
                        Log::info("Expired backup cleaned up", [
                            'backup_id' => $backup->id,
                            'name' => $backup->name,
                            'size_bytes' => $size
                        ]);
                    }
                } catch (\Exception $e) {
                    $this->error("Failed to delete backup {$backup->name}: " . $e->getMessage());
                    $failedCount++;
                    
                    Log::error("Failed to cleanup expired backup", [
                        'backup_id' => $backup->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            if ($dryRun) {
                $this->info("Dry run completed. Would delete {$deletedCount} backups, freeing " . $this->formatBytes($totalSize));
            } else {
                $this->info("Cleanup completed. Deleted {$deletedCount} backups, freed " . $this->formatBytes($totalSize));
                
                if ($failedCount > 0) {
                    $this->warn("Failed to delete {$failedCount} backups. Check logs for details.");
                }
            }
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error("Backup cleanup failed: " . $e->getMessage());
            Log::error("Backup cleanup process failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return Command::FAILURE;
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        if ($bytes === 0) {
            return '0 B';
        }
        
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $power = floor(log($bytes, 1024));
        
        return round($bytes / pow(1024, $power), 2) . ' ' . $units[$power];
    }
}
