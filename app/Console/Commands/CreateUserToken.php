<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class CreateUserToken extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'passport:create-token {email : The email of the user}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a personal access token for a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User with email {$email} not found.");
            return 1;
        }
        
        try {
            $token = $user->createToken('CLI Personal Access Token')->accessToken;
            
            $this->info("Token created successfully for user {$user->name}:");
            $this->line($token);
            
            $this->info("\nYou can use this token in API requests with the following header:");
            $this->line("Authorization: Bearer {$token}");
            
            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to create token: " . $e->getMessage());
            return 1;
        }
    }
}
