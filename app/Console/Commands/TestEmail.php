<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Mail\Message;

class TestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test email to verify email configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        $this->info("Sending test email to {$email}...");
        $this->info("Mail configuration: " . config('mail.default') . " via " . config('mail.mailers.smtp.host') . ":" . config('mail.mailers.smtp.port'));

        try {
            // Create a more detailed test email
            $html = '
                <h1>Test Email from Laravel Hosting App</h1>
                <p>This is a test email to verify your email configuration is working correctly.</p>
                <hr>
                <h2>Configuration Details:</h2>
                <ul>
                    <li><strong>Driver:</strong> ' . config('mail.default') . '</li>
                    <li><strong>Host:</strong> ' . config('mail.mailers.smtp.host') . '</li>
                    <li><strong>Port:</strong> ' . config('mail.mailers.smtp.port') . '</li>
                    <li><strong>From Address:</strong> ' . config('mail.from.address') . '</li>
                    <li><strong>From Name:</strong> ' . config('mail.from.name') . '</li>
                </ul>
                <p>If you received this email, your email configuration is working correctly!</p>
            ';

            Mail::html($html, function (Message $message) use ($email) {
                $message->to($email)
                    ->subject('Test Email from Laravel Hosting App');
            });

            $this->info('Test email sent successfully!');
            $this->info('Please check your inbox (and spam folder) for the test email.');
            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to send test email: ' . $e->getMessage());

            // Display more detailed error information
            $this->line('');
            $this->line('Debugging information:');
            $this->line('- Mail Driver: ' . config('mail.default'));
            $this->line('- SMTP Host: ' . config('mail.mailers.smtp.host'));
            $this->line('- SMTP Port: ' . config('mail.mailers.smtp.port'));
            $this->line('- SMTP Encryption: ' . config('mail.mailers.smtp.encryption'));
            $this->line('- From Address: ' . config('mail.from.address'));

            return 1;
        }
    }
}
