<?php

namespace App\Console\Commands;

use App\Models\Application;
use App\Services\ApplicationMonitoringService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MonitorApplications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'applications:monitor {--all : Monitor all applications} {--id= : Monitor specific application by ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor application health and update statuses';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting application monitoring...');

        if ($this->option('id')) {
            return $this->monitorSingleApplication($this->option('id'));
        }

        return $this->monitorAllApplications();
    }

    /**
     * Monitor a single application
     */
    private function monitorSingleApplication($applicationId)
    {
        $application = Application::find($applicationId);

        if (!$application) {
            $this->error("Application with ID {$applicationId} not found.");
            return 1;
        }

        $this->info("Monitoring application: {$application->name}");

        try {
            $monitoringService = new ApplicationMonitoringService($application);
            $result = $monitoringService->checkApplicationHealth();

            if ($result['success']) {
                $this->info("Health check completed for {$application->name}");
                $this->line("Health Score: {$result['health_score']}%");
                $this->line("Status: {$result['status']}");

                if (!empty($result['recommendations'])) {
                    $this->warn('Recommendations:');
                    foreach ($result['recommendations'] as $recommendation) {
                        $this->line("  - {$recommendation}");
                    }
                }
            } else {
                $this->error("Health check failed for {$application->name}: {$result['error']}");
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("Error monitoring {$application->name}: " . $e->getMessage());
            Log::error("Application monitoring error: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Monitor all applications
     */
    private function monitorAllApplications()
    {
        $applications = Application::where('status', '!=', 'inactive')->get();

        if ($applications->isEmpty()) {
            $this->info('No active applications found to monitor.');
            return 0;
        }

        $this->info("Found {$applications->count()} applications to monitor.");

        $results = [
            'total' => 0,
            'healthy' => 0,
            'warning' => 0,
            'critical' => 0,
            'errors' => 0
        ];

        $progressBar = $this->output->createProgressBar($applications->count());
        $progressBar->start();

        foreach ($applications as $application) {
            try {
                $monitoringService = new ApplicationMonitoringService($application);
                $result = $monitoringService->checkApplicationHealth();

                $results['total']++;

                if ($result['success']) {
                    $healthScore = $result['health_score'];

                    if ($healthScore >= 80) {
                        $results['healthy']++;
                    } elseif ($healthScore >= 50) {
                        $results['warning']++;
                    } else {
                        $results['critical']++;
                    }
                } else {
                    $results['errors']++;
                    Log::error("Health check failed for {$application->name}: {$result['error']}");
                }
            } catch (\Exception $e) {
                $results['errors']++;
                Log::error("Error monitoring {$application->name}: " . $e->getMessage());
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        // Display summary
        $this->info('Monitoring Summary:');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Applications', $results['total']],
                ['Healthy (80%+)', $results['healthy']],
                ['Warning (50-79%)', $results['warning']],
                ['Critical (<50%)', $results['critical']],
                ['Errors', $results['errors']],
            ]
        );

        // Log summary
        Log::info('Application monitoring completed', $results);

        return 0;
    }
}
