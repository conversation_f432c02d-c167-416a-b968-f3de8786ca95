<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\TestEmail::class,
        Commands\CreateUserToken::class,
        Commands\CheckAuthSetup::class,
        Commands\CheckEmailConfig::class,
        Commands\ProcessAutomaticBackups::class,
        Commands\CleanupExpiredBackups::class,
    ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Run automatic backups daily at 2 AM
        $schedule->command('backups:auto database')->dailyAt('02:00');

        // Run file backups weekly on Sunday at 3 AM
        $schedule->command('backups:auto files')->weeklyOn(0, '03:00');

        // Clean up expired backups daily at 4 AM
        $schedule->command('backups:cleanup')->dailyAt('04:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
