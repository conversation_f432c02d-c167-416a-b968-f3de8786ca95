<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if (config('app.env') !== 'local' || request()->server('HTTPS') == 'on') {
            \URL::forceScheme('https');
        }

        // Disable Vite in production or when assets are not built
        if (!file_exists(public_path('build/manifest.json'))) {
            \Illuminate\Foundation\Vite::macro('useBuildDirectory', function () {
                return '';
            });
        }
    }
}
