<?php

namespace App\Jobs;

use App\Models\Backup;
use App\Services\BackupService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessBackupJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $backup;
    
    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public $timeout = 3600; // 1 hour

    /**
     * Create a new job instance.
     */
    public function __construct(Backup $backup)
    {
        $this->backup = $backup;
        $this->onQueue('backups');
    }

    /**
     * Execute the job.
     */
    public function handle(BackupService $backupService): void
    {
        try {
            Log::info("Processing backup job", [
                'backup_id' => $this->backup->id,
                'type' => $this->backup->type,
                'name' => $this->backup->name
            ]);

            // Update backup status to running
            $this->backup->update(['status' => 'running']);

            // Process the backup
            $success = $backupService->processBackup($this->backup);

            if (!$success) {
                throw new \Exception('Backup processing failed');
            }

            Log::info("Backup job completed successfully", [
                'backup_id' => $this->backup->id
            ]);

        } catch (\Exception $e) {
            Log::error("Backup job failed", [
                'backup_id' => $this->backup->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->backup->markAsFailed($e->getMessage());
            
            // Re-throw to trigger job failure
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("Backup job failed permanently", [
            'backup_id' => $this->backup->id,
            'error' => $exception->getMessage()
        ]);

        $this->backup->markAsFailed($exception->getMessage());
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'backup',
            'backup:' . $this->backup->id,
            'type:' . $this->backup->type,
        ];
    }
}
