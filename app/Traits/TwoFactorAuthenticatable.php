<?php

namespace App\Traits;

use PragmaRX\Google2FA\Google2FA;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Crypt;

trait TwoFactorAuthenticatable
{
    /**
     * Get the user's two factor authentication recovery codes.
     */
    public function recoveryCodes(): array
    {
        return json_decode(decrypt($this->two_factor_recovery_codes), true) ?? [];
    }

    /**
     * Replace the given recovery code with a new one in the user's stored codes.
     */
    public function replaceRecoveryCode(string $code): void
    {
        $this->forceFill([
            'two_factor_recovery_codes' => encrypt(str_replace(
                $code,
                $this->generateRecoveryCode(),
                decrypt($this->two_factor_recovery_codes)
            )),
        ])->save();
    }

    /**
     * Get the QR code SVG of the user's two factor authentication QR code URL.
     */
    public function twoFactorQrCodeSvg(): string
    {
        $google2fa = new Google2FA();
        
        return $google2fa->getQRCodeInline(
            config('app.name'),
            $this->email,
            decrypt($this->two_factor_secret)
        );
    }

    /**
     * Get the two factor authentication QR code URL.
     */
    public function twoFactorQrCodeUrl(): string
    {
        $google2fa = new Google2FA();
        
        return $google2fa->getQRCodeUrl(
            config('app.name'),
            $this->email,
            decrypt($this->two_factor_secret)
        );
    }

    /**
     * Enable two factor authentication for the user.
     */
    public function enableTwoFactorAuthentication(): void
    {
        $this->forceFill([
            'two_factor_enabled' => true,
            'two_factor_secret' => encrypt($this->generateTwoFactorSecret()),
            'two_factor_recovery_codes' => encrypt(json_encode($this->generateRecoveryCodes())),
            'two_factor_confirmed_at' => now(),
        ])->save();
    }

    /**
     * Disable two factor authentication for the user.
     */
    public function disableTwoFactorAuthentication(): void
    {
        $this->forceFill([
            'two_factor_enabled' => false,
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null,
            'two_factor_confirmed_at' => null,
        ])->save();
    }

    /**
     * Generate a new two factor authentication secret.
     */
    protected function generateTwoFactorSecret(): string
    {
        $google2fa = new Google2FA();
        return $google2fa->generateSecretKey();
    }

    /**
     * Generate new recovery codes for the user.
     */
    protected function generateRecoveryCodes(): array
    {
        return Collection::times(8, function () {
            return $this->generateRecoveryCode();
        })->toArray();
    }

    /**
     * Generate a new recovery code.
     */
    protected function generateRecoveryCode(): string
    {
        return strtolower(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8));
    }

    /**
     * Confirm the user's two factor authentication setup.
     */
    public function confirmTwoFactorAuthentication(string $code): bool
    {
        $google2fa = new Google2FA();
        
        $valid = $google2fa->verifyKey(
            decrypt($this->two_factor_secret),
            $code
        );

        if ($valid) {
            $this->forceFill([
                'two_factor_confirmed_at' => now(),
            ])->save();
        }

        return $valid;
    }

    /**
     * Verify the given two factor authentication code.
     */
    public function verifyTwoFactorCode(string $code): bool
    {
        if (!$this->two_factor_enabled) {
            return false;
        }

        $google2fa = new Google2FA();
        
        return $google2fa->verifyKey(
            decrypt($this->two_factor_secret),
            $code
        );
    }

    /**
     * Verify the given recovery code.
     */
    public function verifyRecoveryCode(string $code): bool
    {
        if (!$this->two_factor_enabled) {
            return false;
        }

        $recoveryCodes = $this->recoveryCodes();
        
        if (in_array($code, $recoveryCodes)) {
            $this->replaceRecoveryCode($code);
            return true;
        }

        return false;
    }

    /**
     * Determine if two factor authentication is enabled.
     */
    public function hasEnabledTwoFactorAuthentication(): bool
    {
        return $this->two_factor_enabled && 
               !is_null($this->two_factor_secret) && 
               !is_null($this->two_factor_confirmed_at);
    }

    /**
     * Determine if two factor authentication is not enabled.
     */
    public function hasNotEnabledTwoFactorAuthentication(): bool
    {
        return !$this->hasEnabledTwoFactorAuthentication();
    }
}
