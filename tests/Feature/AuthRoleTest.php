<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Role;

class AuthRoleTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_register()
    {
        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $response->assertRedirect('/email/verify');
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
        ]);
    }

    public function test_user_gets_default_role_on_registration()
    {
        $response = $this->post('/register', [
            'name' => 'Role Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertTrue($user->hasRole('user'));
    }

    public function test_user_can_login()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertRedirect('/dashboard');
        $this->assertAuthenticatedAs($user);
    }

    public function test_user_cannot_access_admin_routes()
    {
        // Create user role
        $userRole = Role::create([
            'name' => 'User',
            'slug' => 'user',
            'description' => 'Regular user with limited access',
        ]);

        // Create a user with user role
        $user = User::factory()->create();
        $user->roles()->attach($userRole);

        // Login as the user
        $this->actingAs($user);

        // Try to access admin route
        $response = $this->get('/admin/dashboard');

        // Should get a 403 forbidden response
        $response->assertStatus(403);
    }

    public function test_admin_can_access_admin_routes()
    {
        // Create admin role
        $adminRole = Role::create([
            'name' => 'Administrator',
            'slug' => 'admin',
            'description' => 'Administrator has full access to all features',
        ]);

        // Create a user with admin role
        $admin = User::factory()->create();
        $admin->roles()->attach($adminRole);

        // Login as the admin
        $this->actingAs($admin);

        // Try to access admin route
        $response = $this->get('/admin/dashboard');

        // Should be successful
        $response->assertStatus(200);
    }

    public function test_api_authentication()
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
        ]);

        // Login via API
        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'user',
            'token',
            'message'
        ]);

        // Get the token
        $token = $response->json('token');

        // Use the token to access a protected route
        $userResponse = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/user');

        $userResponse->assertStatus(200);
        $userResponse->assertJsonFragment([
            'email' => '<EMAIL>',
        ]);
    }
}
