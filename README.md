# Hosting Management Platform

A modern, professional hosting management platform built with Laravel 11, featuring a comprehensive admin dashboard, user management, and system settings. The platform provides a complete solution for managing hosting services with a beautiful, responsive interface.

## 🚀 Features

### Authentication & Authorization
- **Modern Login/Register Pages** - Glassmorphism design with social login options
- **Role-Based Access Control** - Admin and user roles with permissions
- **Secure Authentication** - CSRF protection and session management

### Admin Dashboard
- **Comprehensive Dashboard** - Statistics, charts, and system overview
- **User Management** - Create, edit, delete users with role assignment
- **Role & Permissions** - Manage user roles and access levels
- **System Settings** - Complete settings management with real-time updates

### Settings Management
- **General Settings** - Site name, description, timezone configuration
- **Security Settings** - Password requirements, session timeout, 2FA
- **Email Settings** - SMTP configuration and notification preferences
- **System Tools** - Cache management, backup creation, system information

### Modern UI/UX
- **Responsive Design** - Works perfectly on desktop, tablet, and mobile
- **Glassmorphism Effects** - Modern design with backdrop blur effects
- **Interactive Elements** - Hover effects, animations, and transitions
- **Dark/Light Theme Support** - Consistent color scheme throughout

## 🛠️ Technology Stack

- **Backend**: Laravel 11 (PHP 8.2+)
- **Frontend**: Blade Templates, Tailwind CSS, Alpine.js
- **Database**: MySQL 8.0
- **Authentication**: Laravel Breeze with Livewire
- **Containerization**: Docker & Docker Compose
- **Web Server**: Nginx
- **Process Manager**: Supervisor

## 📋 Prerequisites

### For Docker Installation (Recommended)
- Docker Engine 20.10+
- Docker Compose 2.0+
- Git

### For Local Installation
- PHP 8.2 or higher
- Composer 2.0+
- Node.js 18+ & npm
- MySQL 8.0 or MariaDB 10.3+
- Git

## 🐳 Docker Installation (Recommended)

### Windows Installation

1. **Install Docker Desktop**
   ```bash
   # Download and install Docker Desktop from:
   # https://www.docker.com/products/docker-desktop/

   # Verify installation
   docker --version
   docker-compose --version
   ```

2. **Clone the Repository**
   ```bash
   git clone https://github.com/Logicielab-saas/Hosting-app.git
   cd Hosting-app
   ```

3. **Environment Setup**
   ```bash
   # Copy environment file
   copy .env.example .env

   # Edit .env file with your preferred text editor
   notepad .env
   ```

4. **Build and Start Containers**
   ```bash
   # Build and start all services
   docker-compose up -d --build

   # Check container status
   docker-compose ps
   ```

5. **Application Setup**
   ```bash
   # Install dependencies
   docker-compose exec app composer install

   # Generate application key
   docker-compose exec app php artisan key:generate

   # Run migrations and seeders
   docker-compose exec app php artisan migrate --seed

   # Create storage link
   docker-compose exec app php artisan storage:link

   # Clear cache
   docker-compose exec app php artisan optimize:clear
   ```

### Linux Installation

1. **Install Docker and Docker Compose**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install docker.io docker-compose

   # CentOS/RHEL
   sudo yum install docker docker-compose

   # Start Docker service
   sudo systemctl start docker
   sudo systemctl enable docker

   # Add user to docker group
   sudo usermod -aG docker $USER
   newgrp docker

   # Verify installation
   docker --version
   docker-compose --version
   ```

2. **Clone the Repository**
   ```bash
   git clone https://github.com/Logicielab-saas/Hosting-app.git
   cd hosting-platform
   ```

3. **Environment Setup**
   ```bash
   # Copy environment file
   cp .env.example .env

   # Edit environment variables
   nano .env
   ```

4. **Build and Start Containers**
   ```bash
   # Build and start all services
   docker-compose up -d --build

   # Check container status
   docker-compose ps
   ```

5. **Application Setup**
   ```bash
   # Install dependencies
   docker-compose exec app composer install

   # Generate application key
   docker-compose exec app php artisan key:generate

   # Run migrations and seeders
   docker-compose exec app php artisan migrate --seed

   # Create storage link
   docker-compose exec app php artisan storage:link

   # Clear cache
   docker-compose exec app php artisan optimize:clear
   ```

## 💻 Local Installation

### Prerequisites Installation

1. **Install PHP 8.2+**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install php8.2 php8.2-cli php8.2-fpm php8.2-mysql php8.2-xml php8.2-curl php8.2-zip php8.2-mbstring php8.2-gd

   # Windows (using Chocolatey)
   choco install php

   # macOS (using Homebrew)
   brew install php@8.2
   ```

2. **Install Composer**
   ```bash
   # Download and install Composer
   curl -sS https://getcomposer.org/installer | php
   sudo mv composer.phar /usr/local/bin/composer

   # Verify installation
   composer --version
   ```

3. **Install Node.js and npm**
   ```bash
   # Ubuntu/Debian
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs

   # Windows (download from nodejs.org)
   # macOS (using Homebrew)
   brew install node

   # Verify installation
   node --version
   npm --version
   ```

4. **Install MySQL**
   ```bash
   # Ubuntu/Debian
   sudo apt install mysql-server

   # Windows (download from mysql.com)
   # macOS (using Homebrew)
   brew install mysql

   # Start MySQL service
   sudo systemctl start mysql
   sudo systemctl enable mysql
   ```

### Application Setup

1. **Clone Repository**
   ```bash
   git clone https://github.com/Logicielab-saas/Hosting-app.git
   cd Hosting-app
   ```

2. **Install Dependencies**
   ```bash
   # Install PHP dependencies
   composer install

   # Install Node.js dependencies
   npm install
   ```

3. **Environment Configuration**
   ```bash
   # Copy environment file
   cp .env.example .env

   # Generate application key
   php artisan key:generate
   ```

4. **Database Setup**
   ```bash
   # Create database
   mysql -u root -p
   CREATE DATABASE hosting_platform;
   EXIT;

   # Update .env file with database credentials
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=hosting_platform
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

5. **Run Migrations and Seeders**
   ```bash
   # Run database migrations
   php artisan migrate

   # Seed database with default data
   php artisan db:seed
   ```

6. **Build Assets**
   ```bash
   # Build frontend assets
   npm run build

   # Or for development
   npm run dev
   ```

7. **Create Storage Link**
   ```bash
   php artisan storage:link
   ```

8. **Start Development Server**
   ```bash
   # Start Laravel development server
   php artisan serve

   # Application will be available at http://localhost:8000
   ```

## 🔧 Configuration

### Environment Variables

Key environment variables to configure in `.env`:

```env
# Application
APP_NAME="Hosting Platform"
APP_ENV=local
APP_KEY=base64:your-generated-key
APP_DEBUG=true
APP_URL=http://localhost:3000

# Database
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=hosting_platform
DB_USERNAME=root
DB_PASSWORD=password

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Cache & Session
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379
```

### Docker Services

The application includes the following Docker services:

- **app**: Laravel application (PHP 8.2-FPM)
- **nginx**: Web server (Nginx)
- **mysql**: Database server (MySQL 8.0)
- **redis**: Cache and session store (Redis)
- **mailhog**: Email testing (MailHog)

## 🚀 Usage

### Accessing the Application

- **Main Application**: http://localhost:3000
- **Admin Dashboard**: http://localhost:3000/admin/dashboard
- **Login Page**: http://localhost:3000/login
- **Register Page**: http://localhost:3000/register
- **MailHog (Email Testing)**: http://localhost:8025

### Default Credentials

After running the seeders, you can use these default credentials:

```
Admin User:
Email: <EMAIL>
Password: password

Regular User:
Email: <EMAIL>
Password: password
```

### Docker Commands

```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# View logs
docker-compose logs -f app

# Access application container
docker-compose exec app bash

# Run Artisan commands
docker-compose exec app php artisan migrate
docker-compose exec app php artisan cache:clear

# Rebuild containers
docker-compose up -d --build
```

## 🔍 Troubleshooting

### Common Issues

#### Docker Issues

1. **Port Already in Use**
   ```bash
   # Check what's using the port
   sudo netstat -tulpn | grep :3000

   # Kill the process or change port in docker-compose.yml
   ```

2. **Permission Issues (Linux)**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   chmod -R 755 storage bootstrap/cache
   ```

3. **Container Won't Start**
   ```bash
   # Check logs
   docker-compose logs app

   # Rebuild containers
   docker-compose down
   docker-compose up -d --build
   ```

#### Application Issues

1. **Database Connection Error**
   ```bash
   # Check database container
   docker-compose exec mysql mysql -u root -p

   # Verify .env database settings
   # Run migrations again
   docker-compose exec app php artisan migrate:fresh --seed
   ```

2. **Storage Permission Issues**
   ```bash
   # Fix storage permissions
   docker-compose exec app chmod -R 775 storage
   docker-compose exec app chmod -R 775 bootstrap/cache
   ```

3. **Cache Issues**
   ```bash
   # Clear all caches
   docker-compose exec app php artisan optimize:clear
   docker-compose exec app php artisan config:clear
   docker-compose exec app php artisan view:clear
   ```

### Performance Optimization

#### Production Deployment

1. **Environment Setup**
   ```bash
   # Set production environment
   APP_ENV=production
   APP_DEBUG=false

   # Optimize application
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   php artisan optimize
   ```

2. **Database Optimization**
   ```bash
   # Add database indexes
   php artisan migrate

   # Optimize database
   php artisan db:optimize
   ```

3. **Asset Optimization**
   ```bash
   # Build production assets
   npm run build

   # Optimize images and assets
   php artisan storage:link
   ```

## 📚 API Documentation

### Authentication Endpoints

```http
POST /login
POST /register
POST /logout
GET /user
```

### Admin Endpoints

```http
GET /admin/dashboard
GET /admin/users
POST /admin/users
PUT /admin/users/{id}
DELETE /admin/users/{id}

GET /admin/roles
POST /admin/roles
PUT /admin/roles/{id}
DELETE /admin/roles/{id}

GET /admin/settings
POST /admin/settings
POST /admin/settings/clear-cache
POST /admin/settings/create-backup
GET /admin/settings/system-info
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
docker-compose exec app php artisan test

# Run specific test suite
docker-compose exec app php artisan test --testsuite=Feature

# Run with coverage
docker-compose exec app php artisan test --coverage
```

### Test Database

```bash
# Create test database
docker-compose exec mysql mysql -u root -p
CREATE DATABASE hosting_platform_test;

# Run tests with test database
docker-compose exec app php artisan test --env=testing
```

## 🔒 Security

### Security Features

- **CSRF Protection** - All forms protected against CSRF attacks
- **SQL Injection Prevention** - Eloquent ORM with prepared statements
- **XSS Protection** - Blade template escaping
- **Authentication** - Secure session-based authentication
- **Authorization** - Role-based access control
- **Password Hashing** - Bcrypt password hashing
- **Rate Limiting** - API and login rate limiting

### Security Best Practices

1. **Environment Variables**
   - Never commit `.env` file to version control
   - Use strong, unique passwords
   - Rotate API keys regularly

2. **Database Security**
   - Use strong database passwords
   - Limit database user permissions
   - Regular database backups

3. **Server Security**
   - Keep Docker images updated
   - Use HTTPS in production
   - Configure firewall rules
   - Regular security updates

## 📖 Development Guide

### Project Structure

```
hosting-platform/
├── app/
│   ├── Http/Controllers/
│   ├── Models/
│   ├── Livewire/
│   └── Providers/
├── database/
│   ├── migrations/
│   ├── seeders/
│   └── factories/
├── resources/
│   ├── views/
│   ├── css/
│   └── js/
├── routes/
├── docker/
├── docker-compose.yml
└── README.md
```

### Adding New Features

1. **Create Migration**
   ```bash
   docker-compose exec app php artisan make:migration create_new_table
   ```

2. **Create Model**
   ```bash
   docker-compose exec app php artisan make:model NewModel -m
   ```

3. **Create Controller**
   ```bash
   docker-compose exec app php artisan make:controller NewController
   ```

4. **Add Routes**
   ```php
   // routes/web.php
   Route::get('/new-feature', [NewController::class, 'index']);
   ```

### Code Style

- Follow PSR-12 coding standards
- Use meaningful variable and function names
- Write comprehensive comments
- Follow Laravel best practices

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Workflow

1. **Setup Development Environment**
   ```bash
   git clone https://github.com/your-username/hosting-platform.git
   cd hosting-platform
   docker-compose up -d --build
   ```

2. **Make Changes**
   - Follow coding standards
   - Write tests for new features
   - Update documentation

3. **Test Changes**
   ```bash
   docker-compose exec app php artisan test
   ```

4. **Submit Pull Request**
   - Provide clear description
   - Include screenshots if UI changes
   - Reference related issues

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Laravel Framework
- Tailwind CSS
- Alpine.js
- Docker Community
- Open Source Contributors

## 📞 Support

For support and questions:

- **Documentation**: Check this README and inline code comments
- **Issues**: Create an issue on GitHub
- **Email**: <EMAIL>

---

**Made with ❤️ by the Hosting Platform Team**
