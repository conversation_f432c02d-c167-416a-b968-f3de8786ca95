<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PassportAuthController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/register', [PassportAuthController::class, 'register']);
Route::post('/login', [PassportAuthController::class, 'login']);

// Payment Webhook Routes (no auth required)
Route::post('/webhooks/stripe', [\App\Http\Controllers\WebhookController::class, 'stripe']);
Route::post('/webhooks/paypal', [\App\Http\Controllers\WebhookController::class, 'paypal']);
Route::post('/webhooks/crypto', [\App\Http\Controllers\WebhookController::class, 'crypto']);
Route::post('/webhooks/subscription', [\App\Http\Controllers\WebhookController::class, 'subscription']);
Route::any('/webhooks/test', [\App\Http\Controllers\WebhookController::class, 'test']);

// Protected routes for authenticated users
Route::middleware(['auth:api', 'verified'])->group(function () {
    Route::post('/logout', [PassportAuthController::class, 'logout']);
    Route::get('/user', [PassportAuthController::class, 'user']);

    // User role routes
    Route::middleware(['role:user'])->group(function () {
        Route::get('/user/profile', function () {
            return response()->json(['message' => 'User profile data']);
        });
    });

    // Admin role routes
    Route::middleware(['role:admin'])->group(function () {
        Route::get('/admin/dashboard', function () {
            return response()->json(['message' => 'Admin dashboard data']);
        });

        Route::get('/admin/users', function () {
            $users = \App\Models\User::with('roles')->get();
            return response()->json(['users' => $users]);
        });
    });
});
