<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Livewire\Auth\Login;
use App\Livewire\Auth\Register;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\VerificationController;

Route::get('/', function () {
    // If user is authenticated, redirect to admin dashboard
    if (auth()->check()) {
        return redirect()->route('admin.dashboard');
    }
    return view('welcome');
});

// Temporary test route to check if the application is working
Route::get('/test', function () {
    return view('test');
});

// Payment Webhook Routes (no auth required)
Route::post('/webhooks/stripe', [\App\Http\Controllers\WebhookController::class, 'stripe'])->name('webhooks.stripe');
Route::post('/webhooks/paypal', [\App\Http\Controllers\WebhookController::class, 'paypal'])->name('webhooks.paypal');
Route::post('/webhooks/crypto', [\App\Http\Controllers\WebhookController::class, 'crypto'])->name('webhooks.crypto');
Route::post('/webhooks/subscription', [\App\Http\Controllers\WebhookController::class, 'subscription'])->name('webhooks.subscription');
Route::any('/webhooks/test', [\App\Http\Controllers\WebhookController::class, 'test'])->name('webhooks.test');

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('login', Login::class)->name('login');
    Route::get('register', Register::class)->name('register');
});

// Logout Routes - support both GET and POST for compatibility
Route::match(['get', 'post'], 'logout', [LoginController::class, 'logout'])
    ->name('logout')
    ->middleware('auth');

Route::middleware('auth')->group(function () {
    // Email verification routes
    Route::get('email/verify', [VerificationController::class, 'show'])->name('verification.notice');
    Route::get('email/verify/{id}/{hash}', [VerificationController::class, 'verify'])->name('verification.verify');
    Route::post('email/resend', [VerificationController::class, 'resend'])->name('verification.resend');

    // Protected routes
    Route::middleware(['verified'])->group(function () {
        Route::get('/dashboard', \App\Livewire\Dashboard::class)->name('dashboard');

        Route::get('/profile', function () {
            return view('profile');
        })->name('profile');



        // Reseller routes
        Route::middleware(['role:reseller'])->prefix('reseller')->group(function () {
            Route::get('/dashboard', function () {
                return view('reseller.dashboard');
            })->name('reseller.dashboard');

            Route::get('/clients', function () {
                return view('reseller.clients');
            })->name('reseller.clients');
        });
    });
});

// Email Verification Routes
Route::get('/email/verify', function () {
    return view('auth.verify-email');
})->middleware('auth')->name('verification.notice');

Route::get('/email/verify/{id}/{hash}', function (EmailVerificationRequest $request) {
    $request->fulfill();
    return redirect('/admin/dashboard');
})->middleware(['auth', 'signed'])->name('verification.verify');

Route::post('/email/verification-notification', function (Request $request) {
    $request->user()->sendEmailVerificationNotification();
    return back()->with('message', 'Verification link sent!');
})->middleware(['auth', 'throttle:6,1'])->name('verification.send');

// Manual verification route for development
Route::get('/email/verify-manually', [App\Http\Controllers\VerifyEmailManually::class, 'verifyManually'])
    ->middleware('auth')
    ->name('verification.manual');

// Admin Dashboard Routes (moved outside for testing)
Route::middleware(['auth'])->group(function () {
    Route::get('/admin/dashboard', [\App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('admin.dashboard');
    Route::get('/admin/dashboard/data', [\App\Http\Controllers\Admin\DashboardController::class, 'getData'])->name('admin.dashboard.data');

    // Simple test route
    Route::get('/admin/test', function() {
        return 'Admin route works!';
    })->name('admin.test');

    // User Management Routes
    Route::get('/admin/users/export', [\App\Http\Controllers\Admin\UserController::class, 'export'])->name('admin.users.export');
    Route::get('/admin/users/stats', [\App\Http\Controllers\Admin\UserController::class, 'getStats'])->name('admin.users.stats');
    Route::post('/admin/users/bulk-action', [\App\Http\Controllers\Admin\UserController::class, 'bulkAction'])->name('admin.users.bulk-action');
    Route::post('/admin/users/{user}/send-verification', [\App\Http\Controllers\Admin\UserController::class, 'sendVerificationEmail'])->name('admin.users.send-verification');
    Route::post('/admin/users/{user}/reset-password', [\App\Http\Controllers\Admin\UserController::class, 'resetPassword'])->name('admin.users.reset-password');
    Route::resource('admin/users', \App\Http\Controllers\Admin\UserController::class, ['as' => 'admin']);

    Route::get('/admin/roles', function () {
        return view('admin.roles');
    })->name('admin.roles');

    Route::get('/admin/profile', [\App\Http\Controllers\Admin\ProfileController::class, 'index'])->name('admin.profile');
    Route::post('/admin/profile', [\App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('admin.profile.update');
    Route::get('/admin/account', [\App\Http\Controllers\Admin\AccountController::class, 'index'])->name('admin.account');
    Route::post('/admin/account', [\App\Http\Controllers\Admin\AccountController::class, 'update'])->name('admin.account.update');
    Route::delete('/admin/account', [\App\Http\Controllers\Admin\AccountController::class, 'deleteAccount'])->name('admin.account.delete');

    Route::get('/admin/settings', [App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('admin.settings');
    Route::post('/admin/settings', [App\Http\Controllers\Admin\SettingsController::class, 'store'])->name('admin.settings.store');
    Route::post('/admin/settings/update', [App\Http\Controllers\Admin\SettingsController::class, 'update'])->name('admin.settings.update');
    Route::post('/admin/settings/clear-cache', [App\Http\Controllers\Admin\SettingsController::class, 'clearCache'])->name('admin.settings.clear-cache');
    Route::post('/admin/settings/create-backup', [App\Http\Controllers\Admin\SettingsController::class, 'createBackup'])->name('admin.settings.create-backup');
    Route::get('/admin/settings/system-info', [App\Http\Controllers\Admin\SettingsController::class, 'getSystemInfo'])->name('admin.settings.system-info');
    Route::get('/admin/settings/export', [App\Http\Controllers\Admin\SettingsController::class, 'exportSettings'])->name('admin.settings.export');

    // Two-Factor Authentication routes
    Route::prefix('admin/two-factor')->name('admin.two-factor.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\TwoFactorController::class, 'index'])->name('index');
        Route::post('/enable', [App\Http\Controllers\Admin\TwoFactorController::class, 'enable'])->name('enable');
        Route::post('/disable', [App\Http\Controllers\Admin\TwoFactorController::class, 'disable'])->name('disable');
        Route::get('/qr-code', [App\Http\Controllers\Admin\TwoFactorController::class, 'generateQrCode'])->name('qr-code');
        Route::post('/recovery-codes', [App\Http\Controllers\Admin\TwoFactorController::class, 'generateRecoveryCodes'])->name('recovery-codes');
        Route::post('/backup-method', [App\Http\Controllers\Admin\TwoFactorController::class, 'setBackupMethod'])->name('backup-method');
        Route::post('/send-backup-code', [App\Http\Controllers\Admin\TwoFactorController::class, 'sendBackupCode'])->name('send-backup-code');
        Route::get('/verify', [App\Http\Controllers\Admin\TwoFactorController::class, 'showVerification'])->name('verify');
        Route::post('/verify-code', [App\Http\Controllers\Admin\TwoFactorController::class, 'verify'])->name('verify-code');
        Route::post('/verify-backup-code', [App\Http\Controllers\Admin\TwoFactorController::class, 'verifyBackupCode'])->name('verify-backup-code');
    });

    // Domain Management Routes
    Route::get('/admin/domains/export', [\App\Http\Controllers\Admin\DomainController::class, 'export'])->name('admin.domains.export');
    Route::post('/admin/domains/bulk-action', [\App\Http\Controllers\Admin\DomainController::class, 'bulkAction'])->name('admin.domains.bulk-action');
    Route::resource('admin/domains', \App\Http\Controllers\Admin\DomainController::class, ['as' => 'admin']);

    // DNS Management Routes
    Route::prefix('admin/domains/{domain}')->name('admin.domains.')->group(function () {
        Route::get('/dns', [\App\Http\Controllers\Admin\DnsRecordController::class, 'index'])->name('dns.index');
        Route::get('/dns/create', [\App\Http\Controllers\Admin\DnsRecordController::class, 'create'])->name('dns.create');
        Route::post('/dns', [\App\Http\Controllers\Admin\DnsRecordController::class, 'store'])->name('dns.store');
        Route::get('/dns/{record}/edit', [\App\Http\Controllers\Admin\DnsRecordController::class, 'edit'])->name('dns.edit');
        Route::put('/dns/{record}', [\App\Http\Controllers\Admin\DnsRecordController::class, 'update'])->name('dns.update');
        Route::delete('/dns/{record}', [\App\Http\Controllers\Admin\DnsRecordController::class, 'destroy'])->name('dns.destroy');
        Route::post('/dns/{record}/toggle', [\App\Http\Controllers\Admin\DnsRecordController::class, 'toggle'])->name('dns.toggle');
        Route::post('/dns/{record}/check-propagation', [\App\Http\Controllers\Admin\DnsRecordController::class, 'checkPropagation'])->name('dns.check-propagation');
        Route::post('/dns/bulk-action', [\App\Http\Controllers\Admin\DnsRecordController::class, 'bulkAction'])->name('dns.bulk-action');

        // SSL Certificate Routes
        Route::get('/ssl', [\App\Http\Controllers\Admin\SslCertificateController::class, 'index'])->name('ssl.index');
        Route::get('/ssl/create', [\App\Http\Controllers\Admin\SslCertificateController::class, 'create'])->name('ssl.create');
        Route::post('/ssl', [\App\Http\Controllers\Admin\SslCertificateController::class, 'store'])->name('ssl.store');
        Route::get('/ssl/{certificate}', [\App\Http\Controllers\Admin\SslCertificateController::class, 'show'])->name('ssl.show');
        Route::post('/ssl/{certificate}/renew', [\App\Http\Controllers\Admin\SslCertificateController::class, 'renew'])->name('ssl.renew');
        Route::post('/ssl/{certificate}/install', [\App\Http\Controllers\Admin\SslCertificateController::class, 'install'])->name('ssl.install');
        Route::delete('/ssl/{certificate}', [\App\Http\Controllers\Admin\SslCertificateController::class, 'destroy'])->name('ssl.destroy');
        Route::get('/ssl/{certificate}/download/{type}', [\App\Http\Controllers\Admin\SslCertificateController::class, 'download'])->name('ssl.download');
        Route::post('/ssl/bulk-action', [\App\Http\Controllers\Admin\SslCertificateController::class, 'bulkAction'])->name('ssl.bulk-action');
    });

    // Database Management Routes
    Route::get('/admin/databases/export', [\App\Http\Controllers\Admin\DatabaseController::class, 'export'])->name('admin.databases.export');
    Route::post('/admin/databases/bulk-action', [\App\Http\Controllers\Admin\DatabaseController::class, 'bulkAction'])->name('admin.databases.bulk-action');
    Route::post('/admin/databases/{database}/update-stats', [\App\Http\Controllers\Admin\DatabaseController::class, 'updateStats'])->name('admin.databases.update-stats');
    Route::post('/admin/databases/{database}/create-backup', [\App\Http\Controllers\Admin\DatabaseController::class, 'createBackup'])->name('admin.databases.create-backup');
    Route::post('/admin/databases/{database}/restore-backup', [\App\Http\Controllers\Admin\DatabaseController::class, 'restoreBackup'])->name('admin.databases.restore-backup');
    Route::get('/admin/databases/{database}/test-connection', [\App\Http\Controllers\Admin\DatabaseController::class, 'testConnection'])->name('admin.databases.test-connection');
    Route::resource('admin/databases', \App\Http\Controllers\Admin\DatabaseController::class, ['as' => 'admin']);

    // Database User Management Routes
    Route::resource('admin/database-users', \App\Http\Controllers\Admin\DatabaseUserController::class, ['as' => 'admin']);
    Route::post('/admin/database-users/{databaseUser}/grant-permissions', [\App\Http\Controllers\Admin\DatabaseUserController::class, 'grantPermissions'])->name('admin.database-users.grant-permissions');

    // File Manager Routes
    Route::get('/admin/files', [\App\Http\Controllers\Admin\FileManagerController::class, 'index'])->name('admin.files.index');
    Route::post('/admin/files/upload', [\App\Http\Controllers\Admin\FileManagerController::class, 'upload'])->name('admin.files.upload');
    Route::post('/admin/files/create-folder', [\App\Http\Controllers\Admin\FileManagerController::class, 'createFolder'])->name('admin.files.create-folder');
    Route::delete('/admin/files/delete', [\App\Http\Controllers\Admin\FileManagerController::class, 'delete'])->name('admin.files.delete');
    Route::put('/admin/files/{file}/rename', [\App\Http\Controllers\Admin\FileManagerController::class, 'rename'])->name('admin.files.rename');
    Route::get('/admin/files/{file}/download', [\App\Http\Controllers\Admin\FileManagerController::class, 'download'])->name('admin.files.download');
    Route::get('/admin/files/{file}/content', [\App\Http\Controllers\Admin\FileManagerController::class, 'getContent'])->name('admin.files.content');
    Route::put('/admin/files/{file}/content', [\App\Http\Controllers\Admin\FileManagerController::class, 'updateContent'])->name('admin.files.update-content');

    // Backup Management Routes
    Route::resource('admin/backups', \App\Http\Controllers\Admin\BackupController::class, ['as' => 'admin']);
    Route::get('/admin/backups/{backup}/download', [\App\Http\Controllers\Admin\BackupController::class, 'download'])->name('admin.backups.download');
    Route::post('/admin/backups/{backup}/restore', [\App\Http\Controllers\Admin\BackupController::class, 'restore'])->name('admin.backups.restore');
    Route::post('/admin/backups/{backup}/retry', [\App\Http\Controllers\Admin\BackupController::class, 'retry'])->name('admin.backups.retry');
    Route::post('/admin/backups/bulk-action', [\App\Http\Controllers\Admin\BackupController::class, 'bulkAction'])->name('admin.backups.bulk-action');
    Route::post('/admin/backups/cleanup', [\App\Http\Controllers\Admin\BackupController::class, 'cleanup'])->name('admin.backups.cleanup');

    // Payment Management Routes
    Route::get('/admin/payments/methods', [\App\Http\Controllers\Admin\PaymentController::class, 'paymentMethods'])->name('admin.payments.methods');
    Route::get('/admin/payments/methods/create', [\App\Http\Controllers\Admin\PaymentController::class, 'createPaymentMethod'])->name('admin.payments.methods.create');
    Route::post('/admin/payments/methods', [\App\Http\Controllers\Admin\PaymentController::class, 'storePaymentMethod'])->name('admin.payments.methods.store');
    Route::post('/admin/payments/methods/{paymentMethod}/default', [\App\Http\Controllers\Admin\PaymentController::class, 'setDefaultPaymentMethod'])->name('admin.payments.methods.default');
    Route::delete('/admin/payments/methods/{paymentMethod}', [\App\Http\Controllers\Admin\PaymentController::class, 'deletePaymentMethod'])->name('admin.payments.methods.delete');

    Route::get('/admin/payments/transactions', [\App\Http\Controllers\Admin\PaymentController::class, 'transactions'])->name('admin.payments.transactions');
    Route::get('/admin/payments/transactions/{transaction}', [\App\Http\Controllers\Admin\PaymentController::class, 'showTransaction'])->name('admin.payments.transactions.show');
    Route::post('/admin/payments/transactions/{transaction}/refund', [\App\Http\Controllers\Admin\PaymentController::class, 'processRefund'])->name('admin.payments.transactions.refund');
    Route::post('/admin/payments/pay', [\App\Http\Controllers\Admin\PaymentController::class, 'makePayment'])->name('admin.payments.pay');

    // Server Management Routes
    Route::resource('admin/servers', \App\Http\Controllers\Admin\ServerController::class, ['as' => 'admin']);
    Route::post('/admin/servers/test-connection-form', [\App\Http\Controllers\Admin\ServerController::class, 'testConnectionForm'])->name('admin.servers.test-connection-form');
    Route::post('/admin/servers/{server}/ping', [\App\Http\Controllers\Admin\ServerController::class, 'ping'])->name('admin.servers.ping');
    Route::post('/admin/servers/{server}/update-monitoring', [\App\Http\Controllers\Admin\ServerController::class, 'updateMonitoring'])->name('admin.servers.update-monitoring');
    Route::post('/admin/servers/{server}/test-connection', [\App\Http\Controllers\Admin\ServerController::class, 'testConnection'])->name('admin.servers.test-connection');
    Route::post('/admin/servers/{server}/restart-service', [\App\Http\Controllers\Admin\ServerController::class, 'restartService'])->name('admin.servers.restart-service');
    Route::post('/admin/servers/{server}/execute-command', [\App\Http\Controllers\Admin\ServerController::class, 'executeCommand'])->name('admin.servers.execute-command');
    Route::get('/admin/servers/{server}/logs', [\App\Http\Controllers\Admin\ServerController::class, 'getLogs'])->name('admin.servers.logs');
    Route::post('/admin/servers/bulk-action', [\App\Http\Controllers\Admin\ServerController::class, 'bulkAction'])->name('admin.servers.bulk-action');

    // Applications Management Routes
    Route::resource('admin/applications', \App\Http\Controllers\Admin\ApplicationController::class, ['as' => 'admin']);
    Route::post('/admin/applications/install-template', [\App\Http\Controllers\Admin\ApplicationController::class, 'installFromTemplate'])->name('admin.applications.install-template');
    Route::post('/admin/applications/{application}/update', [\App\Http\Controllers\Admin\ApplicationController::class, 'updateApp'])->name('admin.applications.update-app');
    Route::post('/admin/applications/{application}/restart', [\App\Http\Controllers\Admin\ApplicationController::class, 'restart'])->name('admin.applications.restart');
    Route::get('/admin/applications/{application}/health', [\App\Http\Controllers\Admin\ApplicationController::class, 'checkHealth'])->name('admin.applications.health');
    Route::get('/admin/applications/monitor/all', [\App\Http\Controllers\Admin\ApplicationController::class, 'monitorAll'])->name('admin.applications.monitor-all');
    Route::delete('/admin/applications/{application}/uninstall', [\App\Http\Controllers\Admin\ApplicationController::class, 'uninstall'])->name('admin.applications.uninstall');
    Route::post('/admin/applications/bulk-action', [\App\Http\Controllers\Admin\ApplicationController::class, 'bulkAction'])->name('admin.applications.bulk-action');

    // Simple File Manager Routes
    Route::get('/admin/simple-file-manager', [\App\Http\Controllers\Admin\SimpleFileManagerController::class, 'index'])->name('admin.simple-file-manager.index');
    Route::get('/admin/simple-file-manager/list', [\App\Http\Controllers\Admin\SimpleFileManagerController::class, 'listDirectory'])->name('admin.simple-file-manager.list');
    Route::post('/admin/simple-file-manager/create-directory', [\App\Http\Controllers\Admin\SimpleFileManagerController::class, 'createDirectory'])->name('admin.simple-file-manager.create-directory');
    Route::post('/admin/simple-file-manager/upload', [\App\Http\Controllers\Admin\SimpleFileManagerController::class, 'uploadFiles'])->name('admin.simple-file-manager.upload');
    Route::post('/admin/simple-file-manager/delete', [\App\Http\Controllers\Admin\SimpleFileManagerController::class, 'delete'])->name('admin.simple-file-manager.delete');
    Route::post('/admin/simple-file-manager/rename', [\App\Http\Controllers\Admin\SimpleFileManagerController::class, 'rename'])->name('admin.simple-file-manager.rename');
    Route::get('/admin/simple-file-manager/download', [\App\Http\Controllers\Admin\SimpleFileManagerController::class, 'downloadFile'])->name('admin.simple-file-manager.download');
    Route::get('/admin/simple-file-manager/edit', [\App\Http\Controllers\Admin\SimpleFileManagerController::class, 'edit'])->name('admin.simple-file-manager.edit');
    Route::get('/admin/simple-file-manager/content', [\App\Http\Controllers\Admin\SimpleFileManagerController::class, 'getFileContent'])->name('admin.simple-file-manager.content');
    Route::post('/admin/simple-file-manager/save', [\App\Http\Controllers\Admin\SimpleFileManagerController::class, 'saveFileContent'])->name('admin.simple-file-manager.save');

    // Integration Management Routes
    Route::get('/admin/integration', [\App\Http\Controllers\Admin\IntegrationController::class, 'index'])->name('admin.integration.index');
    Route::post('/admin/integration/test-connection', [\App\Http\Controllers\Admin\IntegrationController::class, 'testConnection'])->name('admin.integration.test-connection');
    Route::get('/admin/integration/real-time-data', [\App\Http\Controllers\Admin\IntegrationController::class, 'getRealTimeData'])->name('admin.integration.real-time-data');

    // Other New Admin Routes (From the images you shared)
    // Team Management Routes
    Route::get('/admin/team', [\App\Http\Controllers\Admin\TeamController::class, 'index'])->name('admin.team.index');
    Route::post('/admin/team/add', [\App\Http\Controllers\Admin\TeamController::class, 'store'])->name('admin.team.add');
    Route::post('/admin/team/update-status', [\App\Http\Controllers\Admin\TeamController::class, 'updateStatus'])->name('admin.team.update-status');
    Route::post('/admin/team/remove', [\App\Http\Controllers\Admin\TeamController::class, 'remove'])->name('admin.team.remove');
    Route::get('/admin/team/real-time-data', [\App\Http\Controllers\Admin\TeamController::class, 'getRealTimeData'])->name('admin.team.real-time-data');
    // Billing Dashboard Routes
    Route::get('/admin/billing/dashboard', [\App\Http\Controllers\Admin\BillingController::class, 'index'])->name('admin.billing.dashboard');
    Route::post('/admin/billing/process-refund', [\App\Http\Controllers\Admin\BillingController::class, 'processRefund'])->name('admin.billing.process-refund');
    Route::post('/admin/billing/retry-payment', [\App\Http\Controllers\Admin\BillingController::class, 'retryPayment'])->name('admin.billing.retry-payment');
    Route::post('/admin/billing/generate-invoice', [\App\Http\Controllers\Admin\BillingController::class, 'generateInvoice'])->name('admin.billing.generate-invoice');
    Route::get('/admin/billing/real-time-data', [\App\Http\Controllers\Admin\BillingController::class, 'getRealTimeData'])->name('admin.billing.real-time-data');
    // InsightHub Routes
    Route::get('/admin/insights', [\App\Http\Controllers\Admin\InsightController::class, 'index'])->name('admin.insights.index');
    Route::post('/admin/insights/export', [\App\Http\Controllers\Admin\InsightController::class, 'exportData'])->name('admin.insights.export');
    Route::post('/admin/insights/generate-report', [\App\Http\Controllers\Admin\InsightController::class, 'generateReport'])->name('admin.insights.generate-report');
    Route::post('/admin/insights/setup-alert', [\App\Http\Controllers\Admin\InsightController::class, 'setupAlert'])->name('admin.insights.setup-alert');
    Route::get('/admin/insights/real-time-data', [\App\Http\Controllers\Admin\InsightController::class, 'getRealTimeData'])->name('admin.insights.real-time-data');
    // White-Label Management Routes
    Route::get('/admin/white-label', [\App\Http\Controllers\Admin\WhiteLabelController::class, 'index'])->name('admin.white-label.index');
    Route::post('/admin/white-label/update-settings', [\App\Http\Controllers\Admin\WhiteLabelController::class, 'updateSettings'])->name('admin.white-label.update-settings');
    Route::post('/admin/white-label/upload-logo', [\App\Http\Controllers\Admin\WhiteLabelController::class, 'uploadLogo'])->name('admin.white-label.upload-logo');
    Route::post('/admin/white-label/preview-theme', [\App\Http\Controllers\Admin\WhiteLabelController::class, 'previewTheme'])->name('admin.white-label.preview-theme');
    Route::post('/admin/white-label/deploy', [\App\Http\Controllers\Admin\WhiteLabelController::class, 'deploy'])->name('admin.white-label.deploy');
    // Reseller Panel Routes
    Route::get('/admin/reseller', [\App\Http\Controllers\Admin\ResellerController::class, 'index'])->name('admin.reseller.index');
    Route::post('/admin/reseller/approve', [\App\Http\Controllers\Admin\ResellerController::class, 'approve'])->name('admin.reseller.approve');
    Route::post('/admin/reseller/suspend', [\App\Http\Controllers\Admin\ResellerController::class, 'suspend'])->name('admin.reseller.suspend');
    Route::post('/admin/reseller/process-payout', [\App\Http\Controllers\Admin\ResellerController::class, 'processPayout'])->name('admin.reseller.process-payout');
    Route::get('/admin/reseller/real-time-data', [\App\Http\Controllers\Admin\ResellerController::class, 'getRealTimeData'])->name('admin.reseller.real-time-data');
    // Premium Care Management Routes
    Route::get('/admin/premium-care', [\App\Http\Controllers\Admin\PremiumCareController::class, 'index'])->name('admin.premium-care.index');
    Route::post('/admin/premium-care/assign-ticket', [\App\Http\Controllers\Admin\PremiumCareController::class, 'assignTicket'])->name('admin.premium-care.assign-ticket');
    Route::post('/admin/premium-care/upgrade-client', [\App\Http\Controllers\Admin\PremiumCareController::class, 'upgradeClient'])->name('admin.premium-care.upgrade-client');
    Route::get('/admin/premium-care/real-time-data', [\App\Http\Controllers\Admin\PremiumCareController::class, 'getRealTimeData'])->name('admin.premium-care.real-time-data');
    // Documentation Management Routes
    Route::get('/admin/documentation', [\App\Http\Controllers\Admin\DocumentationController::class, 'index'])->name('admin.documentation.index');
    Route::post('/admin/documentation/create-article', [\App\Http\Controllers\Admin\DocumentationController::class, 'createArticle'])->name('admin.documentation.create-article');
    Route::post('/admin/documentation/update-article-status', [\App\Http\Controllers\Admin\DocumentationController::class, 'updateArticleStatus'])->name('admin.documentation.update-article-status');
    Route::post('/admin/documentation/approve-feedback', [\App\Http\Controllers\Admin\DocumentationController::class, 'approveFeedback'])->name('admin.documentation.approve-feedback');
    Route::get('/admin/documentation/real-time-data', [\App\Http\Controllers\Admin\DocumentationController::class, 'getRealTimeData'])->name('admin.documentation.real-time-data');
    // API Documentation Management Routes
    Route::get('/admin/api-docs', [\App\Http\Controllers\Admin\ApiDocsController::class, 'index'])->name('admin.api-docs.index');
    Route::post('/admin/api-docs/generate', [\App\Http\Controllers\Admin\ApiDocsController::class, 'generateDocs'])->name('admin.api-docs.generate');
    Route::post('/admin/api-docs/update-endpoint-status', [\App\Http\Controllers\Admin\ApiDocsController::class, 'updateEndpointStatus'])->name('admin.api-docs.update-endpoint-status');
    Route::get('/admin/api-docs/real-time-data', [\App\Http\Controllers\Admin\ApiDocsController::class, 'getRealTimeData'])->name('admin.api-docs.real-time-data');
    // Referral Program Management Routes
    Route::get('/admin/referral', [\App\Http\Controllers\Admin\ReferralController::class, 'index'])->name('admin.referral.index');
    Route::post('/admin/referral/update-tier', [\App\Http\Controllers\Admin\ReferralController::class, 'updateTier'])->name('admin.referral.update-tier');
    Route::post('/admin/referral/process-payout', [\App\Http\Controllers\Admin\ReferralController::class, 'processPayout'])->name('admin.referral.process-payout');
    Route::get('/admin/referral/real-time-data', [\App\Http\Controllers\Admin\ReferralController::class, 'getRealTimeData'])->name('admin.referral.real-time-data');
    // Affiliate Program Management Routes
    Route::get('/admin/affiliate', [\App\Http\Controllers\Admin\AffiliateController::class, 'index'])->name('admin.affiliate.index');
    Route::post('/admin/affiliate/create-program', [\App\Http\Controllers\Admin\AffiliateController::class, 'createProgram'])->name('admin.affiliate.create-program');
    Route::post('/admin/affiliate/update-program-status', [\App\Http\Controllers\Admin\AffiliateController::class, 'updateProgramStatus'])->name('admin.affiliate.update-program-status');
    Route::get('/admin/affiliate/real-time-data', [\App\Http\Controllers\Admin\AffiliateController::class, 'getRealTimeData'])->name('admin.affiliate.real-time-data');
});

// Dashboard Routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Routes accessible by all authenticated users
    Route::get('/dashboard', App\Livewire\Dashboard::class)->name('dashboard');

    // Routes accessible by users with 'user' role
    Route::middleware(['role:user'])->group(function () {
        Route::get('/user/settings', function () {
            return view('user.settings');
        })->name('user.settings');
    });
});

// Token Test Route
Route::get('/token', function () {
    return view('token-test');
})->name('token.test')->middleware('auth');
