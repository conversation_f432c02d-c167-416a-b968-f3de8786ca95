<x-layouts.app title="Passport Test">
    <div class="py-10">
        <header>
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h1 class="text-3xl font-bold text-gray-900">Passport API Test</h1>
            </div>
        </header>
        <main>
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <h2 class="text-xl font-semibold mb-4">Your Access Token</h2>
                        
                        @if(session()->has('access_token'))
                            <div class="mb-4">
                                <p class="text-gray-600 mb-2">Your current access token is:</p>
                                <div class="bg-gray-100 p-4 rounded overflow-x-auto">
                                    <code class="text-sm text-gray-800">{{ session('access_token') }}</code>
                                </div>
                            </div>
                        @else
                            <p class="text-red-600 mb-4">No access token found in session.</p>
                        @endif

                        <h2 class="text-xl font-semibold mb-4 mt-8">Test API Endpoints</h2>
                        
                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg font-medium mb-2">Get User Info</h3>
                                <button id="getUserBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Test GET /api/user
                                </button>
                                <div id="userResult" class="mt-2 bg-gray-100 p-4 rounded hidden">
                                    <pre class="text-sm text-gray-800"></pre>
                                </div>
                            </div>

                            <div>
                                <h3 class="text-lg font-medium mb-2">Test Protected Resource</h3>
                                <button id="testProtectedBtn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                    Test GET /api/test
                                </button>
                                <div id="protectedResult" class="mt-2 bg-gray-100 p-4 rounded hidden">
                                    <pre class="text-sm text-gray-800"></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const token = "{{ session('access_token') }}";
            
            document.getElementById('getUserBtn').addEventListener('click', function() {
                fetchEndpoint('/api/user', 'userResult');
            });
            
            document.getElementById('testProtectedBtn').addEventListener('click', function() {
                fetchEndpoint('/api/test', 'protectedResult');
            });
            
            function fetchEndpoint(url, resultId) {
                const resultDiv = document.getElementById(resultId);
                const resultPre = resultDiv.querySelector('pre');
                
                resultDiv.classList.remove('hidden');
                resultPre.textContent = 'Loading...';
                
                fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': 'Bearer ' + token
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    resultPre.textContent = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    resultPre.textContent = 'Error: ' + error.message;
                });
            }
        });
    </script>
</x-layouts.app>
