<x-layouts.app title="Verify Email">
    <div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100">
        <div class="w-full sm:max-w-md mt-6 px-6 py-4 bg-white shadow-md overflow-hidden sm:rounded-lg">
            <h2 class="text-2xl font-bold mb-4 text-center">Verify Your Email Address</h2>

            <div class="mb-4 text-sm text-gray-600">
                Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another.
            </div>

            @if (session('message'))
                <div class="mb-4 font-medium text-sm text-green-600">
                    {{ session('message') }}
                </div>
            @endif

            <div class="mb-4 p-4 bg-blue-100 border-l-4 border-blue-500 text-blue-700">
                <p class="font-bold">Email Verification Instructions</p>
                <ol class="list-decimal ml-5 mt-2">
                    <li>Check your email inbox for a message from <strong>{{ config('mail.from.name') }} &lt;{{ config('mail.from.address') }}&gt;</strong></li>
                    <li>Open the email and click on the verification link</li>
                    <li>If you don't see the email, check your spam/junk folder</li>
                    <li>If you still don't see it, click the "Resend Verification Email" button below</li>
                </ol>
            </div>

            @if (app()->environment('local'))
                <div class="mb-4 p-4 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700">
                    <p class="font-bold">Development Environment</p>
                    <p>In development mode, you can use the "Verify Manually" button below to skip the email verification process.</p>
                    <p class="mt-2">Email configuration: <strong>{{ config('mail.default') }}</strong> via <strong>{{ config('mail.mailers.smtp.host') }}:{{ config('mail.mailers.smtp.port') }}</strong></p>
                </div>
            @endif

            <div class="mt-4 flex items-center justify-between">
                <div class="flex space-x-4">
                    <form method="POST" action="{{ route('verification.send') }}">
                        @csrf
                        <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                            Resend Verification Email
                        </button>
                    </form>

                    <a href="{{ route('verification.manual') }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                        Verify Manually (Dev Only)
                    </a>
                </div>

                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <button type="submit" class="text-sm text-red-600 hover:text-red-800">
                        Log Out
                    </button>
                </form>
            </div>
        </div>
    </div>
</x-layouts.app>
