<x-layouts.app title="Access Token">
    <div class="py-10">
        <header>
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h1 class="text-3xl font-bold text-gray-900">Your Access Token</h1>
            </div>
        </header>
        <main>
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        @if(session()->has('access_token'))
                            <div class="mb-4">
                                <p class="text-gray-600 mb-2">Your current access token is:</p>
                                <div class="bg-gray-100 p-4 rounded overflow-x-auto">
                                    <code class="text-sm text-gray-800 break-all">{{ session('access_token') }}</code>
                                </div>
                            </div>
                            
                            <div class="mt-6">
                                <p class="text-gray-600 mb-2">You can use this token in API requests with the following header:</p>
                                <div class="bg-gray-100 p-4 rounded">
                                    <code class="text-sm text-gray-800">Authorization: Bearer {{ session('access_token') }}</code>
                                </div>
                            </div>
                        @else
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                                <p>No access token found in session. Please try logging in again.</p>
                            </div>
                            
                            <div class="mt-4">
                                <a href="{{ route('login') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Go to Login
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </main>
    </div>
</x-layouts.app>
