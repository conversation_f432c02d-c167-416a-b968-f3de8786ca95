<x-layouts.app title="User Settings">
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h2 class="text-2xl font-bold mb-4">User Settings</h2>
                    
                    <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
                        <p class="font-bold">Role-Protected Page</p>
                        <p>This page is only accessible to users with the 'user' role.</p>
                    </div>
                    
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold">Account Settings</h3>
                        <p>This is where users can manage their account settings.</p>
                    </div>
                    
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold">Notification Preferences</h3>
                        <p>This is where users can manage their notification preferences.</p>
                    </div>
                    
                    <div class="mt-6">
                        <a href="{{ route('profile') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Back to Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
