@extends('layouts.admin')

@section('title', 'Add Payment Method')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Add Payment Method</h1>
                <a href="{{ route('admin.payments.methods') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Payment Methods
                </a>
            </div>

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            @endif

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Payment Method Details</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('admin.payments.methods.store') }}" id="paymentMethodForm">
                                @csrf

                                <!-- Gateway Selection -->
                                <div class="form-group">
                                    <label for="gateway" class="form-label">Payment Gateway</label>
                                    <select class="form-control @error('gateway') is-invalid @enderror" 
                                            id="gateway" name="gateway" required onchange="updatePaymentTypes()">
                                        <option value="">Select a payment gateway</option>
                                        @foreach($availableGateways as $gateway)
                                            <option value="{{ $gateway->slug }}" {{ old('gateway') == $gateway->slug ? 'selected' : '' }}>
                                                {{ $gateway->name }}
                                                @if($gateway->fee_percentage > 0 || $gateway->fee_fixed > 0)
                                                    ({{ number_format($gateway->fee_percentage * 100, 2) }}% + ${{ number_format($gateway->fee_fixed, 2) }})
                                                @endif
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('gateway')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Payment Type Selection -->
                                <div class="form-group">
                                    <label for="type" class="form-label">Payment Type</label>
                                    <select class="form-control @error('type') is-invalid @enderror" 
                                            id="type" name="type" required onchange="showPaymentFields()">
                                        <option value="">Select payment type</option>
                                        <option value="credit_card" {{ old('type') == 'credit_card' ? 'selected' : '' }}>Credit Card</option>
                                        <option value="paypal" {{ old('type') == 'paypal' ? 'selected' : '' }}>PayPal</option>
                                        <option value="bank_account" {{ old('type') == 'bank_account' ? 'selected' : '' }}>Bank Account</option>
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Credit Card Fields -->
                                <div id="credit_card_fields" class="payment-fields" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label for="cardholder_name" class="form-label">Cardholder Name</label>
                                                <input type="text" class="form-control @error('cardholder_name') is-invalid @enderror" 
                                                       id="cardholder_name" name="cardholder_name" value="{{ old('cardholder_name') }}">
                                                @error('cardholder_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="form-group">
                                                <label for="card_number" class="form-label">Card Number</label>
                                                <input type="text" class="form-control @error('card_number') is-invalid @enderror" 
                                                       id="card_number" name="card_number" value="{{ old('card_number') }}"
                                                       placeholder="1234 5678 9012 3456" maxlength="19">
                                                @error('card_number')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="cvv" class="form-label">CVV</label>
                                                <input type="text" class="form-control @error('cvv') is-invalid @enderror" 
                                                       id="cvv" name="cvv" value="{{ old('cvv') }}"
                                                       placeholder="123" maxlength="4">
                                                @error('cvv')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="exp_month" class="form-label">Expiry Month</label>
                                                <select class="form-control @error('exp_month') is-invalid @enderror" 
                                                        id="exp_month" name="exp_month">
                                                    <option value="">Month</option>
                                                    @for($i = 1; $i <= 12; $i++)
                                                        <option value="{{ sprintf('%02d', $i) }}" {{ old('exp_month') == sprintf('%02d', $i) ? 'selected' : '' }}>
                                                            {{ sprintf('%02d', $i) }}
                                                        </option>
                                                    @endfor
                                                </select>
                                                @error('exp_month')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="exp_year" class="form-label">Expiry Year</label>
                                                <select class="form-control @error('exp_year') is-invalid @enderror" 
                                                        id="exp_year" name="exp_year">
                                                    <option value="">Year</option>
                                                    @for($i = date('Y'); $i <= date('Y') + 15; $i++)
                                                        <option value="{{ $i }}" {{ old('exp_year') == $i ? 'selected' : '' }}>
                                                            {{ $i }}
                                                        </option>
                                                    @endfor
                                                </select>
                                                @error('exp_year')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- PayPal Fields -->
                                <div id="paypal_fields" class="payment-fields" style="display: none;">
                                    <div class="form-group">
                                        <label for="paypal_email" class="form-label">PayPal Email</label>
                                        <input type="email" class="form-control @error('paypal_email') is-invalid @enderror" 
                                               id="paypal_email" name="paypal_email" value="{{ old('paypal_email') }}"
                                               placeholder="<EMAIL>">
                                        @error('paypal_email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Bank Account Fields -->
                                <div id="bank_account_fields" class="payment-fields" style="display: none;">
                                    <div class="form-group">
                                        <label for="bank_name" class="form-label">Bank Name</label>
                                        <input type="text" class="form-control @error('bank_name') is-invalid @enderror" 
                                               id="bank_name" name="bank_name" value="{{ old('bank_name') }}">
                                        @error('bank_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="account_number" class="form-label">Account Number</label>
                                                <input type="text" class="form-control @error('account_number') is-invalid @enderror" 
                                                       id="account_number" name="account_number" value="{{ old('account_number') }}">
                                                @error('account_number')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="routing_number" class="form-label">Routing Number</label>
                                                <input type="text" class="form-control @error('routing_number') is-invalid @enderror" 
                                                       id="routing_number" name="routing_number" value="{{ old('routing_number') }}">
                                                @error('routing_number')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Add Payment Method
                                    </button>
                                    <a href="{{ route('admin.payments.methods') }}" class="btn btn-secondary ml-2">Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updatePaymentTypes() {
    const gateway = document.getElementById('gateway').value;
    const typeSelect = document.getElementById('type');
    
    // Reset type selection
    typeSelect.value = '';
    showPaymentFields();
    
    // Enable/disable payment types based on gateway
    const options = typeSelect.querySelectorAll('option');
    options.forEach(option => {
        if (option.value === '') return;
        
        if (gateway === 'stripe' && option.value === 'paypal') {
            option.disabled = true;
        } else if (gateway === 'paypal' && option.value !== 'paypal' && option.value !== '') {
            option.disabled = true;
        } else {
            option.disabled = false;
        }
    });
}

function showPaymentFields() {
    const type = document.getElementById('type').value;
    const fields = document.querySelectorAll('.payment-fields');
    
    // Hide all payment fields
    fields.forEach(field => {
        field.style.display = 'none';
    });
    
    // Show relevant fields
    if (type) {
        const targetFields = document.getElementById(type + '_fields');
        if (targetFields) {
            targetFields.style.display = 'block';
        }
    }
}

// Format card number input
document.addEventListener('DOMContentLoaded', function() {
    const cardNumberInput = document.getElementById('card_number');
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            e.target.value = formattedValue;
        });
    }
    
    // Show fields if type is already selected (for validation errors)
    showPaymentFields();
});
</script>
@endsection
