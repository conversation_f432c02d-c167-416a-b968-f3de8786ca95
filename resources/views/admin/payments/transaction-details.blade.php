@extends('layouts.admin')

@section('title', 'Transaction Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Transaction Details</h1>
                <a href="{{ route('admin.payments.transactions') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Transactions
                </a>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            @endif

            <div class="row">
                <!-- Transaction Overview -->
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">Transaction Overview</h6>
                            <span class="badge {{ $transaction->status_color }} badge-lg">
                                {{ ucfirst($transaction->status) }}
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="font-weight-bold">Transaction ID:</td>
                                            <td><code>{{ $transaction->transaction_id }}</code></td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Reference Number:</td>
                                            <td><code>{{ $transaction->reference_number }}</code></td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Type:</td>
                                            <td>
                                                <span class="badge badge-{{ $transaction->type === 'refund' ? 'warning' : 'info' }}">
                                                    {{ ucfirst($transaction->type) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Amount:</td>
                                            <td>
                                                <span class="{{ $transaction->amount < 0 ? 'text-danger' : 'text-success' }} h5">
                                                    {{ $transaction->formatted_amount }}
                                                </span>
                                            </td>
                                        </tr>
                                        @if($transaction->fee_amount > 0)
                                        <tr>
                                            <td class="font-weight-bold">Fee:</td>
                                            <td class="text-muted">{{ $transaction->formatted_fee_amount }}</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Net Amount:</td>
                                            <td class="text-success">{{ $transaction->formatted_net_amount }}</td>
                                        </tr>
                                        @endif
                                        <tr>
                                            <td class="font-weight-bold">Currency:</td>
                                            <td>{{ strtoupper($transaction->currency) }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="font-weight-bold">Gateway:</td>
                                            <td>
                                                <span class="badge badge-secondary">
                                                    {{ ucfirst($transaction->gateway) }}
                                                </span>
                                            </td>
                                        </tr>
                                        @if($transaction->gateway_transaction_id)
                                        <tr>
                                            <td class="font-weight-bold">Gateway ID:</td>
                                            <td><code>{{ $transaction->gateway_transaction_id }}</code></td>
                                        </tr>
                                        @endif
                                        <tr>
                                            <td class="font-weight-bold">Payment Method:</td>
                                            <td>{{ ucfirst(str_replace('_', ' ', $transaction->payment_method_type)) }}</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Created:</td>
                                            <td>{{ $transaction->created_at->format('M d, Y H:i:s') }}</td>
                                        </tr>
                                        @if($transaction->processed_at)
                                        <tr>
                                            <td class="font-weight-bold">Processed:</td>
                                            <td>{{ $transaction->processed_at->format('M d, Y H:i:s') }}</td>
                                        </tr>
                                        @endif
                                        @if($transaction->failed_at)
                                        <tr>
                                            <td class="font-weight-bold">Failed:</td>
                                            <td>{{ $transaction->failed_at->format('M d, Y H:i:s') }}</td>
                                        </tr>
                                        @endif
                                    </table>
                                </div>
                            </div>

                            @if($transaction->description)
                            <div class="mt-3">
                                <h6 class="font-weight-bold">Description:</h6>
                                <p class="text-muted">{{ $transaction->description }}</p>
                            </div>
                            @endif

                            @if($transaction->failure_reason)
                            <div class="mt-3">
                                <h6 class="font-weight-bold text-danger">Failure Reason:</h6>
                                <div class="alert alert-danger">
                                    <strong>{{ $transaction->failure_code ? '[' . $transaction->failure_code . '] ' : '' }}</strong>
                                    {{ $transaction->failure_reason }}
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Gateway Response -->
                    @if($transaction->gateway_response)
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Gateway Response</h6>
                        </div>
                        <div class="card-body">
                            <pre class="bg-light p-3 rounded"><code>{{ json_encode($transaction->gateway_response, JSON_PRETTY_PRINT) }}</code></pre>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Actions & Related Info -->
                <div class="col-lg-4">
                    <!-- Actions -->
                    @if($transaction->status === 'completed' && $transaction->type === 'payment' && $transaction->amount > 0)
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                        </div>
                        <div class="card-body">
                            <button type="button" class="btn btn-warning btn-block" 
                                    data-toggle="modal" data-target="#refundModal">
                                <i class="fas fa-undo"></i> Process Refund
                            </button>
                        </div>
                    </div>
                    @endif

                    <!-- Payment Method Info -->
                    @if($transaction->paymentMethod)
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Payment Method</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <i class="{{ $transaction->paymentMethod->icon }} fa-2x text-primary mr-3"></i>
                                <div>
                                    <h6 class="mb-1">{{ $transaction->paymentMethod->display_name }}</h6>
                                    <small class="text-muted">{{ ucfirst($transaction->paymentMethod->gateway) }}</small>
                                </div>
                            </div>
                            @if($transaction->paymentMethod->is_default)
                                <span class="badge badge-primary">Default Method</span>
                            @endif
                        </div>
                    </div>
                    @endif

                    <!-- Subscription Info -->
                    @if($transaction->subscription)
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Subscription</h6>
                        </div>
                        <div class="card-body">
                            <h6>{{ $transaction->subscription->plan->name ?? 'Unknown Plan' }}</h6>
                            <p class="text-muted mb-2">{{ $transaction->subscription->formatted_amount }} / {{ $transaction->subscription->billing_cycle }}</p>
                            <span class="badge {{ $transaction->subscription->status_color }}">
                                {{ ucfirst($transaction->subscription->status) }}
                            </span>
                        </div>
                    </div>
                    @endif

                    <!-- Invoice Info -->
                    @if($transaction->invoice)
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Invoice</h6>
                        </div>
                        <div class="card-body">
                            <h6>{{ $transaction->invoice->invoice_number }}</h6>
                            <p class="text-muted mb-2">{{ $transaction->invoice->formatted_total_amount }}</p>
                            <span class="badge {{ $transaction->invoice->status_color }}">
                                {{ ucfirst($transaction->invoice->status) }}
                            </span>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Refund Modal -->
@if($transaction->status === 'completed' && $transaction->type === 'payment' && $transaction->amount > 0)
<div class="modal fade" id="refundModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Process Refund</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ route('admin.payments.transactions.refund', $transaction) }}">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="amount">Refund Amount</label>
                        <input type="number" class="form-control" id="amount" name="amount" 
                               step="0.01" min="0.01" max="{{ $transaction->amount }}" 
                               value="{{ $transaction->amount }}" required>
                        <small class="form-text text-muted">
                            Maximum refund: {{ $transaction->formatted_amount }}
                        </small>
                    </div>
                    <div class="form-group">
                        <label for="reason">Reason</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required 
                                  placeholder="Please provide a reason for the refund"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Process Refund</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif

<style>
.badge-lg {
    font-size: 0.9em;
    padding: 0.5rem 0.75rem;
}
</style>
@endsection
