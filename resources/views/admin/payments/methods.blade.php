@extends('layouts.admin')

@section('title', 'Payment Methods')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Payment Methods</h1>
                <a href="{{ route('admin.payments.methods.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add Payment Method
                </a>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            @endif

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Your Payment Methods</h6>
                </div>
                <div class="card-body">
                    @if($paymentMethods->count() > 0)
                        <div class="row">
                            @foreach($paymentMethods as $method)
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card {{ $method->is_default ? 'border-primary' : '' }}">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <div class="d-flex align-items-center">
                                                    <i class="{{ $method->icon }} fa-2x text-primary mr-3"></i>
                                                    <div>
                                                        <h6 class="card-title mb-1">{{ $method->display_name }}</h6>
                                                        <small class="text-muted">{{ ucfirst($method->gateway) }}</small>
                                                    </div>
                                                </div>
                                                @if($method->is_default)
                                                    <span class="badge badge-primary">Default</span>
                                                @endif
                                            </div>

                                            <div class="mb-3">
                                                @if($method->type === 'credit_card')
                                                    <p class="text-muted mb-1">
                                                        <small>Expires: {{ $method->exp_month }}/{{ $method->exp_year }}</small>
                                                    </p>
                                                    @if($method->isExpiringSoon())
                                                        <div class="alert alert-warning alert-sm py-1 px-2 mb-2">
                                                            <small><i class="fas fa-exclamation-triangle"></i> Expires soon</small>
                                                        </div>
                                                    @endif
                                                @elseif($method->type === 'paypal')
                                                    <p class="text-muted mb-1">
                                                        <small>{{ $method->paypal_email }}</small>
                                                    </p>
                                                @endif

                                                @if($method->verified_at)
                                                    <span class="badge badge-success badge-sm">
                                                        <i class="fas fa-check"></i> Verified
                                                    </span>
                                                @else
                                                    <span class="badge badge-warning badge-sm">
                                                        <i class="fas fa-clock"></i> Pending
                                                    </span>
                                                @endif
                                            </div>

                                            <div class="btn-group btn-group-sm w-100" role="group">
                                                @if(!$method->is_default)
                                                    <form method="POST" action="{{ route('admin.payments.methods.default', $method) }}" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-outline-primary btn-sm">
                                                            Set Default
                                                        </button>
                                                    </form>
                                                @endif
                                                
                                                <form method="POST" action="{{ route('admin.payments.methods.delete', $method) }}" 
                                                      class="d-inline" onsubmit="return confirm('Are you sure you want to remove this payment method?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-outline-danger btn-sm">
                                                        <i class="fas fa-trash"></i> Remove
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Payment Methods</h5>
                            <p class="text-muted">Add a payment method to start making payments.</p>
                            <a href="{{ route('admin.payments.methods.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Your First Payment Method
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            @if($availableGateways->count() > 0)
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Available Payment Options</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($availableGateways as $gateway)
                                <div class="col-md-6 col-lg-3 mb-3">
                                    <div class="card border-0 bg-light">
                                        <div class="card-body text-center py-3">
                                            <i class="{{ $gateway->icon }} fa-2x text-primary mb-2"></i>
                                            <h6 class="card-title">{{ $gateway->name }}</h6>
                                            <p class="card-text text-muted small">{{ $gateway->description }}</p>
                                            @if($gateway->fee_percentage > 0 || $gateway->fee_fixed > 0)
                                                <small class="text-muted">
                                                    Fee: {{ number_format($gateway->fee_percentage * 100, 2) }}% + ${{ number_format($gateway->fee_fixed, 2) }}
                                                </small>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.alert-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.badge-sm {
    font-size: 0.7em;
}

.card.border-primary {
    border-width: 2px;
}

.btn-group-sm .btn {
    font-size: 0.75rem;
}
</style>
@endsection
