<x-admin-layout>
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- <PERSON>er -->
        <div class="sm:flex sm:items-center sm:justify-between mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">InsightHub</h1>
                <p class="mt-2 text-sm text-gray-700">Advanced analytics, performance metrics, and business intelligence</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <button onclick="refreshData()" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                </button>
                <button onclick="showExportModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export Data
                </button>
            </div>
        </div>

        <!-- Key Metrics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Visitors</p>
                            <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_visitors']) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Page Views</p>
                            <p class="text-2xl font-bold text-green-600">{{ number_format($stats['page_views']) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Conversion Rate</p>
                            <p class="text-2xl font-bold text-purple-600">{{ $stats['conversion_rate'] }}%</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Avg Session</p>
                            <p class="text-2xl font-bold text-yellow-600">{{ $stats['avg_session_duration'] }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Overview & Performance -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <div class="lg:col-span-2">
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Traffic Analytics</h3>
                        <p class="text-sm text-gray-500">Daily visitor trends and growth metrics</p>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-green-600">{{ $analytics['monthly_growth'] }}%</div>
                                <div class="text-sm text-gray-500">Monthly Growth</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-blue-600">{{ $analytics['weekly_growth'] }}%</div>
                                <div class="text-sm text-gray-500">Weekly Growth</div>
                            </div>
                        </div>
                        <div class="space-y-3">
                            @foreach($analytics['daily_visitors'] as $day => $visitors)
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-700">{{ $day }}</span>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ ($visitors / 8500) * 100 }}%"></div>
                                    </div>
                                    <span class="text-sm font-semibold text-gray-900">{{ number_format($visitors) }}</span>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div>
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Performance</h3>
                        <p class="text-sm text-gray-500">System performance metrics</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-700">Uptime</span>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-green-600">{{ $performance['uptime_percentage'] }}%</div>
                                    <div class="text-xs text-gray-500">Excellent</div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-700">Response Time</span>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-blue-600">{{ $performance['server_response_time'] }}ms</div>
                                    <div class="text-xs text-gray-500">Good</div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-700">Page Load</span>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-yellow-600">{{ $performance['page_load_time'] }}s</div>
                                    <div class="text-xs text-gray-500">Average</div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-700">CDN Hit Rate</span>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-green-600">{{ $performance['cdn_hit_rate'] }}%</div>
                                    <div class="text-xs text-gray-500">Excellent</div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-700">SSL Score</span>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-green-600">{{ $performance['ssl_score'] }}</div>
                                    <div class="text-xs text-gray-500">Perfect</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Traffic Sources -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Traffic Sources</h3>
                <p class="text-sm text-gray-500">Where your visitors are coming from</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                    @foreach($trafficSources as $source => $data)
                    <div class="text-center">
                        <div class="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                            <span class="text-white font-bold text-lg">{{ $data['percentage'] }}%</span>
                        </div>
                        <h4 class="text-sm font-semibold text-gray-900 mb-1">{{ ucfirst(str_replace('_', ' ', $source)) }}</h4>
                        <p class="text-xs text-gray-500">{{ number_format($data['visitors']) }} visitors</p>
                        <p class="text-xs font-medium {{ $data['growth'] > 0 ? 'text-green-600' : 'text-red-600' }}">
                            {{ $data['growth'] > 0 ? '+' : '' }}{{ $data['growth'] }}%
                        </p>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- User Behavior & Recent Insights -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- User Behavior -->
            <div class="lg:col-span-2">
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">User Behavior</h3>
                        <p class="text-sm text-gray-500">Device types, browsers, and operating systems</p>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- Device Types -->
                            <div>
                                <h4 class="text-sm font-semibold text-gray-900 mb-3">Device Types</h4>
                                <div class="space-y-2">
                                    @foreach($userBehavior['device_types'] as $device)
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-600">{{ $device['device'] }}</span>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-16 bg-gray-200 rounded-full h-1">
                                                <div class="bg-blue-600 h-1 rounded-full" style="width: {{ $device['percentage'] }}%"></div>
                                            </div>
                                            <span class="text-xs font-medium">{{ $device['percentage'] }}%</span>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>

                            <!-- Browsers -->
                            <div>
                                <h4 class="text-sm font-semibold text-gray-900 mb-3">Browsers</h4>
                                <div class="space-y-2">
                                    @foreach($userBehavior['browsers'] as $browser)
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-600">{{ $browser['browser'] }}</span>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-16 bg-gray-200 rounded-full h-1">
                                                <div class="bg-green-600 h-1 rounded-full" style="width: {{ $browser['percentage'] }}%"></div>
                                            </div>
                                            <span class="text-xs font-medium">{{ $browser['percentage'] }}%</span>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>

                            <!-- Operating Systems -->
                            <div>
                                <h4 class="text-sm font-semibold text-gray-900 mb-3">Operating Systems</h4>
                                <div class="space-y-2">
                                    @foreach($userBehavior['operating_systems'] as $os)
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-600">{{ $os['os'] }}</span>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-16 bg-gray-200 rounded-full h-1">
                                                <div class="bg-purple-600 h-1 rounded-full" style="width: {{ $os['percentage'] }}%"></div>
                                            </div>
                                            <span class="text-xs font-medium">{{ $os['percentage'] }}%</span>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Insights -->
            <div>
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Recent Insights</h3>
                        <p class="text-sm text-gray-500">Latest analytics insights</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            @foreach($recentInsights as $insight)
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 {{ $insight['impact'] === 'positive' ? 'bg-green-100' : ($insight['impact'] === 'warning' ? 'bg-yellow-100' : 'bg-red-100') }} rounded-full flex items-center justify-center">
                                        @if($insight['impact'] === 'positive')
                                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                            </svg>
                                        @elseif($insight['impact'] === 'warning')
                                            <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                                            </svg>
                                        @else
                                            <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                                            </svg>
                                        @endif
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900">{{ $insight['title'] }}</p>
                                    <p class="text-xs text-gray-500">{{ $insight['description'] }}</p>
                                    <div class="flex items-center justify-between mt-1">
                                        <p class="text-xs text-gray-400">{{ $insight['timestamp']->diffForHumans() }}</p>
                                        <span class="text-xs font-medium {{ $insight['impact'] === 'positive' ? 'text-green-600' : ($insight['impact'] === 'warning' ? 'text-yellow-600' : 'text-red-600') }}">
                                            {{ $insight['value'] }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div id="exportModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
            <div class="relative bg-white rounded-lg max-w-lg w-full">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Export Analytics Data</h3>
                </div>
                <form id="exportForm" class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Data Type</label>
                        <select name="data_type" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select Data Type</option>
                            <option value="traffic">Traffic Analytics</option>
                            <option value="performance">Performance Metrics</option>
                            <option value="user_behavior">User Behavior</option>
                            <option value="traffic_sources">Traffic Sources</option>
                            <option value="all">All Data</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Format</label>
                        <select name="format" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="csv">CSV</option>
                            <option value="xlsx">Excel</option>
                            <option value="pdf">PDF Report</option>
                            <option value="json">JSON</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Date Range</label>
                        <select name="date_range" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="7d">Last 7 days</option>
                            <option value="30d">Last 30 days</option>
                            <option value="90d">Last 90 days</option>
                            <option value="1y">Last year</option>
                            <option value="custom">Custom range</option>
                        </select>
                    </div>
                </form>
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button onclick="hideExportModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">Cancel</button>
                    <button onclick="exportData()" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">Export</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Toast -->
    <div id="notification" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
            <div class="flex items-center">
                <div id="notification-icon" class="flex-shrink-0 mr-3">
                    <!-- Icon will be inserted here -->
                </div>
                <div>
                    <p id="notification-title" class="text-sm font-medium text-gray-900"></p>
                    <p id="notification-message" class="text-sm text-gray-500"></p>
                </div>
                <button onclick="hideNotification()" class="ml-4 text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        function showNotification(type, title, message) {
            const notification = document.getElementById('notification');
            const icon = document.getElementById('notification-icon');
            const titleEl = document.getElementById('notification-title');
            const messageEl = document.getElementById('notification-message');

            titleEl.textContent = title;
            messageEl.textContent = message;

            if (type === 'success') {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                `;
            } else {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                `;
            }

            notification.classList.remove('hidden');
            setTimeout(() => {
                hideNotification();
            }, 5000);
        }

        function hideNotification() {
            document.getElementById('notification').classList.add('hidden');
        }

        function showExportModal() {
            document.getElementById('exportModal').classList.remove('hidden');
        }

        function hideExportModal() {
            document.getElementById('exportModal').classList.add('hidden');
            document.getElementById('exportForm').reset();
        }

        function exportData() {
            const form = document.getElementById('exportForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            if (!data.data_type || !data.format || !data.date_range) {
                showNotification('error', 'Export Failed', 'Please fill in all required fields');
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Exporting...';
            button.disabled = true;

            fetch('/admin/insights/export', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Export Successful', data.message);
                    hideExportModal();
                    if (data.download_url) {
                        setTimeout(() => {
                            showNotification('success', 'Download Ready', 'Your export is ready for download');
                        }, 1000);
                    }
                } else {
                    showNotification('error', 'Export Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Export Failed', 'Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
        }

        function refreshData() {
            const refreshButton = document.querySelector('button[onclick="refreshData()"]');
            const originalText = refreshButton.innerHTML;

            refreshButton.innerHTML = `
                <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refreshing...
            `;

            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // Auto-refresh every 60 seconds
        setInterval(() => {
            fetch('/admin/insights/real-time-data')
                .then(response => response.json())
                .then(data => {
                    console.log('Data refreshed:', data.timestamp);
                    // Update specific elements here instead of full page reload
                })
                .catch(error => console.error('Auto-refresh failed:', error));
        }, 60000);

        // Add smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Close modal when clicking outside
            document.getElementById('exportModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    hideExportModal();
                }
            });

            // Animate progress bars
            setTimeout(() => {
                const progressBars = document.querySelectorAll('.bg-blue-600, .bg-green-600, .bg-purple-600');
                progressBars.forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    bar.style.transition = 'width 1s ease-in-out';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 100);
                });
            }, 500);

            // Animate traffic source circles
            const circles = document.querySelectorAll('.bg-gradient-to-br');
            circles.forEach((circle, index) => {
                circle.style.transform = 'scale(0)';
                circle.style.transition = 'transform 0.5s ease';
                setTimeout(() => {
                    circle.style.transform = 'scale(1)';
                }, 200 + (index * 100));
            });
        });

        // Real-time metric updates simulation
        function updateMetrics() {
            const metrics = document.querySelectorAll('.dashboard-card .text-2xl');
            metrics.forEach(metric => {
                if (Math.random() > 0.8) { // 20% chance to update
                    const currentValue = metric.textContent;
                    metric.style.color = '#10B981'; // Green color
                    setTimeout(() => {
                        metric.style.color = '';
                    }, 1000);
                }
            });
        }

        // Update metrics every 30 seconds
        setInterval(updateMetrics, 30000);
    </script>
</x-admin-layout>
