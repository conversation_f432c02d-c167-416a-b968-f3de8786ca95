<x-admin-layout>
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- <PERSON> Header -->
        <div class="sm:flex sm:items-center sm:justify-between mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Add DNS Record</h1>
                <p class="mt-2 text-sm text-gray-700">
                    Create a new DNS record for <span class="font-semibold text-blue-600">{{ $domain->name }}</span>
                </p>
            </div>
            <div class="mt-4 sm:mt-0">
                <a href="{{ route('admin.domains.dns.index', $domain) }}"
                   class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to DNS Records
                </a>
            </div>
        </div>

        <!-- DNS Record Form -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
            <form method="POST" action="{{ route('admin.domains.dns.store', $domain) }}" class="space-y-6">
                @csrf
                
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">DNS Record Details</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Record Type -->
                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Record Type</label>
                            <select name="type" id="type" required 
                                    class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('type') border-red-300 @enderror">
                                <option value="">Select record type...</option>
                                @foreach(\App\Models\DnsRecord::RECORD_TYPES as $type => $name)
                                    <option value="{{ $type }}" {{ old('type') === $type ? 'selected' : '' }}>{{ $name }}</option>
                                @endforeach
                            </select>
                            @error('type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Record Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                            <div class="flex">
                                <input type="text" name="name" id="name" value="{{ old('name') }}" required
                                       placeholder="www, mail, @, etc."
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('name') border-red-300 @enderror">
                                <span class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-lg bg-gray-50 text-gray-500 text-sm">
                                    .{{ $domain->name }}
                                </span>
                            </div>
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-xs text-gray-500">Use @ for the root domain</p>
                        </div>

                        <!-- Record Value -->
                        <div class="md:col-span-2">
                            <label for="value" class="block text-sm font-medium text-gray-700 mb-2">Value</label>
                            <input type="text" name="value" id="value" value="{{ old('value') }}" required
                                   placeholder="Enter the record value..."
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('value') border-red-300 @enderror">
                            @error('value')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <div id="value-help" class="mt-1 text-xs text-gray-500"></div>
                        </div>

                        <!-- TTL -->
                        <div>
                            <label for="ttl" class="block text-sm font-medium text-gray-700 mb-2">TTL (Time to Live)</label>
                            <select name="ttl" id="ttl" required 
                                    class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('ttl') border-red-300 @enderror">
                                @foreach(\App\Models\DnsRecord::DEFAULT_TTLS as $ttl => $name)
                                    <option value="{{ $ttl }}" {{ old('ttl', 3600) == $ttl ? 'selected' : '' }}>{{ $name }}</option>
                                @endforeach
                            </select>
                            @error('ttl')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Priority (for MX and SRV records) -->
                        <div id="priority-field" class="hidden">
                            <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                            <input type="number" name="priority" id="priority" value="{{ old('priority') }}" min="0" max="65535"
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('priority') border-red-300 @enderror">
                            @error('priority')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Weight (for SRV records) -->
                        <div id="weight-field" class="hidden">
                            <label for="weight" class="block text-sm font-medium text-gray-700 mb-2">Weight</label>
                            <input type="number" name="weight" id="weight" value="{{ old('weight') }}" min="0" max="65535"
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('weight') border-red-300 @enderror">
                            @error('weight')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Port (for SRV records) -->
                        <div id="port-field" class="hidden">
                            <label for="port" class="block text-sm font-medium text-gray-700 mb-2">Port</label>
                            <input type="number" name="port" id="port" value="{{ old('port') }}" min="1" max="65535"
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('port') border-red-300 @enderror">
                            @error('port')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Comment -->
                        <div class="md:col-span-2">
                            <label for="comment" class="block text-sm font-medium text-gray-700 mb-2">Comment (Optional)</label>
                            <textarea name="comment" id="comment" rows="3" 
                                      placeholder="Add a description or note for this DNS record..."
                                      class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('comment') border-red-300 @enderror">{{ old('comment') }}</textarea>
                            @error('comment')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Quick Templates -->
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Templates</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <button type="button" onclick="fillTemplate('www', 'A', '*************')" 
                                class="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200">
                            <div class="text-sm font-medium text-gray-900">WWW Record</div>
                            <div class="text-xs text-gray-500">Point www to an IP address</div>
                        </button>
                        <button type="button" onclick="fillTemplate('mail', 'A', '*************')" 
                                class="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200">
                            <div class="text-sm font-medium text-gray-900">Mail Server</div>
                            <div class="text-xs text-gray-500">Point mail to an IP address</div>
                        </button>
                        <button type="button" onclick="fillTemplate('@', 'MX', 'mail.' + '{{ $domain->name }}', 10)" 
                                class="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200">
                            <div class="text-sm font-medium text-gray-900">MX Record</div>
                            <div class="text-xs text-gray-500">Email server record</div>
                        </button>
                        <button type="button" onclick="fillTemplate('_dmarc', 'TXT', 'v=DMARC1; p=quarantine;')" 
                                class="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200">
                            <div class="text-sm font-medium text-gray-900">DMARC Record</div>
                            <div class="text-xs text-gray-500">Email authentication</div>
                        </button>
                        <button type="button" onclick="fillTemplate('@', 'TXT', 'v=spf1 include:_spf.' + '{{ $domain->name }}' + ' ~all')" 
                                class="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200">
                            <div class="text-sm font-medium text-gray-900">SPF Record</div>
                            <div class="text-xs text-gray-500">Email sender policy</div>
                        </button>
                        <button type="button" onclick="fillTemplate('www', 'CNAME', '{{ $domain->name }}')" 
                                class="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200">
                            <div class="text-sm font-medium text-gray-900">CNAME Alias</div>
                            <div class="text-xs text-gray-500">Alias www to root domain</div>
                        </button>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="px-6 py-4 bg-gray-50 flex items-center justify-end space-x-3">
                    <a href="{{ route('admin.domains.dns.index', $domain) }}" 
                       class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                        Create DNS Record
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Record type change handler
        document.getElementById('type').addEventListener('change', function() {
            const type = this.value;
            const priorityField = document.getElementById('priority-field');
            const weightField = document.getElementById('weight-field');
            const portField = document.getElementById('port-field');
            const valueHelp = document.getElementById('value-help');

            // Hide all optional fields first
            priorityField.classList.add('hidden');
            weightField.classList.add('hidden');
            portField.classList.add('hidden');

            // Show relevant fields and update help text
            switch(type) {
                case 'A':
                    valueHelp.textContent = 'Enter an IPv4 address (e.g., *************)';
                    break;
                case 'AAAA':
                    valueHelp.textContent = 'Enter an IPv6 address (e.g., 2001:db8::1)';
                    break;
                case 'CNAME':
                    valueHelp.textContent = 'Enter a domain name (e.g., example.com)';
                    break;
                case 'MX':
                    priorityField.classList.remove('hidden');
                    valueHelp.textContent = 'Enter mail server hostname (e.g., mail.example.com)';
                    break;
                case 'TXT':
                    valueHelp.textContent = 'Enter text content (e.g., v=spf1 include:_spf.example.com ~all)';
                    break;
                case 'NS':
                    valueHelp.textContent = 'Enter nameserver hostname (e.g., ns1.example.com)';
                    break;
                case 'SRV':
                    priorityField.classList.remove('hidden');
                    weightField.classList.remove('hidden');
                    portField.classList.remove('hidden');
                    valueHelp.textContent = 'Enter target hostname (e.g., target.example.com)';
                    break;
                default:
                    valueHelp.textContent = '';
            }
        });

        // Template filler function
        function fillTemplate(name, type, value, priority = null) {
            document.getElementById('name').value = name;
            document.getElementById('type').value = type;
            document.getElementById('value').value = value;
            
            if (priority !== null) {
                document.getElementById('priority').value = priority;
            }

            // Trigger change event to show/hide relevant fields
            document.getElementById('type').dispatchEvent(new Event('change'));
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('type').dispatchEvent(new Event('change'));
        });
    </script>
</x-admin-layout>
