<x-admin-layout>
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- <PERSON> Header -->
        <div class="sm:flex sm:items-center sm:justify-between mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit DNS Record</h1>
                <p class="mt-2 text-sm text-gray-700">
                    Modify DNS record for <span class="font-semibold text-blue-600">{{ $domain->name }}</span>
                </p>
            </div>
            <div class="mt-4 sm:mt-0">
                <a href="{{ route('admin.domains.dns.index', $domain) }}"
                   class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to DNS Records
                </a>
            </div>
        </div>

        <!-- Current Record Info -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Current Record</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p><strong>Name:</strong> {{ $record->full_name }}</p>
                        <p><strong>Type:</strong> {{ $record->type_name }}</p>
                        <p><strong>Value:</strong> {{ $record->value }}</p>
                        <p><strong>TTL:</strong> {{ $record->ttl_name }}</p>
                        @if($record->priority)
                            <p><strong>Priority:</strong> {{ $record->priority }}</p>
                        @endif
                        @if($record->weight)
                            <p><strong>Weight:</strong> {{ $record->weight }}</p>
                        @endif
                        @if($record->port)
                            <p><strong>Port:</strong> {{ $record->port }}</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- DNS Record Form -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
            <form method="POST" action="{{ route('admin.domains.dns.update', [$domain, $record]) }}" class="space-y-6">
                @csrf
                @method('PUT')
                
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">DNS Record Details</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Record Type -->
                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Record Type</label>
                            <select name="type" id="type" required 
                                    class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('type') border-red-300 @enderror">
                                @foreach(\App\Models\DnsRecord::RECORD_TYPES as $type => $name)
                                    <option value="{{ $type }}" {{ old('type', $record->type) === $type ? 'selected' : '' }}>{{ $name }}</option>
                                @endforeach
                            </select>
                            @error('type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Record Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                            <div class="flex">
                                <input type="text" name="name" id="name" value="{{ old('name', $record->name) }}" required
                                       placeholder="www, mail, @, etc."
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('name') border-red-300 @enderror">
                                <span class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-lg bg-gray-50 text-gray-500 text-sm">
                                    .{{ $domain->name }}
                                </span>
                            </div>
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-xs text-gray-500">Use @ for the root domain</p>
                        </div>

                        <!-- Record Value -->
                        <div class="md:col-span-2">
                            <label for="value" class="block text-sm font-medium text-gray-700 mb-2">Value</label>
                            <input type="text" name="value" id="value" value="{{ old('value', $record->value) }}" required
                                   placeholder="Enter the record value..."
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('value') border-red-300 @enderror">
                            @error('value')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <div id="value-help" class="mt-1 text-xs text-gray-500"></div>
                        </div>

                        <!-- TTL -->
                        <div>
                            <label for="ttl" class="block text-sm font-medium text-gray-700 mb-2">TTL (Time to Live)</label>
                            <select name="ttl" id="ttl" required 
                                    class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('ttl') border-red-300 @enderror">
                                @foreach(\App\Models\DnsRecord::DEFAULT_TTLS as $ttl => $name)
                                    <option value="{{ $ttl }}" {{ old('ttl', $record->ttl) == $ttl ? 'selected' : '' }}>{{ $name }}</option>
                                @endforeach
                            </select>
                            @error('ttl')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Priority (for MX and SRV records) -->
                        <div id="priority-field" class="{{ in_array($record->type, ['MX', 'SRV']) ? '' : 'hidden' }}">
                            <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                            <input type="number" name="priority" id="priority" value="{{ old('priority', $record->priority) }}" min="0" max="65535"
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('priority') border-red-300 @enderror">
                            @error('priority')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Weight (for SRV records) -->
                        <div id="weight-field" class="{{ $record->type === 'SRV' ? '' : 'hidden' }}">
                            <label for="weight" class="block text-sm font-medium text-gray-700 mb-2">Weight</label>
                            <input type="number" name="weight" id="weight" value="{{ old('weight', $record->weight) }}" min="0" max="65535"
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('weight') border-red-300 @enderror">
                            @error('weight')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Port (for SRV records) -->
                        <div id="port-field" class="{{ $record->type === 'SRV' ? '' : 'hidden' }}">
                            <label for="port" class="block text-sm font-medium text-gray-700 mb-2">Port</label>
                            <input type="number" name="port" id="port" value="{{ old('port', $record->port) }}" min="1" max="65535"
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('port') border-red-300 @enderror">
                            @error('port')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Comment -->
                        <div class="md:col-span-2">
                            <label for="comment" class="block text-sm font-medium text-gray-700 mb-2">Comment (Optional)</label>
                            <textarea name="comment" id="comment" rows="3" 
                                      placeholder="Add a description or note for this DNS record..."
                                      class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('comment') border-red-300 @enderror">{{ old('comment', $record->comment) }}</textarea>
                            @error('comment')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Record Status -->
                @if($record->is_system)
                    <div class="p-6 border-b border-gray-200">
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800">System Record</h3>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <p>This is a system-generated DNS record. Please be careful when modifying it as it may affect your domain's functionality.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Propagation Status -->
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Propagation Status</h3>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full {{ $record->propagated ? 'bg-green-400' : 'bg-yellow-400' }} mr-3"></div>
                            <span class="text-sm text-gray-900">
                                {{ $record->propagated ? 'Propagated' : 'Propagation Pending' }}
                            </span>
                            @if($record->last_checked_at)
                                <span class="text-xs text-gray-500 ml-2">
                                    Last checked: {{ $record->last_checked_at->diffForHumans() }}
                                </span>
                            @endif
                        </div>
                        <form method="POST" action="{{ route('admin.domains.dns.check-propagation', [$domain, $record]) }}" class="inline">
                            @csrf
                            <button type="submit" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                Check Now
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="px-6 py-4 bg-gray-50 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        @if(!$record->is_system)
                            <form method="POST" action="{{ route('admin.domains.dns.destroy', [$domain, $record]) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this DNS record? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200">
                                    Delete Record
                                </button>
                            </form>
                        @endif
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{{ route('admin.domains.dns.index', $domain) }}" 
                           class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                            Update DNS Record
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Record type change handler
        document.getElementById('type').addEventListener('change', function() {
            const type = this.value;
            const priorityField = document.getElementById('priority-field');
            const weightField = document.getElementById('weight-field');
            const portField = document.getElementById('port-field');
            const valueHelp = document.getElementById('value-help');

            // Hide all optional fields first
            priorityField.classList.add('hidden');
            weightField.classList.add('hidden');
            portField.classList.add('hidden');

            // Show relevant fields and update help text
            switch(type) {
                case 'A':
                    valueHelp.textContent = 'Enter an IPv4 address (e.g., *************)';
                    break;
                case 'AAAA':
                    valueHelp.textContent = 'Enter an IPv6 address (e.g., 2001:db8::1)';
                    break;
                case 'CNAME':
                    valueHelp.textContent = 'Enter a domain name (e.g., example.com)';
                    break;
                case 'MX':
                    priorityField.classList.remove('hidden');
                    valueHelp.textContent = 'Enter mail server hostname (e.g., mail.example.com)';
                    break;
                case 'TXT':
                    valueHelp.textContent = 'Enter text content (e.g., v=spf1 include:_spf.example.com ~all)';
                    break;
                case 'NS':
                    valueHelp.textContent = 'Enter nameserver hostname (e.g., ns1.example.com)';
                    break;
                case 'SRV':
                    priorityField.classList.remove('hidden');
                    weightField.classList.remove('hidden');
                    portField.classList.remove('hidden');
                    valueHelp.textContent = 'Enter target hostname (e.g., target.example.com)';
                    break;
                default:
                    valueHelp.textContent = '';
            }
        });

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('type').dispatchEvent(new Event('change'));
        });
    </script>
</x-admin-layout>
