<x-layouts.admin title="Create New Backup" header="Create New Backup">
    <!-- Dashboard Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                        Create New Backup
                    </span>
                </h1>
                <p class="text-gray-600 mt-1">Create a backup for databases, files, or full system</p>
            </div>
            <div class="flex items-center space-x-4">
                <a href="{{ route('admin.backups.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    <span>Back to Backups</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Create Backup Form -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100">
        <div class="p-6 border-b border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900">Backup Configuration</h3>
            <p class="text-sm text-gray-600 mt-1">Configure your backup settings and select what to backup</p>
        </div>

        <form method="POST" action="{{ route('admin.backups.store') }}" class="p-6 space-y-6">
            @csrf

            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Backup Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Backup Name *</label>
                    <input type="text" name="name" id="name" value="{{ old('name') }}" required
                           placeholder="My Database Backup"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror">
                    <p class="text-xs text-gray-500 mt-1">A descriptive name for this backup</p>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Backup Type -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Backup Type *</label>
                    <select name="type" id="type" required onchange="toggleBackupOptions()"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('type') border-red-500 @enderror">
                        <option value="">Select Backup Type</option>
                        <option value="database" {{ old('type') === 'database' ? 'selected' : '' }}>Database Backup</option>
                        <option value="files" {{ old('type') === 'files' ? 'selected' : '' }}>Files Backup</option>
                        <option value="full" {{ old('type') === 'full' ? 'selected' : '' }}>Full System Backup</option>
                    </select>
                    @error('type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Database Selection (shown when type is database) -->
            <div id="database-selection" class="hidden">
                <label for="database_id" class="block text-sm font-medium text-gray-700 mb-2">Select Database *</label>
                <select name="backupable_id" id="database_id"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('backupable_id') border-red-500 @enderror">
                    <option value="">Choose Database</option>
                    @foreach($databases as $database)
                        <option value="{{ $database->id }}" {{ old('backupable_id') == $database->id ? 'selected' : '' }}>
                            {{ $database->name }} ({{ $database->type }}) - {{ $database->user->name }}
                        </option>
                    @endforeach
                </select>
                <input type="hidden" name="backupable_type_database" value="App\Models\Database">
                @error('backupable_id')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Domain Selection (shown when type is files) -->
            <div id="domain-selection" class="hidden">
                <label for="domain_id" class="block text-sm font-medium text-gray-700 mb-2">Select Domain *</label>
                <select name="backupable_id" id="domain_id"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('backupable_id') border-red-500 @enderror">
                    <option value="">Choose Domain</option>
                    @foreach($domains as $domain)
                        <option value="{{ $domain->id }}" {{ old('backupable_id') == $domain->id ? 'selected' : '' }}>
                            {{ $domain->name }} - {{ $domain->user->name }}
                        </option>
                    @endforeach
                </select>
                <input type="hidden" name="backupable_type_domain" value="App\Models\Domain">
                @error('backupable_id')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Owner Selection -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- User -->
                <div>
                    <label for="user_id" class="block text-sm font-medium text-gray-700 mb-2">Backup Owner *</label>
                    <select name="user_id" id="user_id" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('user_id') border-red-500 @enderror">
                        <option value="">Select Owner</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}" {{ old('user_id', auth()->id()) == $user->id ? 'selected' : '' }}>
                                {{ $user->name }} ({{ $user->email }})
                            </option>
                        @endforeach
                    </select>
                    @error('user_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Compression -->
                <div>
                    <label for="compression" class="block text-sm font-medium text-gray-700 mb-2">Compression *</label>
                    <select name="compression" id="compression" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('compression') border-red-500 @enderror">
                        <option value="gzip" {{ old('compression', 'gzip') === 'gzip' ? 'selected' : '' }}>GZip (Recommended)</option>
                        <option value="zip" {{ old('compression') === 'zip' ? 'selected' : '' }}>ZIP</option>
                        <option value="none" {{ old('compression') === 'none' ? 'selected' : '' }}>No Compression</option>
                    </select>
                    @error('compression')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Advanced Options -->
            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">Advanced Options</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Encryption -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" name="encrypted" id="encrypted" value="1" {{ old('encrypted') ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <label for="encrypted" class="ml-2 text-sm font-medium text-gray-700">
                                Enable Encryption
                            </label>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Encrypt backup file with AES-256</p>
                        @error('encrypted')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Expiration Date -->
                    <div>
                        <label for="expires_at" class="block text-sm font-medium text-gray-700 mb-2">Expiration Date</label>
                        <input type="date" name="expires_at" id="expires_at" value="{{ old('expires_at') }}"
                               min="{{ now()->addDay()->format('Y-m-d') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('expires_at') border-red-500 @enderror">
                        <p class="text-xs text-gray-500 mt-1">Leave empty for automatic retention based on backup type</p>
                        @error('expires_at')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Backup Information -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">Backup Information</h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <ul class="list-disc list-inside space-y-1">
                                <li><strong>Database backups:</strong> Include all tables, data, and structure</li>
                                <li><strong>File backups:</strong> Include all files and directories for the selected domain</li>
                                <li><strong>Full backups:</strong> Include all databases, files, and system configurations</li>
                                <li><strong>Retention:</strong> Database (30 days), Files (14 days), Full (7 days)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-100">
                <a href="{{ route('admin.backups.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                    Cancel
                </a>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                    </svg>
                    <span>Create Backup</span>
                </button>
            </div>
        </form>
    </div>

    <script>
        function toggleBackupOptions() {
            const type = document.getElementById('type').value;
            const databaseSelection = document.getElementById('database-selection');
            const domainSelection = document.getElementById('domain-selection');
            
            // Hide all selections first
            databaseSelection.classList.add('hidden');
            domainSelection.classList.add('hidden');
            
            // Clear backupable_type
            const backupableTypeInputs = document.querySelectorAll('input[name^="backupable_type"]');
            backupableTypeInputs.forEach(input => {
                input.removeAttribute('name');
            });
            
            // Show relevant selection and set backupable_type
            if (type === 'database') {
                databaseSelection.classList.remove('hidden');
                document.querySelector('input[name="backupable_type_database"]').setAttribute('name', 'backupable_type');
            } else if (type === 'files') {
                domainSelection.classList.remove('hidden');
                document.querySelector('input[name="backupable_type_domain"]').setAttribute('name', 'backupable_type');
            }
            // For 'full' type, no selection needed
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleBackupOptions();
        });

        // Set default expiration dates based on backup type
        document.getElementById('type').addEventListener('change', function() {
            const type = this.value;
            const expiresAt = document.getElementById('expires_at');
            
            if (!expiresAt.value) { // Only set if user hasn't manually set a date
                const today = new Date();
                let days = 30; // default
                
                switch(type) {
                    case 'database':
                        days = 30;
                        break;
                    case 'files':
                        days = 14;
                        break;
                    case 'full':
                        days = 7;
                        break;
                }
                
                const expirationDate = new Date(today.getTime() + (days * 24 * 60 * 60 * 1000));
                expiresAt.value = expirationDate.toISOString().split('T')[0];
            }
        });
    </script>
</x-layouts.admin>
