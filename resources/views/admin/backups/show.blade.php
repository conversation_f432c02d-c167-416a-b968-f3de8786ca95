<x-layouts.admin title="Backup Details - {{ $backup->name }}" header="Backup Details">
    <!-- Dashboard Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                        Backup Details
                    </span>
                </h1>
                <p class="text-gray-600 mt-1">View and manage backup: {{ $backup->name }}</p>
            </div>
            <div class="flex items-center space-x-4">
                @if($backup->isCompleted())
                    <a href="{{ route('admin.backups.download', $backup) }}" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>Download</span>
                    </a>
                    <button onclick="restoreBackup({{ $backup->id }})" class="bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span>Restore</span>
                    </button>
                @endif
                <a href="{{ route('admin.backups.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    <span>Back to Backups</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Backup Status Card -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 mb-8">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-16 h-16 {{ $backup->type_color }} bg-opacity-20 rounded-xl flex items-center justify-center mr-4">
                        <svg class="w-8 h-8 {{ $backup->type_color }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            @if($backup->type === 'database')
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                            @elseif($backup->type === 'files')
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path>
                            @else
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                            @endif
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">{{ $backup->name }}</h2>
                        <div class="flex items-center space-x-4 mt-2">
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full {{ $backup->status_color }}">
                                {{ ucfirst($backup->status) }}
                            </span>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full {{ $backup->type_color }} bg-opacity-20">
                                {{ ucfirst($backup->type) }} Backup
                            </span>
                            @if($backup->is_automatic)
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-800">
                                    Automatic
                                </span>
                            @endif
                            @if($backup->encrypted)
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-purple-100 text-purple-800">
                                    Encrypted
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
                
                @if($backup->isRunning())
                    <div class="text-right">
                        <div class="text-sm text-gray-600 mb-2">Progress</div>
                        <div class="w-32 bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: {{ $backup->getProgressPercentage() }}%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">{{ $backup->getProgressPercentage() }}%</div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Backup Information Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Basic Information -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Basic Information</h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Backup ID:</span>
                    <span class="text-sm text-gray-900">#{{ $backup->id }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Owner:</span>
                    <span class="text-sm text-gray-900">{{ $backup->user->name }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Type:</span>
                    <span class="text-sm text-gray-900">{{ ucfirst($backup->type) }}</span>
                </div>
                @if($backup->backupable)
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-600">Source:</span>
                        <span class="text-sm text-gray-900">
                            @if($backup->type === 'database')
                                {{ $backup->backupable->name }} ({{ $backup->backupable->type }})
                            @elseif($backup->type === 'files')
                                {{ $backup->backupable->name }}
                            @endif
                        </span>
                    </div>
                @endif
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Compression:</span>
                    <span class="text-sm text-gray-900">{{ strtoupper($backup->compression) }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Storage Driver:</span>
                    <span class="text-sm text-gray-900">{{ ucfirst($backup->storage_driver) }}</span>
                </div>
            </div>
        </div>

        <!-- Timing Information -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Timing Information</h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Created:</span>
                    <span class="text-sm text-gray-900">{{ $backup->created_at->format('M d, Y H:i:s') }}</span>
                </div>
                @if($backup->started_at)
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-600">Started:</span>
                        <span class="text-sm text-gray-900">{{ $backup->started_at->format('M d, Y H:i:s') }}</span>
                    </div>
                @endif
                @if($backup->completed_at)
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-600">Completed:</span>
                        <span class="text-sm text-gray-900">{{ $backup->completed_at->format('M d, Y H:i:s') }}</span>
                    </div>
                @endif
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Duration:</span>
                    <span class="text-sm text-gray-900">{{ $backup->duration_human }}</span>
                </div>
                @if($backup->expires_at)
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-600">Expires:</span>
                        <span class="text-sm text-gray-900 {{ $backup->isExpired() ? 'text-red-600' : '' }}">
                            {{ $backup->expires_at->format('M d, Y H:i:s') }}
                            @if($backup->isExpired())
                                <span class="text-xs">(Expired)</span>
                            @endif
                        </span>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- File Information -->
    @if($backup->isCompleted())
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 mb-8">
        <div class="p-6 border-b border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900">File Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ $backup->size_human }}</div>
                    <div class="text-sm text-gray-600">File Size</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">
                        @if($backup->fileExists())
                            <svg class="w-8 h-8 mx-auto text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        @else
                            <svg class="w-8 h-8 mx-auto text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        @endif
                    </div>
                    <div class="text-sm text-gray-600">File Status</div>
                    <div class="text-xs {{ $backup->fileExists() ? 'text-green-600' : 'text-red-600' }}">
                        {{ $backup->fileExists() ? 'Available' : 'Missing' }}
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">
                        {{ $backup->encrypted ? 'AES-256' : 'None' }}
                    </div>
                    <div class="text-sm text-gray-600">Encryption</div>
                </div>
            </div>
            
            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                <div class="text-sm font-medium text-gray-700 mb-2">Storage Path:</div>
                <div class="text-sm text-gray-600 font-mono break-all">{{ $backup->storage_path }}</div>
            </div>
        </div>
    </div>
    @endif

    <!-- Error Information -->
    @if($backup->isFailed() && $backup->error_message)
    <div class="bg-white rounded-xl shadow-lg border border-red-200 mb-8">
        <div class="p-6 border-b border-red-200">
            <h3 class="text-lg font-semibold text-red-900">Error Information</h3>
        </div>
        <div class="p-6">
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Backup Failed</h3>
                        <div class="mt-2 text-sm text-red-700">
                            {{ $backup->error_message }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Metadata -->
    @if($backup->metadata)
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 mb-8">
        <div class="p-6 border-b border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900">Additional Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                @foreach($backup->metadata as $key => $value)
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-600">{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                        <span class="text-sm text-gray-900">{{ is_array($value) ? json_encode($value) : $value }}</span>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Actions -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100">
        <div class="p-6 border-b border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900">Actions</h3>
        </div>
        <div class="p-6">
            <div class="flex flex-wrap gap-4">
                @if($backup->isCompleted())
                    <a href="{{ route('admin.backups.download', $backup) }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>Download Backup</span>
                    </a>
                    <button onclick="restoreBackup({{ $backup->id }})" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span>Restore Backup</span>
                    </button>
                @endif
                
                <form method="POST" action="{{ route('admin.backups.destroy', $backup) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this backup? This action cannot be undone.')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        <span>Delete Backup</span>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Restore backup function
        function restoreBackup(backupId) {
            if (confirm('Are you sure you want to restore this backup? This action cannot be undone and will overwrite existing data.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/backups/${backupId}/restore`;
                
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                
                const confirmInput = document.createElement('input');
                confirmInput.type = 'hidden';
                confirmInput.name = 'confirm';
                confirmInput.value = '1';
                
                form.appendChild(csrfToken);
                form.appendChild(confirmInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Auto-refresh for running backups
        @if($backup->isRunning())
        setTimeout(() => {
            location.reload();
        }, 10000); // Refresh every 10 seconds for running backups
        @endif
    </script>
</x-layouts.admin>
