<x-layouts.admin title="File Manager" header="File Manager">
    <!-- Dashboard Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                        File Manager
                    </span>
                </h1>
                <p class="text-gray-600 mt-1">Manage files and directories across domains</p>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Upload Button -->
                <button onclick="openUploadModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <span>Upload Files</span>
                </button>
                <!-- New Folder Button -->
                <button onclick="openNewFolderModal()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <span>New Folder</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <!-- Total Files -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Files</p>
                        <p class="text-3xl font-bold text-gray-900">{{ number_format($stats['total_files']) }}</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Directories -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Directories</p>
                        <p class="text-3xl font-bold text-green-600">{{ number_format($stats['total_directories']) }}</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Size -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Size</p>
                        <p class="text-3xl font-bold text-indigo-600">
                            @php
                                $totalSize = $stats['total_size'];
                                $units = ['B', 'KB', 'MB', 'GB', 'TB'];
                                for ($i = 0; $totalSize > 1024 && $i < count($units) - 1; $i++) {
                                    $totalSize /= 1024;
                                }
                                echo round($totalSize, 1) . ' ' . $units[$i];
                            @endphp
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Images -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Images</p>
                        <p class="text-3xl font-bold text-orange-600">{{ number_format($stats['images']) }}</p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 mb-8">
        <div class="p-6">
            <form method="GET" action="{{ route('admin.files.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Domain Filter -->
                <div>
                    <label for="domain_id" class="block text-sm font-medium text-gray-700 mb-2">Domain</label>
                    <select name="domain_id" id="domain_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Domains</option>
                        @foreach($domains as $domain)
                            <option value="{{ $domain->id }}" {{ request('domain_id') == $domain->id ? 'selected' : '' }}>
                                {{ $domain->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- User Filter -->
                <div>
                    <label for="user_id" class="block text-sm font-medium text-gray-700 mb-2">Owner</label>
                    <select name="user_id" id="user_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Users</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                {{ $user->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Path Input -->
                <div>
                    <label for="path" class="block text-sm font-medium text-gray-700 mb-2">Path</label>
                    <input type="text" name="path" id="path" value="{{ $currentPath }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Filter Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        Navigate
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 mb-8">
        <div class="p-4">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                    @foreach($breadcrumb as $index => $crumb)
                        <li class="flex items-center">
                            @if($index > 0)
                                <svg class="w-4 h-4 text-gray-400 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            @endif
                            @if($index === count($breadcrumb) - 1)
                                <span class="text-gray-500 font-medium">{{ $crumb['name'] }}</span>
                            @else
                                <a href="{{ route('admin.files.index', array_merge(request()->query(), ['path' => $crumb['path']])) }}"
                                   class="text-blue-600 hover:text-blue-800 font-medium">
                                    {{ $crumb['name'] }}
                                </a>
                            @endif
                        </li>
                    @endforeach
                </ol>
            </nav>
        </div>
    </div>

    <!-- File List -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100">
        <div class="p-6 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Files & Directories</h3>
                <div class="flex items-center space-x-2">
                    <button onclick="toggleView()" id="view-toggle" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                        </svg>
                    </button>
                    <span class="text-sm text-gray-500">{{ count($files) }} items</span>
                </div>
            </div>
        </div>

        @if(count($files) > 0)
        <!-- Table View -->
        <div id="table-view" class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="select-all" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Modified</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($files as $file)
                    <tr class="hover:bg-gray-50" data-file-id="{{ $file->id }}">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" name="files[]" value="{{ $file->id }}" class="file-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-8 h-8 flex items-center justify-center mr-3">
                                    @if($file->type === 'directory')
                                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path>
                                        </svg>
                                    @else
                                        <svg class="w-6 h-6 {{ $file->color_class }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    @endif
                                </div>
                                <div>
                                    @if($file->type === 'directory')
                                        <a href="{{ route('admin.files.index', array_merge(request()->query(), ['path' => rtrim($currentPath, '/') . '/' . $file->name])) }}"
                                           class="text-sm font-medium text-blue-600 hover:text-blue-800">
                                            {{ $file->name }}
                                        </a>
                                    @else
                                        <span class="text-sm font-medium text-gray-900">{{ $file->name }}</span>
                                    @endif
                                    @if($file->domain)
                                        <div class="text-xs text-gray-500">{{ $file->domain->name }}</div>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ $file->type === 'directory' ? '-' : $file->size_human }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                {{ $file->type === 'directory' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800' }}">
                                {{ ucfirst($file->type) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ $file->last_modified_at ? $file->last_modified_at->format('M d, Y H:i') : 'N/A' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-2">
                                @if($file->type === 'file')
                                    <button onclick="downloadFile({{ $file->id }})" class="text-green-600 hover:text-green-900">Download</button>
                                    @if($file->isEditable())
                                        <button onclick="editFile({{ $file->id }})" class="text-blue-600 hover:text-blue-900">Edit</button>
                                    @endif
                                @endif
                                <button onclick="renameFile({{ $file->id }}, '{{ $file->name }}')" class="text-indigo-600 hover:text-indigo-900">Rename</button>
                                <button onclick="deleteFile({{ $file->id }})" class="text-red-600 hover:text-red-900">Delete</button>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
        <div class="p-12 text-center">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No files found</h3>
            <p class="text-gray-500 mb-6">This directory is empty. Upload files or create folders to get started.</p>
            <div class="flex justify-center space-x-4">
                <button onclick="openUploadModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                    Upload Files
                </button>
                <button onclick="openNewFolderModal()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                    New Folder
                </button>
            </div>
        </div>
        @endif
    </div>

    <!-- Bulk Actions -->
    <div id="bulk-actions" class="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg border border-gray-200 px-6 py-4 hidden">
        <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600">
                <span id="selected-count">0</span> items selected
            </span>
            <button onclick="bulkDelete()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-1 rounded text-sm">
                Delete Selected
            </button>
            <button onclick="clearSelection()" class="text-gray-500 hover:text-gray-700">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- Upload Modal -->
    <div id="upload-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Upload Files</h3>
                    <form id="upload-form" enctype="multipart/form-data">
                        @csrf
                        <input type="hidden" name="path" value="{{ $currentPath }}">
                        <input type="hidden" name="domain_id" value="{{ $domainId }}">
                        <input type="hidden" name="user_id" value="{{ request('user_id', auth()->id()) }}">

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Select Files</label>
                            <input type="file" name="files[]" multiple class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeUploadModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">
                                Cancel
                            </button>
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                                Upload
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- New Folder Modal -->
    <div id="new-folder-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Create New Folder</h3>
                    <form id="new-folder-form">
                        @csrf
                        <input type="hidden" name="path" value="{{ $currentPath }}">
                        <input type="hidden" name="domain_id" value="{{ $domainId }}">
                        <input type="hidden" name="user_id" value="{{ request('user_id', auth()->id()) }}">

                        <div class="mb-4">
                            <label for="folder-name" class="block text-sm font-medium text-gray-700 mb-2">Folder Name</label>
                            <input type="text" id="folder-name" name="name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeNewFolderModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">
                                Cancel
                            </button>
                            <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                                Create
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- File Editor Modal -->
    <div id="file-editor-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-hidden">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Edit File: <span id="editor-filename"></span></h3>
                </div>
                <div class="p-6 max-h-96 overflow-y-auto">
                    <textarea id="file-content" rows="20" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"></textarea>
                </div>
                <div class="p-6 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="closeFileEditor()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">
                        Cancel
                    </button>
                    <button type="button" onclick="saveFileContent()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        Save
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentEditingFileId = null;

        // Bulk selection functionality
        const selectAllCheckbox = document.getElementById('select-all');
        const fileCheckboxes = document.querySelectorAll('.file-checkbox');
        const bulkActions = document.getElementById('bulk-actions');
        const selectedCount = document.getElementById('selected-count');

        selectAllCheckbox?.addEventListener('change', function() {
            fileCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });

        fileCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBulkActions);
        });

        function updateBulkActions() {
            const selected = Array.from(fileCheckboxes).filter(cb => cb.checked);
            const count = selected.length;

            if (count > 0) {
                bulkActions.classList.remove('hidden');
                selectedCount.textContent = count;
            } else {
                bulkActions.classList.add('hidden');
            }
        }

        function clearSelection() {
            fileCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            selectAllCheckbox.checked = false;
            updateBulkActions();
        }

        // Modal functions
        function openUploadModal() {
            document.getElementById('upload-modal').classList.remove('hidden');
        }

        function closeUploadModal() {
            document.getElementById('upload-modal').classList.add('hidden');
        }

        function openNewFolderModal() {
            document.getElementById('new-folder-modal').classList.remove('hidden');
            document.getElementById('folder-name').focus();
        }

        function closeNewFolderModal() {
            document.getElementById('new-folder-modal').classList.add('hidden');
            document.getElementById('new-folder-form').reset();
        }

        // File operations
        function downloadFile(fileId) {
            window.location.href = `/admin/files/${fileId}/download`;
        }

        function editFile(fileId) {
            fetch(`/admin/files/${fileId}/content`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentEditingFileId = fileId;
                        document.getElementById('editor-filename').textContent = data.file.name;
                        document.getElementById('file-content').value = data.content;
                        document.getElementById('file-editor-modal').classList.remove('hidden');
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error loading file content');
                });
        }

        function closeFileEditor() {
            document.getElementById('file-editor-modal').classList.add('hidden');
            currentEditingFileId = null;
        }

        function saveFileContent() {
            if (!currentEditingFileId) return;

            const content = document.getElementById('file-content').value;

            fetch(`/admin/files/${currentEditingFileId}/content`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ content: content })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('File saved successfully');
                    closeFileEditor();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error saving file');
            });
        }

        function renameFile(fileId, currentName) {
            const newName = prompt('Enter new name:', currentName);
            if (newName && newName !== currentName) {
                fetch(`/admin/files/${fileId}/rename`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ name: newName })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error renaming file');
                });
            }
        }

        function deleteFile(fileId) {
            if (confirm('Are you sure you want to delete this item?')) {
                fetch('/admin/files/delete', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ files: [fileId] })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error deleting file');
                });
            }
        }

        function bulkDelete() {
            const selected = Array.from(fileCheckboxes).filter(cb => cb.checked).map(cb => cb.value);

            if (selected.length === 0) return;

            if (confirm(`Are you sure you want to delete ${selected.length} item(s)?`)) {
                fetch('/admin/files/delete', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ files: selected })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error deleting files');
                });
            }
        }

        // Form submissions
        document.getElementById('upload-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('/admin/files/upload', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error uploading files');
            });
        });

        document.getElementById('new-folder-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('/admin/files/create-folder', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error creating folder');
            });
        });
    </script>
</x-layouts.admin>
