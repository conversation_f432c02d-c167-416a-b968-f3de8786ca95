<x-admin-layout>
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- <PERSON> Header -->
        <div class="sm:flex sm:items-center sm:justify-between mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit File</h1>
                <p class="mt-2 text-sm text-gray-700">{{ $fileName }} ({{ $fileSize }})</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <a href="{{ route('admin.simple-file-manager.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Files
                </a>
                <button onclick="saveFile()" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    Save File
                </button>
            </div>
        </div>

        <!-- File Info -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">File Information</h3>
                    </div>
                    <div class="text-sm text-gray-500">
                        Type: {{ $mimeType }}
                    </div>
                </div>
            </div>
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">File Path</label>
                        <p class="mt-1 text-sm text-gray-900 font-mono bg-gray-50 px-3 py-2 rounded">{{ $path }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">File Name</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $fileName }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">File Size</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $fileSize }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Editor -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">File Content</h3>
                    <div class="flex items-center space-x-4">
                        <div class="text-sm text-gray-500">
                            Lines: <span id="lineCount">0</span>
                        </div>
                        <div class="text-sm text-gray-500">
                            Characters: <span id="charCount">0</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <form id="editForm">
                    @csrf
                    <input type="hidden" name="path" value="{{ $path }}">
                    <div class="mb-4">
                        <textarea 
                            id="fileContent" 
                            name="content" 
                            rows="25" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                            placeholder="File content..."
                        >{{ $content }}</textarea>
                    </div>
                </form>
            </div>
        </div>

        <!-- Status Messages -->
        <div id="statusMessage" class="hidden mt-4 p-4 rounded-md">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg id="statusIcon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <!-- Icon will be set by JavaScript -->
                    </svg>
                </div>
                <div class="ml-3">
                    <p id="statusText" class="text-sm font-medium"></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const textarea = document.getElementById('fileContent');
        const lineCountElement = document.getElementById('lineCount');
        const charCountElement = document.getElementById('charCount');
        const statusMessage = document.getElementById('statusMessage');
        const statusIcon = document.getElementById('statusIcon');
        const statusText = document.getElementById('statusText');

        // Update counters
        function updateCounters() {
            const content = textarea.value;
            const lines = content.split('\n').length;
            const chars = content.length;
            
            lineCountElement.textContent = lines;
            charCountElement.textContent = chars;
        }

        // Initial count
        updateCounters();

        // Update on input
        textarea.addEventListener('input', updateCounters);

        // Save file function
        function saveFile() {
            const formData = new FormData(document.getElementById('editForm'));
            
            // Show loading state
            showStatus('info', 'Saving file...');
            
            fetch('{{ route("admin.simple-file-manager.save") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showStatus('success', 'File saved successfully!');
                } else {
                    showStatus('error', 'Error: ' + data.message);
                }
            })
            .catch(error => {
                showStatus('error', 'Error: ' + error.message);
            });
        }

        // Show status message
        function showStatus(type, message) {
            statusText.textContent = message;
            statusMessage.className = 'mt-4 p-4 rounded-md';
            
            if (type === 'success') {
                statusMessage.classList.add('bg-green-50', 'text-green-800');
                statusIcon.innerHTML = '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>';
                statusIcon.classList.add('text-green-400');
            } else if (type === 'error') {
                statusMessage.classList.add('bg-red-50', 'text-red-800');
                statusIcon.innerHTML = '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>';
                statusIcon.classList.add('text-red-400');
            } else {
                statusMessage.classList.add('bg-blue-50', 'text-blue-800');
                statusIcon.innerHTML = '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>';
                statusIcon.classList.add('text-blue-400');
            }
            
            statusMessage.classList.remove('hidden');
            
            // Auto-hide success messages
            if (type === 'success') {
                setTimeout(() => {
                    statusMessage.classList.add('hidden');
                }, 3000);
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+S to save
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                saveFile();
            }
        });

        // Add syntax highlighting hint
        const fileExtension = '{{ pathinfo($fileName, PATHINFO_EXTENSION) }}';
        if (['php', 'js', 'css', 'html', 'json', 'xml'].includes(fileExtension)) {
            textarea.style.fontFamily = 'Monaco, Menlo, "Ubuntu Mono", monospace';
        }
    </script>
</x-admin-layout>
