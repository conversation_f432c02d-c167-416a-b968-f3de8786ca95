<x-admin-layout>
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- <PERSON> Header -->
        <div class="sm:flex sm:items-center sm:justify-between mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">White-Label Management</h1>
                <p class="mt-2 text-sm text-gray-700">Customize branding, themes, and client portal appearance</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <button onclick="previewTheme()" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    Preview
                </button>
                <button onclick="saveSettings()" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                    </svg>
                    Save Settings
                </button>
            </div>
        </div>

        <!-- Branding Settings -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <div class="lg:col-span-2">
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Branding Settings</h3>
                        <p class="text-sm text-gray-500">Customize your company branding and appearance</p>
                    </div>
                    <div class="p-6">
                        <form id="brandingForm" class="space-y-6">
                            <!-- Company Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Company Name</label>
                                    <input type="text" name="company_name" value="{{ $settings['company_name'] }}" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Custom Domain</label>
                                    <input type="text" name="custom_domain" value="{{ $settings['custom_domain'] }}" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Support Email</label>
                                    <input type="email" name="support_email" value="{{ $settings['support_email'] }}" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Support Phone</label>
                                    <input type="text" name="support_phone" value="{{ $settings['support_phone'] }}" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>

                            <!-- Color Scheme -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-3">Color Scheme</label>
                                <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                                    @foreach($brandingOptions['color_schemes'] as $name => $colors)
                                    <div class="color-scheme-option cursor-pointer p-3 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors" data-scheme="{{ $name }}">
                                        <div class="flex space-x-2 mb-2">
                                            <div class="w-6 h-6 rounded-full" style="background-color: {{ $colors['primary'] }}"></div>
                                            <div class="w-6 h-6 rounded-full" style="background-color: {{ $colors['secondary'] }}"></div>
                                            <div class="w-6 h-6 rounded-full" style="background-color: {{ $colors['accent'] }}"></div>
                                        </div>
                                        <p class="text-xs font-medium text-gray-700 capitalize">{{ $name }}</p>
                                    </div>
                                    @endforeach
                                </div>
                            </div>

                            <!-- Typography -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Font Family</label>
                                <select name="font_family" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    @foreach($brandingOptions['typography'] as $font => $description)
                                    <option value="{{ $font }}" {{ $settings['font_family'] === $font ? 'selected' : '' }}>
                                        {{ $font }} - {{ $description }}
                                    </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Footer Text -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Footer Text</label>
                                <textarea name="footer_text" rows="2" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">{{ $settings['footer_text'] }}</textarea>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Logo Upload -->
            <div>
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Logo Management</h3>
                        <p class="text-sm text-gray-500">Upload and manage your logos</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            @foreach($brandingOptions['logo_options'] as $type => $options)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="text-sm font-medium text-gray-900">{{ ucfirst(str_replace('_', ' ', $type)) }}</h4>
                                    <span class="text-xs text-gray-500">{{ $options['max_size'] }}</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <input type="file" id="logo_{{ $type }}" accept="image/*" class="hidden" onchange="uploadLogo('{{ $type }}', this)">
                                        <button onclick="document.getElementById('logo_{{ $type }}').click()" class="text-sm text-blue-600 hover:text-blue-800">
                                            Upload {{ ucfirst(str_replace('_', ' ', $type)) }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customization Features -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Customization Features</h3>
                <p class="text-sm text-gray-500">Enable or disable white-label features</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    @foreach($customizationFeatures as $category => $features)
                    <div>
                        <h4 class="text-sm font-semibold text-gray-900 mb-4 capitalize">{{ str_replace('_', ' ', $category) }}</h4>
                        <div class="space-y-3">
                            @foreach($features as $feature => $config)
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-700">{{ ucfirst(str_replace('_', ' ', $feature)) }}</p>
                                    <p class="text-xs text-gray-500">
                                        @if($config['status'] === 'premium')
                                            Premium Feature
                                        @else
                                            {{ ucfirst($config['status']) }}
                                        @endif
                                    </p>
                                </div>
                                <div class="flex items-center">
                                    @if($config['status'] === 'premium')
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Premium</span>
                                    @else
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" class="sr-only peer" {{ $config['enabled'] ? 'checked' : '' }} onchange="toggleFeature('{{ $feature }}', this.checked)">
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                        </label>
                                    @endif
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- White-Label Clients -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">White-Label Clients</h3>
                <p class="text-sm text-gray-500">Manage client white-label configurations</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Domain</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clients</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($clients as $client)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-lg flex items-center justify-center mr-3" style="background-color: {{ $client['primary_color'] }}20;">
                                        <div class="w-6 h-6 rounded" style="background-color: {{ $client['primary_color'] }}"></div>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $client['company_name'] }}</div>
                                        <div class="text-sm text-gray-500">Setup {{ $client['setup_date']->diffForHumans() }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $client['domain'] }}</div>
                                <div class="text-sm text-gray-500">Last updated {{ $client['last_updated']->diffForHumans() }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {{ $client['plan'] }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    {{ $client['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                    {{ ucfirst($client['status']) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ number_format($client['clients_count']) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900">Edit</button>
                                    <button onclick="previewClient({{ $client['id'] }})" class="text-green-600 hover:text-green-900">Preview</button>
                                    @if($client['status'] === 'active')
                                        <button onclick="deployClient({{ $client['id'] }})" class="text-purple-600 hover:text-purple-900">Deploy</button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Notification Toast -->
    <div id="notification" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
            <div class="flex items-center">
                <div id="notification-icon" class="flex-shrink-0 mr-3">
                    <!-- Icon will be inserted here -->
                </div>
                <div>
                    <p id="notification-title" class="text-sm font-medium text-gray-900"></p>
                    <p id="notification-message" class="text-sm text-gray-500"></p>
                </div>
                <button onclick="hideNotification()" class="ml-4 text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        function showNotification(type, title, message) {
            const notification = document.getElementById('notification');
            const icon = document.getElementById('notification-icon');
            const titleEl = document.getElementById('notification-title');
            const messageEl = document.getElementById('notification-message');

            titleEl.textContent = title;
            messageEl.textContent = message;

            if (type === 'success') {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                `;
            } else {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                `;
            }

            notification.classList.remove('hidden');
            setTimeout(() => {
                hideNotification();
            }, 5000);
        }

        function hideNotification() {
            document.getElementById('notification').classList.add('hidden');
        }

        function saveSettings() {
            const form = document.getElementById('brandingForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = `
                <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Saving...
            `;
            button.disabled = true;

            fetch('/admin/white-label/update-settings', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Settings Saved', data.message);
                } else {
                    showNotification('error', 'Save Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Save Failed', 'Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }

        function uploadLogo(logoType, input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];
                const formData = new FormData();
                formData.append('logo', file);
                formData.append('logo_type', logoType);

                fetch('/admin/white-label/upload-logo', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showNotification('success', 'Logo Uploaded', data.message);
                        // Update logo preview
                        const logoContainer = input.closest('.border').querySelector('.w-16');
                        logoContainer.innerHTML = `<img src="${data.url}" alt="Logo" class="w-full h-full object-contain rounded-lg">`;
                    } else {
                        showNotification('error', 'Upload Failed', data.message);
                    }
                })
                .catch(error => {
                    showNotification('error', 'Upload Failed', 'Network error occurred');
                    console.error('Error:', error);
                });
            }
        }

        function toggleFeature(feature, enabled) {
            fetch('/admin/white-label/toggle-feature', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ feature: feature, enabled: enabled })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Feature Updated', `${feature} has been ${enabled ? 'enabled' : 'disabled'}`);
                } else {
                    showNotification('error', 'Update Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Update Failed', 'Network error occurred');
                console.error('Error:', error);
            });
        }

        function previewTheme() {
            const form = document.getElementById('brandingForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = `
                <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                Generating Preview...
            `;
            button.disabled = true;

            fetch('/admin/white-label/preview-theme', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Preview Generated', data.message);
                    if (data.preview_url) {
                        window.open(data.preview_url, '_blank');
                    }
                } else {
                    showNotification('error', 'Preview Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Preview Failed', 'Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }

        function previewClient(clientId) {
            showNotification('success', 'Client Preview', 'Opening client preview in new window');
            // Simulate opening client preview
            setTimeout(() => {
                window.open(`/admin/white-label/client-preview/${clientId}`, '_blank');
            }, 1000);
        }

        function deployClient(clientId) {
            if (!confirm('Are you sure you want to deploy the white-label configuration for this client?')) return;

            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Deploying...';
            button.disabled = true;

            fetch('/admin/white-label/deploy', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ client_id: clientId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Deployment Successful', data.message);
                } else {
                    showNotification('error', 'Deployment Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Deployment Failed', 'Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
        }

        // Color scheme selection
        document.addEventListener('DOMContentLoaded', function() {
            const colorSchemes = document.querySelectorAll('.color-scheme-option');
            colorSchemes.forEach(scheme => {
                scheme.addEventListener('click', function() {
                    // Remove active class from all schemes
                    colorSchemes.forEach(s => s.classList.remove('border-blue-500', 'bg-blue-50'));

                    // Add active class to selected scheme
                    this.classList.add('border-blue-500', 'bg-blue-50');

                    // Update form values (you would implement this based on your needs)
                    const schemeName = this.dataset.scheme;
                    showNotification('success', 'Color Scheme Selected', `${schemeName} color scheme selected`);
                });
            });

            // Add smooth animations
            const cards = document.querySelectorAll('.bg-white');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</x-admin-layout>
