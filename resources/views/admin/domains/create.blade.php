
<x-layouts.admin title="Add New Domain" header="Add New Domain">
    <!-- Dashboard Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                        Add New Domain
                    </span>
                </h1>
                <p class="text-gray-600 mt-1">Create a new domain with configuration settings</p>
            </div>
            <div class="flex items-center space-x-4">
                <a href="{{ route('admin.domains.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    <span>Back to Domains</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Create Domain Form -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100">
        <div class="p-6 border-b border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900">Domain Information</h3>
            <p class="text-sm text-gray-600 mt-1">Enter the domain details and configuration settings</p>
        </div>

        <form method="POST" action="{{ route('admin.domains.store') }}" class="p-6 space-y-6">
            @csrf

            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Domain Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Domain Name *</label>
                    <input type="text" name="name" id="name" value="{{ old('name') }}" required
                           placeholder="example.com"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Owner -->
                <div>
                    <label for="user_id" class="block text-sm font-medium text-gray-700 mb-2">Domain Owner *</label>
                    <select name="user_id" id="user_id" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('user_id') border-red-500 @enderror">
                        <option value="">Select Owner</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                {{ $user->name }} ({{ $user->email }})
                            </option>
                        @endforeach
                    </select>
                    @error('user_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status *</label>
                    <select name="status" id="status" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('status') border-red-500 @enderror">
                        <option value="active" {{ old('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ old('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        <option value="suspended" {{ old('status') === 'suspended' ? 'selected' : '' }}>Suspended</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Registrar -->
                <div>
                    <label for="registrar" class="block text-sm font-medium text-gray-700 mb-2">Registrar</label>
                    <input type="text" name="registrar" id="registrar" value="{{ old('registrar') }}"
                           placeholder="GoDaddy, Namecheap, etc."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('registrar') border-red-500 @enderror">
                    @error('registrar')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Registration & Expiry Dates -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Registration Date -->
                <div>
                    <label for="registered_at" class="block text-sm font-medium text-gray-700 mb-2">Registration Date</label>
                    <input type="date" name="registered_at" id="registered_at" value="{{ old('registered_at') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('registered_at') border-red-500 @enderror">
                    @error('registered_at')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Expiry Date -->
                <div>
                    <label for="expires_at" class="block text-sm font-medium text-gray-700 mb-2">Expiry Date</label>
                    <input type="date" name="expires_at" id="expires_at" value="{{ old('expires_at') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('expires_at') border-red-500 @enderror">
                    @error('expires_at')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Nameservers -->
            <div class="space-y-4">
                <h4 class="text-md font-medium text-gray-900">Nameservers</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="nameserver1" class="block text-sm font-medium text-gray-700 mb-2">Nameserver 1</label>
                        <input type="text" name="nameserver1" id="nameserver1" value="{{ old('nameserver1') }}"
                               placeholder="ns1.example.com"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('nameserver1') border-red-500 @enderror">
                        @error('nameserver1')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="nameserver2" class="block text-sm font-medium text-gray-700 mb-2">Nameserver 2</label>
                        <input type="text" name="nameserver2" id="nameserver2" value="{{ old('nameserver2') }}"
                               placeholder="ns2.example.com"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('nameserver2') border-red-500 @enderror">
                        @error('nameserver2')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="nameserver3" class="block text-sm font-medium text-gray-700 mb-2">Nameserver 3</label>
                        <input type="text" name="nameserver3" id="nameserver3" value="{{ old('nameserver3') }}"
                               placeholder="ns3.example.com"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('nameserver3') border-red-500 @enderror">
                        @error('nameserver3')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="nameserver4" class="block text-sm font-medium text-gray-700 mb-2">Nameserver 4</label>
                        <input type="text" name="nameserver4" id="nameserver4" value="{{ old('nameserver4') }}"
                               placeholder="ns4.example.com"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('nameserver4') border-red-500 @enderror">
                        @error('nameserver4')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Hosting Configuration -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Document Root -->
                <div>
                    <label for="document_root" class="block text-sm font-medium text-gray-700 mb-2">Document Root</label>
                    <input type="text" name="document_root" id="document_root" value="{{ old('document_root') }}"
                           placeholder="/var/www/html"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('document_root') border-red-500 @enderror">
                    @error('document_root')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- PHP Version -->
                <div>
                    <label for="php_version" class="block text-sm font-medium text-gray-700 mb-2">PHP Version</label>
                    <select name="php_version" id="php_version"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('php_version') border-red-500 @enderror">
                        <option value="">Select PHP Version</option>
                        <option value="8.3" {{ old('php_version') === '8.3' ? 'selected' : '' }}>PHP 8.3</option>
                        <option value="8.2" {{ old('php_version') === '8.2' ? 'selected' : '' }}>PHP 8.2</option>
                        <option value="8.1" {{ old('php_version') === '8.1' ? 'selected' : '' }}>PHP 8.1</option>
                        <option value="8.0" {{ old('php_version') === '8.0' ? 'selected' : '' }}>PHP 8.0</option>
                        <option value="7.4" {{ old('php_version') === '7.4' ? 'selected' : '' }}>PHP 7.4</option>
                    </select>
                    @error('php_version')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- SSL Configuration -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- SSL Provider -->
                <div>
                    <label for="ssl_provider" class="block text-sm font-medium text-gray-700 mb-2">SSL Provider</label>
                    <input type="text" name="ssl_provider" id="ssl_provider" value="{{ old('ssl_provider') }}"
                           placeholder="Let's Encrypt, Cloudflare, etc."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('ssl_provider') border-red-500 @enderror">
                    @error('ssl_provider')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Checkboxes -->
            <div class="space-y-4">
                <div class="flex items-center">
                    <input type="checkbox" name="auto_renew" id="auto_renew" value="1" {{ old('auto_renew') ? 'checked' : '' }}
                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                    <label for="auto_renew" class="ml-2 text-sm text-gray-700">Enable auto-renewal</label>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" name="dns_managed" id="dns_managed" value="1" {{ old('dns_managed') ? 'checked' : '' }}
                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                    <label for="dns_managed" class="ml-2 text-sm text-gray-700">DNS managed by us</label>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" name="ssl_enabled" id="ssl_enabled" value="1" {{ old('ssl_enabled') ? 'checked' : '' }}
                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                    <label for="ssl_enabled" class="ml-2 text-sm text-gray-700">Enable SSL certificate</label>
                </div>
            </div>

            <!-- Notes -->
            <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                <textarea name="notes" id="notes" rows="4" placeholder="Additional notes about this domain..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('notes') border-red-500 @enderror">{{ old('notes') }}</textarea>
                @error('notes')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-100">
                <a href="{{ route('admin.domains.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                    Cancel
                </a>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                    Create Domain
                </button>
            </div>
        </form>
    </div>
</x-layouts.admin>
