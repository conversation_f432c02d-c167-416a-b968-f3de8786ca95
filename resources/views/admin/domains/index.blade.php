<x-admin-layout>
    <div class="px-3 sm:px-6 lg:px-8 min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50">
        <!-- Modern Page Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 pt-4 sm:pt-6 space-y-4 sm:space-y-0">
            <div class="space-y-2">
                <h1 class="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-purple-600 via-indigo-600 to-purple-800 bg-clip-text text-transparent">
                    Domain Management
                </h1>
                <p class="text-purple-600/80 text-base sm:text-lg font-medium">Manage domains, SSL certificates, and DNS settings</p>
                <div class="flex items-center space-x-3 text-sm text-purple-500">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span>System Online</span>
                    </div>
                    <span>•</span>
                    <span id="live-time">{{ now()->format('H:i:s') }}</span>
                </div>
            </div>
            <div class="flex flex-col sm:flex-row items-center space-y-3 sm:space-y-0 sm:space-x-4 w-full sm:w-auto">
                <a href="{{ route('admin.domains.create') }}"
                   class="group inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 border border-transparent rounded-2xl font-semibold text-sm text-white shadow-lg hover:shadow-xl hover:from-purple-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 w-full sm:w-auto">
                    <svg class="w-5 h-5 mr-2 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Add Domain
                </a>
                <a href="{{ route('admin.domains.export') }}"
                   class="group inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 border border-transparent rounded-2xl font-semibold text-sm text-white shadow-lg hover:shadow-xl hover:from-emerald-600 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 w-full sm:w-auto">
                    <svg class="w-5 h-5 mr-2 group-hover:translate-y-[-2px] transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2 2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export
                </a>
            </div>
        </div>

        <!-- Enhanced Statistics Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 sm:gap-6 mb-8">
            <!-- Total Domains Card -->
            <div class="group bg-gradient-to-br from-purple-500 via-indigo-500 to-purple-600 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-purple-400/20 backdrop-blur-sm">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white">Total Domains</h3>
                            <p class="text-white/80 text-sm">All registered</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="counter-number text-white text-3xl font-bold">{{ $stats['total_domains'] }}</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-white/80 text-sm">
                    <span>Domains</span>
                    <span class="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full font-medium">
                        Total
                    </span>
                </div>
            </div>

            <!-- Active Domains Card -->
            <div class="group bg-gradient-to-br from-emerald-500 via-green-500 to-teal-600 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-emerald-400/20 backdrop-blur-sm">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white">Active</h3>
                            <p class="text-white/80 text-sm">Running domains</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="counter-number text-white text-3xl font-bold">{{ $stats['active_domains'] }}</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-white/80 text-sm">
                    <span>Inactive</span>
                    <span class="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full font-medium">
                        Active
                    </span>
                </div>
            </div>

            <!-- SSL Enabled Card -->
            <div class="group bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-blue-400/20 backdrop-blur-sm">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white">SSL Enabled</h3>
                            <p class="text-white/80 text-sm">Secure domains</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="counter-number text-white text-3xl font-bold">{{ $stats['ssl_enabled'] }}</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-white/80 text-sm">
                    <span>Disabled</span>
                    <span class="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full font-medium">
                        Secured
                    </span>
                </div>
            </div>

            <!-- Expiring Soon Card -->
            <div class="group bg-gradient-to-br from-orange-500 via-red-500 to-pink-600 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-orange-400/20 backdrop-blur-sm">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white">Expiring Soon</h3>
                            <p class="text-white/80 text-sm">Needs renewal</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="counter-number text-white text-3xl font-bold">{{ $stats['expiring_soon'] }}</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-white/80 text-sm">
                    <span>Safe</span>
                    <span class="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full font-medium">
                        Warning
                    </span>
                </div>
            </div>
        </div>

        <!-- Enhanced Filters and Search -->
        <div class="bg-white/80 backdrop-blur-sm shadow-xl rounded-2xl border border-purple-200/50 mb-8 overflow-hidden">
            <div class="bg-gradient-to-r from-purple-500/10 to-indigo-500/10 px-6 py-4 border-b border-purple-200/30">
                <h3 class="text-lg font-semibold text-purple-800 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    Search & Filter Domains
                </h3>
            </div>
            <div class="p-6">
                <form method="GET" action="{{ route('admin.domains.index') }}" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- Enhanced Search -->
                        <div class="lg:col-span-2">
                            <label for="search" class="block text-sm font-semibold text-purple-700 mb-2">Search Domains</label>
                            <div class="relative group">
                                <input type="text"
                                       name="search"
                                       id="search"
                                       value="{{ request('search') }}"
                                       placeholder="Search by domain name or user..."
                                       class="block w-full pl-12 pr-4 py-3 border border-purple-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white/50 backdrop-blur-sm text-gray-900 placeholder-purple-400 transition-all duration-300 group-hover:bg-white/70">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-purple-400 group-hover:text-purple-600 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Status Filter -->
                        <div>
                            <label for="status" class="block text-sm font-semibold text-purple-700 mb-2">Status Filter</label>
                            <select name="status" id="status" class="block w-full px-4 py-3 border border-purple-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white/50 backdrop-blur-sm text-gray-900 transition-all duration-300 hover:bg-white/70">
                                <option value="">All Statuses</option>
                                <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                <option value="suspended" {{ request('status') === 'suspended' ? 'selected' : '' }}>Suspended</option>
                            </select>
                        </div>

                        <!-- Enhanced Actions -->
                        <div class="flex items-end space-x-3">
                            <button type="submit" class="group flex-1 bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-xl hover:from-purple-700 hover:to-indigo-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                <svg class="w-4 h-4 inline mr-2 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                                </svg>
                                Filter
                            </button>
                            <a href="{{ route('admin.domains.index') }}" class="group px-4 py-3 border border-purple-300 rounded-xl text-purple-700 hover:bg-purple-50 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 bg-white/50 backdrop-blur-sm">
                                <svg class="w-4 h-4 group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Enhanced Domains Table -->
        <div class="bg-white/80 backdrop-blur-sm shadow-xl rounded-2xl border border-purple-200/50 overflow-hidden">
            <div class="bg-gradient-to-r from-purple-500/10 to-indigo-500/10 px-6 py-4 border-b border-purple-200/30">
                <h3 class="text-lg font-semibold text-purple-800 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    Domains Overview
                </h3>
            </div>
            <div class="overflow-x-auto">
                @if($domains->count() > 0)
                <table class="min-w-full divide-y divide-purple-200/30">
                <thead class="bg-gradient-to-r from-purple-50 to-indigo-50">
                    <tr>
                        <th class="px-6 py-4 text-left text-xs font-bold text-purple-700 uppercase tracking-wider">
                            <input type="checkbox" id="select-all" class="rounded border-purple-300 text-purple-600 focus:ring-purple-500 bg-white/50">
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-purple-700 uppercase tracking-wider">Domain</th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-purple-700 uppercase tracking-wider">Owner</th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-purple-700 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-purple-700 uppercase tracking-wider">SSL</th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-purple-700 uppercase tracking-wider">Expires</th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-purple-700 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-purple-700 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white/50 backdrop-blur-sm divide-y divide-purple-200/30">
                    @foreach($domains as $domain)
                    <tr class="group hover:bg-purple-50/50 transition-all duration-300 hover:shadow-md">
                        <td class="px-6 py-5 whitespace-nowrap">
                            <input type="checkbox" name="domains[]" value="{{ $domain->id }}" class="domain-checkbox rounded border-purple-300 text-purple-600 focus:ring-purple-500 bg-white/50">
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-400 to-indigo-500 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-bold text-gray-900 group-hover:text-purple-700 transition-colors duration-300">{{ $domain->name }}</div>
                                    <div class="text-sm text-purple-600/70 font-medium">{{ $domain->registrar ?? 'N/A' }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap">
                            <div class="text-sm font-semibold text-gray-900">{{ $domain->user->name ?? 'N/A' }}</div>
                            <div class="text-sm text-purple-600/70">{{ $domain->user->email ?? 'N/A' }}</div>
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap">
                            <span class="inline-flex px-3 py-1.5 text-xs font-bold rounded-full shadow-sm
                                {{ $domain->status === 'active' ? 'bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 border border-emerald-200' :
                                   ($domain->status === 'inactive' ? 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border border-gray-200' : 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border border-red-200') }}">
                                {{ ucfirst($domain->status) }}
                            </span>
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap">
                            <span class="inline-flex px-3 py-1.5 text-xs font-bold rounded-full shadow-sm
                                {{ $domain->ssl_enabled ? 'bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 border border-emerald-200' : 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border border-gray-200' }}">
                                {{ $domain->ssl_enabled ? 'Enabled' : 'Disabled' }}
                            </span>
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap text-sm">
                            @if($domain->expires_at)
                                <div class="font-semibold {{ $domain->isExpiringSoon() ? 'text-orange-600' : ($domain->isExpired() ? 'text-red-600' : 'text-gray-900') }}">
                                    {{ $domain->expires_at->format('M d, Y') }}
                                </div>
                                <div class="text-xs text-purple-600/70 font-medium">
                                    {{ $domain->expires_at->diffForHumans() }}
                                </div>
                            @else
                                <span class="text-gray-400 font-medium">N/A</span>
                            @endif
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap text-sm text-purple-600/70 font-medium">
                            {{ $domain->created_at->format('M d, Y') }}
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-3">
                                <a href="{{ route('admin.domains.show', $domain) }}" class="group inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-md">
                                    <svg class="w-3 h-3 mr-1 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    View
                                </a>
                                <a href="{{ route('admin.domains.edit', $domain) }}" class="group inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-lg hover:from-purple-600 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-md">
                                    <svg class="w-3 h-3 mr-1 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                    Edit
                                </a>
                                <form method="POST" action="{{ route('admin.domains.destroy', $domain) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this domain?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="group inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-lg hover:from-red-600 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 shadow-md">
                                        <svg class="w-3 h-3 mr-1 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        Delete
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-100">
            {{ $domains->links() }}
        </div>
        @else
        <div class="p-16 text-center bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl border border-purple-200/50">
            <div class="w-24 h-24 bg-gradient-to-br from-purple-400 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-xl">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                </svg>
            </div>
            <h3 class="text-2xl font-bold text-purple-800 mb-3">No domains found</h3>
            <p class="text-purple-600/80 text-lg mb-8 max-w-md mx-auto">Get started by adding your first domain to begin managing your hosting services.</p>
            <a href="{{ route('admin.domains.create') }}" class="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-2xl hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl font-semibold">
                <svg class="w-5 h-5 mr-2 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Add Your First Domain
            </a>
        </div>
        @endif
    </div>

    <!-- Enhanced Bulk Actions -->
    <div id="bulk-actions" class="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-purple-200/50 px-8 py-5 hidden z-50">
        <div class="flex items-center space-x-6">
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
                <span class="text-sm font-semibold text-purple-700">
                    <span id="selected-count" class="text-purple-600 font-bold">0</span> domains selected
                </span>
            </div>
            <form method="POST" action="{{ route('admin.domains.bulk-action') }}" class="flex items-center space-x-4">
                @csrf
                <input type="hidden" name="domains" id="selected-domains">
                <select name="action" class="px-4 py-2 border border-purple-300 rounded-xl text-sm bg-white/50 backdrop-blur-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 font-medium">
                    <option value="">Choose action...</option>
                    <option value="activate">Activate</option>
                    <option value="deactivate">Deactivate</option>
                    <option value="enable_ssl">Enable SSL</option>
                    <option value="disable_ssl">Disable SSL</option>
                    <option value="delete">Delete</option>
                </select>
                <button type="submit" class="group bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white px-6 py-2 rounded-xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <svg class="w-4 h-4 inline mr-1 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Apply
                </button>
            </form>
            <button onclick="clearSelection()" class="group text-purple-500 hover:text-purple-700 p-2 rounded-xl hover:bg-purple-100 transition-all duration-300">
                <svg class="w-5 h-5 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- Enhanced Interactive Features -->
    <script>
        // Live Clock Update
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const clockElement = document.getElementById('live-time');
            if (clockElement) {
                clockElement.textContent = timeString;
            }
        }

        // Update clock every second
        setInterval(updateClock, 1000);
        updateClock(); // Initial call

        // Animate counters on load
        document.addEventListener('DOMContentLoaded', function() {
            const counters = document.querySelectorAll('.counter-number');
            counters.forEach(counter => {
                const target = parseInt(counter.textContent);
                let current = 0;
                const increment = target / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current);
                }, 30);
            });
        });

        // Enhanced Bulk selection functionality
        const selectAllCheckbox = document.getElementById('select-all');
        const domainCheckboxes = document.querySelectorAll('.domain-checkbox');
        const bulkActions = document.getElementById('bulk-actions');
        const selectedCount = document.getElementById('selected-count');
        const selectedDomainsInput = document.getElementById('selected-domains');

        selectAllCheckbox?.addEventListener('change', function() {
            domainCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });

        domainCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBulkActions);
        });

        function updateBulkActions() {
            const selected = Array.from(domainCheckboxes).filter(cb => cb.checked);
            const count = selected.length;

            if (count > 0) {
                bulkActions.classList.remove('hidden');
                bulkActions.classList.add('animate-bounce');
                selectedCount.textContent = count;
                selectedDomainsInput.value = JSON.stringify(selected.map(cb => cb.value));
                setTimeout(() => {
                    bulkActions.classList.remove('animate-bounce');
                }, 1000);
            } else {
                bulkActions.classList.add('hidden');
            }
        }

        function clearSelection() {
            domainCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            selectAllCheckbox.checked = false;
            updateBulkActions();
        }

        // Add hover effects for table rows
        document.addEventListener('DOMContentLoaded', function() {
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(4px)';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                });
            });
        });
    </script>
    </div>
</x-admin-layout>
