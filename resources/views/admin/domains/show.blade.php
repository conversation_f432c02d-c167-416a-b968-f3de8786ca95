<x-layouts.admin title="Domain Details - {{ $domain->name }}" header="Domain Details">
    <!-- Dashboard Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                        {{ $domain->name }}
                    </span>
                </h1>
                <p class="text-gray-600 mt-1">Domain details and configuration</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{{ route('admin.domains.dns.index', $domain) }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                    </svg>
                    <span>DNS</span>
                </a>
                <a href="{{ route('admin.domains.ssl.index', $domain) }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    <span>SSL</span>
                </a>
                <a href="{{ route('admin.domains.edit', $domain) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    <span>Edit</span>
                </a>
                <a href="{{ route('admin.domains.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    <span>Back</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Domain Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <!-- Status -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Status</p>
                        <p class="text-2xl font-bold {{ $domain->status === 'active' ? 'text-green-600' : ($domain->status === 'inactive' ? 'text-gray-600' : 'text-red-600') }}">
                            {{ ucfirst($domain->status) }}
                        </p>
                    </div>
                    <div class="w-12 h-12 {{ $domain->status === 'active' ? 'bg-green-100' : ($domain->status === 'inactive' ? 'bg-gray-100' : 'bg-red-100') }} rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 {{ $domain->status === 'active' ? 'text-green-600' : ($domain->status === 'inactive' ? 'text-gray-600' : 'text-red-600') }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            @if($domain->status === 'active')
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            @elseif($domain->status === 'inactive')
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            @else
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            @endif
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- SSL Status -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">SSL Certificate</p>
                        <p class="text-2xl font-bold {{ $domain->ssl_enabled ? 'text-green-600' : 'text-gray-600' }}">
                            {{ $domain->ssl_enabled ? 'Enabled' : 'Disabled' }}
                        </p>
                    </div>
                    <div class="w-12 h-12 {{ $domain->ssl_enabled ? 'bg-green-100' : 'bg-gray-100' }} rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 {{ $domain->ssl_enabled ? 'text-green-600' : 'text-gray-600' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Auto Renew -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Auto Renew</p>
                        <p class="text-2xl font-bold {{ $domain->auto_renew ? 'text-blue-600' : 'text-gray-600' }}">
                            {{ $domain->auto_renew ? 'Enabled' : 'Disabled' }}
                        </p>
                    </div>
                    <div class="w-12 h-12 {{ $domain->auto_renew ? 'bg-blue-100' : 'bg-gray-100' }} rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 {{ $domain->auto_renew ? 'text-blue-600' : 'text-gray-600' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- DNS Managed -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">DNS Managed</p>
                        <p class="text-2xl font-bold {{ $domain->dns_managed ? 'text-indigo-600' : 'text-gray-600' }}">
                            {{ $domain->dns_managed ? 'Yes' : 'No' }}
                        </p>
                    </div>
                    <div class="w-12 h-12 {{ $domain->dns_managed ? 'bg-indigo-100' : 'bg-gray-100' }} rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 {{ $domain->dns_managed ? 'text-indigo-600' : 'text-gray-600' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Domain Information -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Domain Information</h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Domain Name</span>
                    <span class="text-sm text-gray-900 font-medium">{{ $domain->name }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Owner</span>
                    <span class="text-sm text-gray-900">{{ $domain->user->name ?? 'N/A' }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Registrar</span>
                    <span class="text-sm text-gray-900">{{ $domain->registrar ?? 'N/A' }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Registration Date</span>
                    <span class="text-sm text-gray-900">{{ $domain->registered_at ? $domain->registered_at->format('M d, Y') : 'N/A' }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Expiry Date</span>
                    <span class="text-sm {{ $domain->isExpiringSoon() ? 'text-orange-600' : ($domain->isExpired() ? 'text-red-600' : 'text-gray-900') }}">
                        {{ $domain->expires_at ? $domain->expires_at->format('M d, Y') : 'N/A' }}
                        @if($domain->expires_at)
                            <span class="text-xs text-gray-500 block">{{ $domain->expires_at->diffForHumans() }}</span>
                        @endif
                    </span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Created</span>
                    <span class="text-sm text-gray-900">{{ $domain->created_at->format('M d, Y H:i') }}</span>
                </div>
            </div>
        </div>

        <!-- Technical Configuration -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Technical Configuration</h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Document Root</span>
                    <span class="text-sm text-gray-900 font-mono">{{ $domain->document_root ?? 'N/A' }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">PHP Version</span>
                    <span class="text-sm text-gray-900">{{ $domain->php_version ?? 'N/A' }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">SSL Provider</span>
                    <span class="text-sm text-gray-900">{{ $domain->ssl_provider ?? 'N/A' }}</span>
                </div>
            </div>
        </div>

        <!-- Nameservers -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Nameservers</h3>
            </div>
            <div class="p-6 space-y-3">
                @if($domain->nameserver1 || $domain->nameserver2 || $domain->nameserver3 || $domain->nameserver4)
                    @foreach(['nameserver1', 'nameserver2', 'nameserver3', 'nameserver4'] as $ns)
                        @if($domain->$ns)
                            <div class="flex items-center space-x-3 py-2">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <span class="text-xs font-medium text-blue-600">NS{{ substr($ns, -1) }}</span>
                                </div>
                                <span class="text-sm text-gray-900 font-mono">{{ $domain->$ns }}</span>
                            </div>
                        @endif
                    @endforeach
                @else
                    <p class="text-sm text-gray-500 text-center py-4">No nameservers configured</p>
                @endif
            </div>
        </div>

        <!-- Notes -->
        @if($domain->notes)
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Notes</h3>
            </div>
            <div class="p-6">
                <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ $domain->notes }}</p>
            </div>
        </div>
        @endif
    </div>

    <!-- Related Information -->
    <div class="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Subdomains -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Subdomains</h3>
            </div>
            <div class="p-6">
                @if($domain->subdomains->count() > 0)
                    <div class="space-y-2">
                        @foreach($domain->subdomains->take(5) as $subdomain)
                            <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                                <span class="text-sm text-gray-900">{{ $subdomain->name }}</span>
                                <span class="text-xs text-gray-500">{{ $subdomain->status }}</span>
                            </div>
                        @endforeach
                        @if($domain->subdomains->count() > 5)
                            <p class="text-xs text-gray-500 text-center pt-2">
                                +{{ $domain->subdomains->count() - 5 }} more
                            </p>
                        @endif
                    </div>
                @else
                    <p class="text-sm text-gray-500 text-center py-4">No subdomains</p>
                @endif
            </div>
        </div>

        <!-- DNS Records -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">DNS Records</h3>
                <a href="{{ route('admin.domains.dns.index', $domain) }}" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                    Manage DNS →
                </a>
            </div>
            <div class="p-6">
                @if($domain->dnsRecords->count() > 0)
                    <div class="space-y-2">
                        @foreach($domain->dnsRecords->take(5) as $record)
                            <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                                <span class="text-sm text-gray-900">{{ $record->type }}</span>
                                <span class="text-xs text-gray-500">{{ $record->name }}</span>
                            </div>
                        @endforeach
                        @if($domain->dnsRecords->count() > 5)
                            <p class="text-xs text-gray-500 text-center pt-2">
                                +{{ $domain->dnsRecords->count() - 5 }} more
                            </p>
                        @endif
                    </div>
                @else
                    <p class="text-sm text-gray-500 text-center py-4">No DNS records</p>
                @endif
            </div>
        </div>

        <!-- SSL Certificates -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">SSL Certificates</h3>
                <a href="{{ route('admin.domains.ssl.index', $domain) }}" class="text-green-600 hover:text-green-800 text-sm font-medium">
                    Manage SSL →
                </a>
            </div>
            <div class="p-6">
                @if($domain->sslCertificates->count() > 0)
                    <div class="space-y-2">
                        @foreach($domain->sslCertificates->take(3) as $cert)
                            <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                                <span class="text-sm text-gray-900">{{ $cert->type_name }}</span>
                                <span class="text-xs {{ $cert->expires_at && $cert->expires_at->isPast() ? 'text-red-500' : 'text-green-500' }}">
                                    {{ $cert->status }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-sm text-gray-500 text-center py-4">No SSL certificates</p>
                @endif
            </div>
        </div>
    </div>
</x-layouts.admin>
