<x-admin-layout>
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- <PERSON> Header -->
        <div class="sm:flex sm:items-center sm:justify-between mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Create New Application</h1>
                <p class="mt-2 text-sm text-gray-700">Deploy a new application to your server</p>
            </div>
            <div class="mt-4 sm:mt-0">
                <a href="{{ route('admin.applications.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Applications
                </a>
            </div>
        </div>

        <!-- One-Click Installation Templates -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">One-Click Installation</h2>
                <p class="text-sm text-gray-600 mt-1">Choose from popular applications and install them instantly</p>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach($appTemplates as $template)
                    <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-md transition-all duration-200 cursor-pointer template-card"
                         data-template-id="{{ $template->id }}"
                         data-template-name="{{ $template->name }}"
                         data-template-type="{{ $template->type }}">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                @if($template->icon_url)
                                    <img src="{{ $template->icon_url }}" alt="{{ $template->name }}" class="w-10 h-10 rounded">
                                @else
                                    <div class="w-10 h-10 bg-blue-100 rounded flex items-center justify-center">
                                        <span class="text-blue-600 font-semibold text-sm">{{ substr($template->name, 0, 2) }}</span>
                                    </div>
                                @endif
                            </div>
                            <div class="flex-1 min-w-0">
                                <h3 class="text-sm font-medium text-gray-900">{{ $template->name }}</h3>
                                <p class="text-xs text-gray-500 mt-1">{{ Str::limit($template->description, 60) }}</p>
                                <div class="flex items-center mt-2 space-x-2">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ ucfirst($template->type) }}
                                    </span>
                                    <div class="flex items-center">
                                        @for($i = 1; $i <= 5; $i++)
                                            @if($i <= floor($template->rating))
                                                <svg class="w-3 h-3 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                </svg>
                                            @else
                                                <svg class="w-3 h-3 text-gray-300 fill-current" viewBox="0 0 20 20">
                                                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                </svg>
                                            @endif
                                        @endfor
                                        <span class="text-xs text-gray-500 ml-1">({{ $template->formatted_rating }})</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Manual Installation Form -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Manual Installation</h2>
                <p class="text-sm text-gray-600 mt-1">Create a custom application manually</p>
            </div>

            <div class="bg-white shadow-sm rounded-lg border border-gray-200">
            <form action="{{ route('admin.applications.store') }}" method="POST" class="p-6">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- User Selection -->
                    <div>
                        <label for="user_id" class="block text-sm font-medium text-gray-700 mb-2">Owner</label>
                        <select name="user_id" id="user_id" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select Owner</option>
                            @foreach($users as $user)
                                <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                    {{ $user->name }} ({{ $user->email }})
                                </option>
                            @endforeach
                        </select>
                        @error('user_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Domain Selection -->
                    <div>
                        <label for="domain_id" class="block text-sm font-medium text-gray-700 mb-2">Domain (Optional)</label>
                        <select name="domain_id" id="domain_id"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select Domain</option>
                            @foreach($domains as $domain)
                                <option value="{{ $domain->id }}" {{ old('domain_id') == $domain->id ? 'selected' : '' }}>
                                    {{ $domain->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('domain_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Application Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Application Name</label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Application Type -->
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Application Type</label>
                        <select name="type" id="type" required onchange="updateVersionOptions()"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select Application Type</option>
                            <option value="wordpress" {{ old('type') === 'wordpress' ? 'selected' : '' }}>WordPress</option>
                            <option value="laravel" {{ old('type') === 'laravel' ? 'selected' : '' }}>Laravel</option>
                            <option value="nodejs" {{ old('type') === 'nodejs' ? 'selected' : '' }}>Node.js</option>
                            <option value="react" {{ old('type') === 'react' ? 'selected' : '' }}>React</option>
                            <option value="vue" {{ old('type') === 'vue' ? 'selected' : '' }}>Vue.js</option>
                            <option value="static" {{ old('type') === 'static' ? 'selected' : '' }}>Static HTML</option>
                            <option value="custom" {{ old('type') === 'custom' ? 'selected' : '' }}>Custom</option>
                        </select>
                        @error('type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Version -->
                    <div>
                        <label for="version" class="block text-sm font-medium text-gray-700 mb-2">Version</label>
                        <select name="version" id="version"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select Version</option>
                        </select>
                        @error('version')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Install Path -->
                    <div>
                        <label for="install_path" class="block text-sm font-medium text-gray-700 mb-2">Install Path</label>
                        <input type="text" name="install_path" id="install_path" value="{{ old('install_path') }}" placeholder="/var/www/html"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        @error('install_path')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- URL -->
                    <div>
                        <label for="url" class="block text-sm font-medium text-gray-700 mb-2">Application URL</label>
                        <input type="url" name="url" id="url" value="{{ old('url') }}" placeholder="https://example.com"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        @error('url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Admin URL -->
                    <div>
                        <label for="admin_url" class="block text-sm font-medium text-gray-700 mb-2">Admin URL</label>
                        <input type="url" name="admin_url" id="admin_url" value="{{ old('admin_url') }}" placeholder="https://example.com/admin"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        @error('admin_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Admin Username -->
                    <div>
                        <label for="admin_username" class="block text-sm font-medium text-gray-700 mb-2">Admin Username</label>
                        <input type="text" name="admin_username" id="admin_username" value="{{ old('admin_username') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        @error('admin_username')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Admin Email -->
                    <div>
                        <label for="admin_email" class="block text-sm font-medium text-gray-700 mb-2">Admin Email</label>
                        <input type="email" name="admin_email" id="admin_email" value="{{ old('admin_email') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        @error('admin_email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Auto Update -->
                    <div class="md:col-span-2">
                        <div class="flex items-center">
                            <input type="checkbox" name="auto_update" id="auto_update" value="1" 
                                   {{ old('auto_update') ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="auto_update" class="ml-2 block text-sm text-gray-900">
                                Enable automatic updates
                            </label>
                        </div>
                        @error('auto_update')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Notes -->
                    <div class="md:col-span-2">
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                        <textarea name="notes" id="notes" rows="4" placeholder="Additional notes about this application..."
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">{{ old('notes') }}</textarea>
                        @error('notes')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('admin.applications.index') }}" 
                       class="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                        Create Application
                    </button>
                </div>
            </form>
        </div>

        <!-- Installation Guide -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-medium text-blue-900 mb-4">Installation Guide</h3>
            <div class="text-sm text-blue-800">
                <p class="mb-2"><strong>WordPress:</strong> Latest version will be downloaded and configured automatically</p>
                <p class="mb-2"><strong>Laravel:</strong> Fresh Laravel installation with composer dependencies</p>
                <p class="mb-2"><strong>Node.js:</strong> Node.js runtime environment setup</p>
                <p class="mb-2"><strong>React/Vue:</strong> Build tools and development environment</p>
                <p><strong>Static HTML:</strong> Basic web server configuration</p>
            </div>
        </div>
    </div>

    <!-- One-Click Installation Modal -->
    <div id="installModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Install Application</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeInstallModal()">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form id="installForm" onsubmit="installApplication(event)">
                    <input type="hidden" id="templateId" name="template_id">

                    <div class="mb-4">
                        <label for="modalDomainId" class="block text-sm font-medium text-gray-700 mb-2">Domain</label>
                        <select id="modalDomainId" name="domain_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select Domain</option>
                            @foreach($domains as $domain)
                                <option value="{{ $domain->id }}">{{ $domain->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="modalAdminEmail" class="block text-sm font-medium text-gray-700 mb-2">Admin Email</label>
                        <input type="email" id="modalAdminEmail" name="admin_email" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div class="mb-4">
                        <label for="modalAdminUser" class="block text-sm font-medium text-gray-700 mb-2">Admin Username</label>
                        <input type="text" id="modalAdminUser" name="admin_user" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div class="mb-4">
                        <label for="modalAdminPass" class="block text-sm font-medium text-gray-700 mb-2">Admin Password</label>
                        <input type="password" id="modalAdminPass" name="admin_pass" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div class="mb-6">
                        <label for="modalSiteTitle" class="block text-sm font-medium text-gray-700 mb-2">Site Title (Optional)</label>
                        <input type="text" id="modalSiteTitle" name="site_title" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeInstallModal()" class="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            Cancel
                        </button>
                        <button type="submit" id="installButton" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            Install
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        const versionOptions = {
            wordpress: ['Latest', '6.4', '6.3', '6.2', '6.1'],
            laravel: ['Latest', '10.x', '9.x', '8.x'],
            nodejs: ['Latest', '20.x', '18.x', '16.x', '14.x'],
            react: ['Latest', '18.x', '17.x', '16.x'],
            vue: ['Latest', '3.x', '2.x'],
            static: ['HTML5'],
            custom: ['Custom']
        };

        function updateVersionOptions() {
            const typeSelect = document.getElementById('type');
            const versionSelect = document.getElementById('version');
            const selectedType = typeSelect.value;

            // Clear existing options
            versionSelect.innerHTML = '<option value="">Select Version</option>';

            if (selectedType && versionOptions[selectedType]) {
                versionOptions[selectedType].forEach(version => {
                    const option = document.createElement('option');
                    option.value = version.toLowerCase();
                    option.textContent = version;
                    versionSelect.appendChild(option);
                });
            }
        }

        // Initialize version options on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateVersionOptions();
            initializeTemplateCards();
        });

        // Template card functionality
        function initializeTemplateCards() {
            const templateCards = document.querySelectorAll('.template-card');
            templateCards.forEach(card => {
                card.addEventListener('click', function() {
                    const templateId = this.dataset.templateId;
                    const templateName = this.dataset.templateName;
                    openInstallModal(templateId, templateName);
                });
            });
        }

        function openInstallModal(templateId, templateName) {
            document.getElementById('templateId').value = templateId;
            document.getElementById('modalTitle').textContent = `Install ${templateName}`;
            document.getElementById('installModal').classList.remove('hidden');
        }

        function closeInstallModal() {
            document.getElementById('installModal').classList.add('hidden');
            document.getElementById('installForm').reset();
        }

        async function installApplication(event) {
            event.preventDefault();

            const installButton = document.getElementById('installButton');
            const originalText = installButton.textContent;

            // Show loading state
            installButton.textContent = 'Installing...';
            installButton.disabled = true;

            try {
                const formData = new FormData(event.target);

                const response = await fetch('{{ route("admin.applications.install-template") }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json',
                    },
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // Show success message
                    showNotification('success', result.message);

                    // Show credentials if available
                    if (result.credentials) {
                        showCredentials(result.credentials, result.admin_url);
                    }

                    // Close modal and redirect after delay
                    setTimeout(() => {
                        closeInstallModal();
                        window.location.href = '{{ route("admin.applications.index") }}';
                    }, 3000);
                } else {
                    showNotification('error', result.error || 'Installation failed');
                }
            } catch (error) {
                console.error('Installation error:', error);
                showNotification('error', 'Installation failed. Please try again.');
            } finally {
                // Reset button state
                installButton.textContent = originalText;
                installButton.disabled = false;
            }
        }

        function showNotification(type, message) {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        function showCredentials(credentials, adminUrl) {
            const credentialsDiv = document.createElement('div');
            credentialsDiv.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white p-6 rounded-lg shadow-xl z-50 border';
            credentialsDiv.innerHTML = `
                <h3 class="text-lg font-semibold mb-4">Installation Complete!</h3>
                <div class="space-y-2 mb-4">
                    <p><strong>Admin Username:</strong> ${credentials.admin_user}</p>
                    <p><strong>Admin Password:</strong> ${credentials.admin_pass}</p>
                    <p><strong>Admin Email:</strong> ${credentials.admin_email}</p>
                    ${adminUrl ? `<p><strong>Admin URL:</strong> <a href="${adminUrl}" target="_blank" class="text-blue-600 hover:underline">${adminUrl}</a></p>` : ''}
                </div>
                <p class="text-sm text-gray-600 mb-4">Please save these credentials in a secure location.</p>
                <button onclick="this.parentElement.remove()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    Close
                </button>
            `;

            document.body.appendChild(credentialsDiv);
        }
    </script>
</x-admin-layout>
