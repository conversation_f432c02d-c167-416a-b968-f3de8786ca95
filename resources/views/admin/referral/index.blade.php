<x-admin-layout>
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- <PERSON> Header -->
        <div class="sm:flex sm:items-center sm:justify-between mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Referral Program</h1>
                <p class="mt-2 text-sm text-gray-700">Manage referral tiers, commissions, and partner performance</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <button onclick="refreshData()" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                </button>
                <button onclick="showPayoutModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    Process Payouts
                </button>
            </div>
        </div>

        <!-- Referral Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Referrers</p>
                            <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_referrers']) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Conversion Rate</p>
                            <p class="text-2xl font-bold text-green-600">{{ $stats['conversion_rate'] }}%</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Commissions</p>
                            <p class="text-2xl font-bold text-purple-600">${{ number_format($stats['total_commissions_paid'], 2) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Pending Commissions</p>
                            <p class="text-2xl font-bold text-yellow-600">${{ number_format($stats['pending_commissions'], 2) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Referral Tiers -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Referral Tiers</h3>
                <p class="text-sm text-gray-500">Manage commission rates and tier benefits</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    @foreach($tiers as $tier)
                    <div class="border-2 rounded-lg p-6 {{ $tier['name'] === 'Platinum' ? 'border-purple-300 bg-purple-50' : 'border-gray-200' }} hover:shadow-lg transition-shadow duration-200">
                        <div class="text-center mb-4">
                            <div class="w-12 h-12 mx-auto mb-3 rounded-full flex items-center justify-center
                                {{ $tier['color'] === 'orange' ? 'bg-orange-100' : 
                                   ($tier['color'] === 'gray' ? 'bg-gray-100' : 
                                   ($tier['color'] === 'yellow' ? 'bg-yellow-100' : 'bg-purple-100')) }}">
                                <svg class="w-6 h-6 {{ $tier['color'] === 'orange' ? 'text-orange-600' : 
                                   ($tier['color'] === 'gray' ? 'text-gray-600' : 
                                   ($tier['color'] === 'yellow' ? 'text-yellow-600' : 'text-purple-600')) }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                            </div>
                            <h4 class="text-lg font-bold text-gray-900">{{ $tier['name'] }}</h4>
                            <p class="text-sm text-gray-500">{{ $tier['members_count'] }} members</p>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="text-center">
                                <span class="text-2xl font-bold text-blue-600">{{ $tier['commission_rate'] }}%</span>
                                <p class="text-xs text-gray-500">Commission Rate</p>
                            </div>
                            
                            <div class="text-center">
                                <span class="text-lg font-semibold text-green-600">${{ $tier['bonus_amount'] }}</span>
                                <p class="text-xs text-gray-500">Bonus at {{ $tier['bonus_threshold'] }} referrals</p>
                            </div>
                            
                            <div class="text-center">
                                <span class="text-sm font-medium">{{ $tier['min_referrals'] }}{{ $tier['max_referrals'] ? '-'.$tier['max_referrals'] : '+' }} referrals</span>
                            </div>
                        </div>
                        
                        <div class="space-y-2">
                            <h5 class="text-xs font-semibold text-gray-700">Benefits:</h5>
                            @foreach($tier['benefits'] as $benefit)
                            <div class="flex items-center text-xs text-gray-600">
                                <svg class="w-3 h-3 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                {{ $benefit }}
                            </div>
                            @endforeach
                        </div>
                        
                        <div class="mt-4">
                            <button onclick="editTier({{ $tier['id'] }})" class="w-full text-xs text-blue-600 hover:text-blue-800 font-medium">Edit Tier</button>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Top Referrers & Recent Referrals -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Top Referrers -->
            <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Top Referrers</h3>
                    <p class="text-sm text-gray-500">Best performing referral partners</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        @foreach($topReferrers as $index => $referrer)
                        <div class="flex items-center space-x-4 p-3 border border-gray-100 rounded-lg hover:bg-gray-50">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 rounded-full flex items-center justify-center font-bold text-white
                                    {{ $index === 0 ? 'bg-yellow-500' : ($index === 1 ? 'bg-gray-400' : ($index === 2 ? 'bg-orange-500' : 'bg-blue-500')) }}">
                                    {{ $index + 1 }}
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">{{ $referrer['name'] }}</p>
                                <p class="text-xs text-gray-500">{{ $referrer['email'] }}</p>
                                <div class="flex items-center space-x-4 mt-1">
                                    <span class="text-xs text-gray-600">{{ $referrer['total_referrals'] }} referrals</span>
                                    <span class="text-xs text-green-600">${{ number_format($referrer['monthly_earnings'], 2) }}/month</span>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    {{ $referrer['tier'] === 'Platinum' ? 'bg-purple-100 text-purple-800' : 
                                       ($referrer['tier'] === 'Gold' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                                    {{ $referrer['tier'] }}
                                </span>
                                <p class="text-xs text-gray-500 mt-1">{{ $referrer['conversion_rate'] }}% conversion</p>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Recent Referrals -->
            <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Recent Referrals</h3>
                    <p class="text-sm text-gray-500">Latest referral activities</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        @foreach($recentReferrals as $referral)
                        <div class="flex items-start space-x-3 p-3 border border-gray-100 rounded-lg">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center
                                    {{ $referral['status'] === 'converted' ? 'bg-green-100' : 
                                       ($referral['status'] === 'pending' ? 'bg-yellow-100' : 'bg-red-100') }}">
                                    @if($referral['status'] === 'converted')
                                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    @elseif($referral['status'] === 'pending')
                                        <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    @else
                                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    @endif
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">{{ $referral['referrer_name'] }}</p>
                                <p class="text-xs text-gray-500">{{ $referral['referred_email'] }}</p>
                                <div class="flex items-center justify-between mt-1">
                                    <span class="text-xs text-gray-400">{{ $referral['created_at']->diffForHumans() }}</span>
                                    @if($referral['status'] === 'converted')
                                        <span class="text-xs font-medium text-green-600">${{ $referral['commission_amount'] }}</span>
                                    @else
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            {{ $referral['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' }}">
                                            {{ ucfirst($referral['status']) }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Commission Payouts -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Commission Payouts</h3>
                <p class="text-sm text-gray-500">Manage referral commission payments</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Referrer</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($payouts as $payout)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $payout['referrer_name'] }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${{ number_format($payout['amount'], 2) }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $payout['period'] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $payout['payment_method'] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    {{ $payout['status'] === 'paid' ? 'bg-green-100 text-green-800' : 
                                       ($payout['status'] === 'processing' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800') }}">
                                    {{ ucfirst($payout['status']) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    @if($payout['status'] === 'pending')
                                        <button onclick="processPayout({{ $payout['id'] }})" class="text-green-600 hover:text-green-900">Process</button>
                                    @endif
                                    <button class="text-blue-600 hover:text-blue-900">View</button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Process Payouts Modal -->
    <div id="payoutModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
            <div class="relative bg-white rounded-lg max-w-lg w-full">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Process Commission Payouts</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex">
                                <svg class="w-5 h-5 text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <h4 class="text-sm font-medium text-blue-800">Pending Payouts Summary</h4>
                                    <p class="text-sm text-blue-700 mt-1">Total pending: ${{ number_format($stats['pending_commissions'], 2) }}</p>
                                    <p class="text-xs text-blue-600 mt-1">{{ count(array_filter($payouts, fn($p) => $p['status'] === 'pending')) }} referrers awaiting payment</p>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">Payment Method</label>
                            <select id="paymentMethod" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <option value="bulk">Process All Pending</option>
                                <option value="paypal">PayPal Only</option>
                                <option value="bank">Bank Transfer Only</option>
                                <option value="stripe">Stripe Only</option>
                            </select>
                        </div>

                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" id="sendNotifications" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Send email notifications to referrers</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button onclick="hidePayoutModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">Cancel</button>
                    <button onclick="processAllPayouts()" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">Process Payouts</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Toast -->
    <div id="notification" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
            <div class="flex items-center">
                <div id="notification-icon" class="flex-shrink-0 mr-3">
                    <!-- Icon will be inserted here -->
                </div>
                <div>
                    <p id="notification-title" class="text-sm font-medium text-gray-900"></p>
                    <p id="notification-message" class="text-sm text-gray-500"></p>
                </div>
                <button onclick="hideNotification()" class="ml-4 text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        function showNotification(type, title, message) {
            const notification = document.getElementById('notification');
            const icon = document.getElementById('notification-icon');
            const titleEl = document.getElementById('notification-title');
            const messageEl = document.getElementById('notification-message');

            titleEl.textContent = title;
            messageEl.textContent = message;

            if (type === 'success') {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                `;
            } else {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                `;
            }

            notification.classList.remove('hidden');
            setTimeout(() => {
                hideNotification();
            }, 5000);
        }

        function hideNotification() {
            document.getElementById('notification').classList.add('hidden');
        }

        function showPayoutModal() {
            document.getElementById('payoutModal').classList.remove('hidden');
        }

        function hidePayoutModal() {
            document.getElementById('payoutModal').classList.add('hidden');
        }

        function processAllPayouts() {
            const paymentMethod = document.getElementById('paymentMethod').value;
            const sendNotifications = document.getElementById('sendNotifications').checked;

            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Processing...';
            button.disabled = true;

            // Simulate processing multiple payouts
            setTimeout(() => {
                showNotification('success', 'Payouts Processed', `All pending payouts have been processed via ${paymentMethod}`);
                hidePayoutModal();
                setTimeout(() => location.reload(), 2000);
            }, 2000);

            setTimeout(() => {
                button.textContent = originalText;
                button.disabled = false;
            }, 2000);
        }

        function processPayout(payoutId) {
            if (!confirm('Are you sure you want to process this payout?')) return;

            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Processing...';
            button.disabled = true;

            fetch('/admin/referral/process-payout', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ payout_id: payoutId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Payout Processed', data.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('error', 'Processing Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Processing Failed', 'Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
        }

        function editTier(tierId) {
            showNotification('success', 'Edit Tier', `Opening tier ${tierId} configuration`);
            // Simulate opening tier editor
        }

        function refreshData() {
            const refreshButton = document.querySelector('button[onclick="refreshData()"]');
            const originalText = refreshButton.innerHTML;

            refreshButton.innerHTML = `
                <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refreshing...
            `;

            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // Auto-refresh every 5 minutes for referral tracking
        setInterval(() => {
            fetch('/admin/referral/real-time-data')
                .then(response => response.json())
                .then(data => {
                    console.log('Referral data refreshed:', data.timestamp);
                })
                .catch(error => console.error('Auto-refresh failed:', error));
        }, 300000);

        // Add smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Close modal when clicking outside
            document.getElementById('payoutModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    hidePayoutModal();
                }
            });

            // Add special effects for Platinum tier
            const platinumTier = document.querySelector('.border-purple-300');
            if (platinumTier) {
                setInterval(() => {
                    platinumTier.style.boxShadow = '0 0 20px rgba(147, 51, 234, 0.3)';
                    setTimeout(() => {
                        platinumTier.style.boxShadow = '';
                    }, 1000);
                }, 5000);
            }

            // Add hover effects to tier cards
            const tierCards = document.querySelectorAll('.border-2.rounded-lg.p-6');
            tierCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.transition = 'transform 0.3s ease';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Animate ranking numbers
            const rankingNumbers = document.querySelectorAll('.w-10.h-10.rounded-full');
            rankingNumbers.forEach((number, index) => {
                setTimeout(() => {
                    number.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        number.style.transform = 'scale(1)';
                    }, 200);
                }, index * 200);
            });
        });

        // Referral-specific features
        function showReferralAlert() {
            showNotification('success', 'New Referral', 'A new referral has been registered!');
        }

        // Simulate new referral notifications
        setInterval(() => {
            if (Math.random() > 0.85) { // 15% chance
                showReferralAlert();
            }
        }, 240000); // Every 4 minutes
    </script>
</x-admin-layout>
