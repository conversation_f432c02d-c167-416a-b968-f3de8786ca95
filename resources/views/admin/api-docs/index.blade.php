<x-admin-layout>
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- <PERSON> Header -->
        <div class="sm:flex sm:items-center sm:justify-between mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">API Documentation</h1>
                <p class="mt-2 text-sm text-gray-700">Manage API endpoints, versions, and developer resources</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <button onclick="refreshData()" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                </button>
                <button onclick="showGenerateDocsModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Generate Docs
                </button>
            </div>
        </div>

        <!-- API Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Endpoints</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $stats['total_endpoints'] }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Daily Requests</p>
                            <p class="text-2xl font-bold text-green-600">{{ number_format($stats['daily_requests']) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Developers</p>
                            <p class="text-2xl font-bold text-purple-600">{{ $stats['registered_developers'] }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Avg Response</p>
                            <p class="text-2xl font-bold text-yellow-600">{{ $stats['average_response_time'] }}ms</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Versions -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">API Versions</h3>
                <p class="text-sm text-gray-500">Manage different API versions and their lifecycle</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    @foreach($versions as $version)
                    <div class="border border-gray-200 rounded-lg p-6 {{ $version['status'] === 'current' ? 'border-blue-300 bg-blue-50' : ($version['status'] === 'deprecated' ? 'border-red-300 bg-red-50' : '') }}">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-semibold text-gray-900">API {{ $version['version'] }}</h4>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                {{ $version['status'] === 'current' ? 'bg-blue-100 text-blue-800' : 
                                   ($version['status'] === 'supported' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800') }}">
                                {{ ucfirst($version['status']) }}
                            </span>
                        </div>
                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-500">Endpoints:</span>
                                <span class="font-medium">{{ $version['endpoints_count'] }}</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-500">Usage:</span>
                                <span class="font-medium">{{ $version['usage_percentage'] }}%</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-500">Released:</span>
                                <span class="font-medium">{{ $version['release_date']->format('M Y') }}</span>
                            </div>
                        </div>
                        <div class="mb-4">
                            <h5 class="text-xs font-semibold text-gray-700 mb-2">Features:</h5>
                            <div class="flex flex-wrap gap-1">
                                @foreach($version['features'] as $feature)
                                <span class="inline-flex px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-700">{{ $feature }}</span>
                                @endforeach
                            </div>
                        </div>
                        <p class="text-xs text-gray-600">{{ $version['changelog'] }}</p>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- API Endpoints & Analytics -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- API Endpoints -->
            <div class="lg:col-span-2">
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">API Endpoints</h3>
                        <p class="text-sm text-gray-500">Manage API endpoint groups and their status</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            @foreach($endpoints as $endpoint)
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="text-sm font-semibold text-gray-900">{{ $endpoint['name'] }}</h4>
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            {{ $endpoint['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ ucfirst($endpoint['status']) }}
                                        </span>
                                        <button onclick="updateEndpointStatus({{ $endpoint['id'] }}, '{{ $endpoint['status'] === 'active' ? 'deprecated' : 'active' }}')" 
                                                class="text-blue-600 hover:text-blue-900 text-xs">
                                            {{ $endpoint['status'] === 'active' ? 'Deprecate' : 'Activate' }}
                                        </button>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-600 mb-3">{{ $endpoint['description'] }}</p>
                                <div class="flex items-center justify-between text-xs text-gray-500">
                                    <div class="flex items-center space-x-4">
                                        <span>{{ $endpoint['endpoints_count'] }} endpoints</span>
                                        <span>{{ $endpoint['base_path'] }}</span>
                                    </div>
                                    <span>{{ number_format($endpoint['usage_count']) }} requests</span>
                                </div>
                                <div class="mt-2 flex space-x-1">
                                    @foreach($endpoint['methods'] as $method)
                                    <span class="inline-flex px-2 py-1 text-xs font-mono rounded 
                                        {{ $method === 'GET' ? 'bg-blue-100 text-blue-800' : 
                                           ($method === 'POST' ? 'bg-green-100 text-green-800' : 
                                           ($method === 'PUT' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800')) }}">
                                        {{ $method }}
                                    </span>
                                    @endforeach
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Analytics -->
            <div>
                <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">API Analytics</h3>
                        <p class="text-sm text-gray-500">Request patterns and performance</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-semibold text-gray-900 mb-2">Daily Requests</h4>
                                <div class="space-y-2">
                                    @foreach($analytics['daily_requests'] as $day => $requests)
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-600">{{ $day }}</span>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-16 bg-gray-200 rounded-full h-1">
                                                <div class="bg-blue-600 h-1 rounded-full" style="width: {{ ($requests / 50000) * 100 }}%"></div>
                                            </div>
                                            <span class="text-xs font-medium">{{ number_format($requests) }}</span>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-semibold text-gray-900 mb-2">Response Times</h4>
                                <div class="space-y-1">
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-600">Average:</span>
                                        <span class="font-medium">{{ $analytics['response_times']['average'] }}ms</span>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-600">95th percentile:</span>
                                        <span class="font-medium">{{ $analytics['response_times']['p95'] }}ms</span>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-600">99th percentile:</span>
                                        <span class="font-medium">{{ $analytics['response_times']['p99'] }}ms</span>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h4 class="text-sm font-semibold text-gray-900 mb-2">Error Rates</h4>
                                <div class="space-y-1">
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-600">Success (2xx):</span>
                                        <span class="font-medium text-green-600">{{ $analytics['error_rates']['2xx'] }}%</span>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-600">Client Error (4xx):</span>
                                        <span class="font-medium text-yellow-600">{{ $analytics['error_rates']['4xx'] }}%</span>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-600">Server Error (5xx):</span>
                                        <span class="font-medium text-red-600">{{ $analytics['error_rates']['5xx'] }}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Recent Activities</h3>
                        <p class="text-sm text-gray-500">Latest API changes</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            @foreach($recentActivities as $activity)
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-6 h-6 {{ $activity['type'] === 'endpoint_added' ? 'bg-green-100' : ($activity['type'] === 'endpoint_deprecated' ? 'bg-red-100' : 'bg-blue-100') }} rounded-full flex items-center justify-center">
                                        @if($activity['type'] === 'endpoint_added')
                                            <svg class="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                            </svg>
                                        @elseif($activity['type'] === 'endpoint_deprecated')
                                            <svg class="w-3 h-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        @else
                                            <svg class="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        @endif
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-xs font-medium text-gray-900">{{ $activity['description'] }}</p>
                                    <p class="text-xs text-gray-500">{{ $activity['user'] }} • {{ $activity['timestamp']->diffForHumans() }}</p>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Developer Tools -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Developer Tools</h3>
                <p class="text-sm text-gray-500">Tools and resources for API developers</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    @foreach($developerTools as $tool)
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200 cursor-pointer">
                        <div class="flex items-center mb-3">
                            <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                </svg>
                            </div>
                            <h4 class="text-sm font-semibold text-gray-900">{{ $tool['name'] }}</h4>
                        </div>
                        <p class="text-xs text-gray-600 mb-3">{{ $tool['description'] }}</p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">{{ number_format($tool['usage_count']) }} uses</span>
                            <a href="{{ $tool['url'] }}" class="text-xs text-blue-600 hover:text-blue-800">Open Tool</a>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Generate Documentation Modal -->
    <div id="generateDocsModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
            <div class="relative bg-white rounded-lg max-w-lg w-full">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Generate API Documentation</h3>
                </div>
                <form id="generateDocsForm" class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">API Version</label>
                        <select name="version" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            @foreach($versions as $version)
                            <option value="{{ $version['version'] }}">API {{ $version['version'] }} ({{ ucfirst($version['status']) }})</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Output Format</label>
                        <select name="format" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="html">HTML</option>
                            <option value="pdf">PDF</option>
                            <option value="json">OpenAPI JSON</option>
                            <option value="yaml">OpenAPI YAML</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Include</label>
                        <div class="mt-2 space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="include_examples" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Code Examples</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="include_schemas" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Response Schemas</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="include_auth" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Authentication Guide</span>
                            </label>
                        </div>
                    </div>
                </form>
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button onclick="hideGenerateDocsModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">Cancel</button>
                    <button onclick="generateDocs()" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">Generate</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Toast -->
    <div id="notification" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
            <div class="flex items-center">
                <div id="notification-icon" class="flex-shrink-0 mr-3">
                    <!-- Icon will be inserted here -->
                </div>
                <div>
                    <p id="notification-title" class="text-sm font-medium text-gray-900"></p>
                    <p id="notification-message" class="text-sm text-gray-500"></p>
                </div>
                <button onclick="hideNotification()" class="ml-4 text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        function showNotification(type, title, message) {
            const notification = document.getElementById('notification');
            const icon = document.getElementById('notification-icon');
            const titleEl = document.getElementById('notification-title');
            const messageEl = document.getElementById('notification-message');

            titleEl.textContent = title;
            messageEl.textContent = message;

            if (type === 'success') {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                `;
            } else {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                `;
            }

            notification.classList.remove('hidden');
            setTimeout(() => {
                hideNotification();
            }, 5000);
        }

        function hideNotification() {
            document.getElementById('notification').classList.add('hidden');
        }

        function showGenerateDocsModal() {
            document.getElementById('generateDocsModal').classList.remove('hidden');
        }

        function hideGenerateDocsModal() {
            document.getElementById('generateDocsModal').classList.add('hidden');
            document.getElementById('generateDocsForm').reset();
        }

        function generateDocs() {
            const form = document.getElementById('generateDocsForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Generating...';
            button.disabled = true;

            fetch('/admin/api-docs/generate', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Documentation Generated', data.message);
                    hideGenerateDocsModal();
                    if (data.download_url) {
                        setTimeout(() => {
                            window.open(data.download_url, '_blank');
                        }, 1000);
                    }
                } else {
                    showNotification('error', 'Generation Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Generation Failed', 'Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
        }

        function updateEndpointStatus(endpointId, status) {
            if (!confirm(`Are you sure you want to ${status} this endpoint group?`)) return;

            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Updating...';
            button.disabled = true;

            fetch('/admin/api-docs/update-endpoint-status', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ endpoint_id: endpointId, status: status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Status Updated', data.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('error', 'Update Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Update Failed', 'Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
        }

        function refreshData() {
            const refreshButton = document.querySelector('button[onclick="refreshData()"]');
            const originalText = refreshButton.innerHTML;

            refreshButton.innerHTML = `
                <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refreshing...
            `;

            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // Auto-refresh every 2 minutes for API monitoring
        setInterval(() => {
            fetch('/admin/api-docs/real-time-data')
                .then(response => response.json())
                .then(data => {
                    console.log('API data refreshed:', data.timestamp);
                    // Update specific elements here instead of full page reload
                })
                .catch(error => console.error('Auto-refresh failed:', error));
        }, 120000);

        // Add smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Close modal when clicking outside
            document.getElementById('generateDocsModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    hideGenerateDocsModal();
                }
            });

            // Add special effects for current API version
            const currentVersionCard = document.querySelector('.border-blue-300');
            if (currentVersionCard) {
                setInterval(() => {
                    currentVersionCard.style.boxShadow = '0 0 15px rgba(59, 130, 246, 0.3)';
                    setTimeout(() => {
                        currentVersionCard.style.boxShadow = '';
                    }, 1000);
                }, 4000);
            }

            // Animate progress bars
            setTimeout(() => {
                const progressBars = document.querySelectorAll('.bg-blue-600');
                progressBars.forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    bar.style.transition = 'width 1s ease-in-out';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 100);
                });
            }, 500);

            // Add hover effects to endpoint cards
            const endpointCards = document.querySelectorAll('.border-gray-200.rounded-lg.p-4');
            endpointCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'transform 0.2s ease';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });

        // API-specific features
        function showApiExplorer() {
            showNotification('success', 'API Explorer', 'Opening interactive API testing interface');
            setTimeout(() => {
                window.open('/api/explorer', '_blank');
            }, 1000);
        }

        // Simulate real-time API monitoring
        setInterval(() => {
            if (Math.random() > 0.8) { // 20% chance
                const endpoints = ['Authentication', 'Hosting Management', 'Domain Management'];
                const randomEndpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
                showNotification('success', 'API Activity', `High activity detected on ${randomEndpoint} endpoints`);
            }
        }, 180000); // Every 3 minutes
    </script>
</x-admin-layout>
