<x-admin-layout>
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- <PERSON> Header -->
        <div class="sm:flex sm:items-center sm:justify-between mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Premium Care Management</h1>
                <p class="mt-2 text-sm text-gray-700">Manage premium support plans, clients, and high-priority services</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <button onclick="refreshData()" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                </button>
                <button class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Add Premium Client
                </button>
            </div>
        </div>

        <!-- Premium Care Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Premium Clients</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $stats['total_premium_clients'] }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Monthly Revenue</p>
                            <p class="text-2xl font-bold text-green-600">${{ number_format($stats['monthly_premium_revenue'], 2) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Avg Response Time</p>
                            <p class="text-2xl font-bold text-blue-600">{{ $stats['average_response_time'] }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Satisfaction Rate</p>
                            <p class="text-2xl font-bold text-yellow-600">{{ $stats['premium_satisfaction_rate'] }}%</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Premium Care Plans -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Premium Care Plans</h3>
                <p class="text-sm text-gray-500">Available premium support plans and their performance</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    @foreach($plans as $plan)
                    <div class="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-200 {{ $plan['name'] === 'VIP Concierge' ? 'border-purple-300 bg-purple-50' : '' }}">
                        <div class="text-center">
                            <h4 class="text-lg font-semibold text-gray-900">{{ $plan['name'] }}</h4>
                            <div class="mt-2">
                                <span class="text-3xl font-bold text-blue-600">${{ $plan['price'] }}</span>
                                <span class="text-gray-500">/{{ $plan['billing_cycle'] }}</span>
                            </div>
                            <div class="mt-2">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    {{ $plan['subscribers'] }} subscribers
                                </span>
                            </div>
                        </div>
                        <div class="mt-4 space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-500">Response Time:</span>
                                <span class="font-medium">{{ $plan['response_time'] }}</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-500">Satisfaction:</span>
                                <span class="font-medium text-green-600">{{ $plan['satisfaction_rate'] }}%</span>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h5 class="text-xs font-semibold text-gray-700 mb-2">Features:</h5>
                            <ul class="text-xs text-gray-600 space-y-1">
                                @foreach(array_slice($plan['features'], 0, 3) as $feature)
                                <li class="flex items-center">
                                    <svg class="w-3 h-3 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    {{ $feature }}
                                </li>
                                @endforeach
                                @if(count($plan['features']) > 3)
                                <li class="text-blue-600">+{{ count($plan['features']) - 3 }} more features</li>
                                @endif
                            </ul>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Support Tickets & Premium Clients -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- Support Tickets -->
            <div class="lg:col-span-2">
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Premium Support Tickets</h3>
                        <p class="text-sm text-gray-500">High-priority tickets from premium clients</p>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticket</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agent</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($supportTickets as $ticket)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $ticket['id'] }}</div>
                                            <div class="text-sm text-gray-500">{{ $ticket['subject'] }}</div>
                                            <div class="text-xs text-gray-400">{{ $ticket['created_at']->diffForHumans() }}</div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $ticket['client'] }}</div>
                                        <div class="text-xs text-gray-500">{{ $ticket['plan'] }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            {{ $ticket['priority'] === 'high' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800' }}">
                                            {{ ucfirst($ticket['priority']) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            {{ $ticket['status'] === 'resolved' ? 'bg-green-100 text-green-800' : 
                                               ($ticket['status'] === 'in_progress' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800') }}">
                                            {{ ucfirst(str_replace('_', ' ', $ticket['status'])) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $ticket['assigned_to'] }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2">
                                            <button class="text-blue-600 hover:text-blue-900">View</button>
                                            @if($ticket['status'] !== 'resolved')
                                                <button onclick="assignTicket('{{ $ticket['id'] }}')" class="text-green-600 hover:text-green-900">Assign</button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div>
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Recent Activities</h3>
                        <p class="text-sm text-gray-500">Latest premium care activities</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            @foreach($recentActivities as $activity)
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 {{ $activity['type'] === 'ticket_resolved' ? 'bg-green-100' : ($activity['type'] === 'client_upgraded' ? 'bg-blue-100' : 'bg-purple-100') }} rounded-full flex items-center justify-center">
                                        @if($activity['type'] === 'ticket_resolved')
                                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        @elseif($activity['type'] === 'client_upgraded')
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                            </svg>
                                        @else
                                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
                                            </svg>
                                        @endif
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900">{{ $activity['description'] }}</p>
                                    <p class="text-xs text-gray-500">{{ $activity['client'] }} • {{ $activity['agent'] }}</p>
                                    <p class="text-xs text-gray-400">{{ $activity['timestamp']->diffForHumans() }}</p>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Premium Clients Table -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Premium Clients</h3>
                <p class="text-sm text-gray-500">Manage premium clients and their support plans</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Satisfaction</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tickets</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Monthly Spend</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($premiumClients as $client)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-sm font-medium text-purple-600">{{ substr($client['name'], 0, 2) }}</span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $client['name'] }}</div>
                                        <div class="text-sm text-gray-500">{{ $client['email'] }}</div>
                                        <div class="text-xs text-gray-400">Joined {{ $client['joined_date']->diffForHumans() }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    {{ $client['plan'] === 'VIP Concierge' ? 'bg-purple-100 text-purple-800' : 
                                       ($client['plan'] === 'Enterprise Care' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800') }}">
                                    {{ $client['plan'] }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900">{{ $client['satisfaction_score'] }}/10</div>
                                    <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-600 h-2 rounded-full" style="width: {{ $client['satisfaction_score'] * 10 }}%"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $client['resolved_tickets'] }}/{{ $client['total_tickets'] }}</div>
                                <div class="text-xs text-gray-500">{{ round(($client['resolved_tickets'] / $client['total_tickets']) * 100) }}% resolved</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${{ number_format($client['monthly_spend'], 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900">View</button>
                                    <button onclick="upgradeClient({{ $client['id'] }})" class="text-green-600 hover:text-green-900">Upgrade</button>
                                    <button class="text-purple-600 hover:text-purple-900">Contact</button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Notification Toast -->
    <div id="notification" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
            <div class="flex items-center">
                <div id="notification-icon" class="flex-shrink-0 mr-3">
                    <!-- Icon will be inserted here -->
                </div>
                <div>
                    <p id="notification-title" class="text-sm font-medium text-gray-900"></p>
                    <p id="notification-message" class="text-sm text-gray-500"></p>
                </div>
                <button onclick="hideNotification()" class="ml-4 text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        function showNotification(type, title, message) {
            const notification = document.getElementById('notification');
            const icon = document.getElementById('notification-icon');
            const titleEl = document.getElementById('notification-title');
            const messageEl = document.getElementById('notification-message');

            titleEl.textContent = title;
            messageEl.textContent = message;

            if (type === 'success') {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                `;
            } else {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                `;
            }

            notification.classList.remove('hidden');
            setTimeout(() => {
                hideNotification();
            }, 5000);
        }

        function hideNotification() {
            document.getElementById('notification').classList.add('hidden');
        }

        function assignTicket(ticketId) {
            // Show agent selection (simplified for demo)
            const agents = ['Sarah Johnson', 'Michael Chen', 'David Rodriguez', 'Emily Watson'];
            const selectedAgent = prompt('Select agent:\n' + agents.map((agent, index) => `${index + 1}. ${agent}`).join('\n'));

            if (!selectedAgent || selectedAgent < 1 || selectedAgent > agents.length) return;

            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Assigning...';
            button.disabled = true;

            fetch('/admin/premium-care/assign-ticket', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ticket_id: ticketId,
                    agent_id: selectedAgent
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Ticket Assigned', data.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('error', 'Assignment Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Assignment Failed', 'Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
        }

        function upgradeClient(clientId) {
            const plans = ['Premium Support', 'Premium Plus', 'Enterprise Care', 'VIP Concierge'];
            const selectedPlan = prompt('Select new plan:\n' + plans.map((plan, index) => `${index + 1}. ${plan}`).join('\n'));

            if (!selectedPlan || selectedPlan < 1 || selectedPlan > plans.length) return;

            if (!confirm(`Are you sure you want to upgrade this client to ${plans[selectedPlan - 1]}?`)) return;

            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Upgrading...';
            button.disabled = true;

            fetch('/admin/premium-care/upgrade-client', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    client_id: clientId,
                    new_plan: plans[selectedPlan - 1]
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Client Upgraded', data.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('error', 'Upgrade Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Upgrade Failed', 'Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
        }

        function refreshData() {
            const refreshButton = document.querySelector('button[onclick="refreshData()"]');
            const originalText = refreshButton.innerHTML;

            refreshButton.innerHTML = `
                <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refreshing...
            `;

            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // Auto-refresh every 30 seconds for premium care
        setInterval(() => {
            fetch('/admin/premium-care/real-time-data')
                .then(response => response.json())
                .then(data => {
                    console.log('Premium care data refreshed:', data.timestamp);
                    // Update specific elements here instead of full page reload
                })
                .catch(error => console.error('Auto-refresh failed:', error));
        }, 30000);

        // Add smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Add premium glow effect to VIP plan
            const vipPlan = document.querySelector('.border-purple-300');
            if (vipPlan) {
                setInterval(() => {
                    vipPlan.style.boxShadow = '0 0 20px rgba(147, 51, 234, 0.3)';
                    setTimeout(() => {
                        vipPlan.style.boxShadow = '';
                    }, 1000);
                }, 3000);
            }

            // Animate satisfaction bars
            setTimeout(() => {
                const satisfactionBars = document.querySelectorAll('.bg-green-600');
                satisfactionBars.forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    bar.style.transition = 'width 1s ease-in-out';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 100);
                });
            }, 500);

            // Add hover effects to premium plan cards
            const planCards = document.querySelectorAll('.border-gray-200.rounded-lg.p-6');
            planCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.transition = 'transform 0.3s ease';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });

        // Premium care specific features
        function showPremiumAlert() {
            showNotification('success', 'Premium Alert', 'High-priority ticket requires immediate attention');
        }

        // Simulate premium alerts every 2 minutes
        setInterval(() => {
            if (Math.random() > 0.7) { // 30% chance
                showPremiumAlert();
            }
        }, 120000);
    </script>
</x-admin-layout>
