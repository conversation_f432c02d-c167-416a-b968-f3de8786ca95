<x-admin-layout>
    <x-slot name="title">Edit Server - {{ $server->name }}</x-slot>
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <svg class="w-8 h-8 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    <span>Edit Server</span>
                </h1>
                <p class="text-gray-600 mt-1">Update server configuration and settings</p>
            </div>
            <div class="flex items-center space-x-4">
                <a href="{{ route('admin.servers.show', $server) }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    <span>View Details</span>
                </a>
                <a href="{{ route('admin.servers.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    <span>Back to Servers</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <form method="POST" action="{{ route('admin.servers.update', $server) }}" class="space-y-8">
        @csrf
        @method('PUT')

        <!-- Basic Information -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Basic Information</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Server Name *</label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        value="{{ old('name', $server->name) }}"
                        required
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror"
                        placeholder="e.g., Web Server 01"
                    >
                    @error('name')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="hostname" class="block text-sm font-medium text-gray-700 mb-2">Hostname *</label>
                    <input
                        type="text"
                        id="hostname"
                        name="hostname"
                        value="{{ old('hostname', $server->hostname) }}"
                        required
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('hostname') border-red-500 @enderror"
                        placeholder="e.g., web01.example.com"
                    >
                    @error('hostname')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="ip_address" class="block text-sm font-medium text-gray-700 mb-2">IP Address *</label>
                    <input
                        type="text"
                        id="ip_address"
                        name="ip_address"
                        value="{{ old('ip_address', $server->ip_address) }}"
                        required
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('ip_address') border-red-500 @enderror"
                        placeholder="e.g., *************"
                    >
                    @error('ip_address')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="port" class="block text-sm font-medium text-gray-700 mb-2">SSH Port *</label>
                    <input
                        type="number"
                        id="port"
                        name="port"
                        value="{{ old('port', $server->port) }}"
                        required
                        min="1"
                        max="65535"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('port') border-red-500 @enderror"
                    >
                    @error('port')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Server Type *</label>
                    <select
                        id="type"
                        name="type"
                        required
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('type') border-red-500 @enderror"
                    >
                        <option value="">Select Type</option>
                        <option value="web" {{ old('type', $server->type) === 'web' ? 'selected' : '' }}>Web Server</option>
                        <option value="database" {{ old('type', $server->type) === 'database' ? 'selected' : '' }}>Database Server</option>
                        <option value="mail" {{ old('type', $server->type) === 'mail' ? 'selected' : '' }}>Mail Server</option>
                        <option value="dns" {{ old('type', $server->type) === 'dns' ? 'selected' : '' }}>DNS Server</option>
                        <option value="load_balancer" {{ old('type', $server->type) === 'load_balancer' ? 'selected' : '' }}>Load Balancer</option>
                        <option value="storage" {{ old('type', $server->type) === 'storage' ? 'selected' : '' }}>Storage Server</option>
                    </select>
                    @error('type')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="operating_system" class="block text-sm font-medium text-gray-700 mb-2">Operating System</label>
                    <input
                        type="text"
                        id="operating_system"
                        name="operating_system"
                        value="{{ old('operating_system', $server->operating_system) }}"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., Ubuntu 22.04 LTS"
                    >
                </div>
            </div>
        </div>

        <!-- Authentication -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Authentication</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Username *</label>
                    <input
                        type="text"
                        id="username"
                        name="username"
                        value="{{ old('username', $server->username) }}"
                        required
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('username') border-red-500 @enderror"
                        placeholder="e.g., root"
                    >
                    @error('username')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="control_panel" class="block text-sm font-medium text-gray-700 mb-2">Control Panel</label>
                    <input
                        type="text"
                        id="control_panel"
                        name="control_panel"
                        value="{{ old('control_panel', $server->control_panel) }}"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., cPanel, Plesk, DirectAdmin"
                    >
                </div>

                <div class="md:col-span-2">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Leave empty to keep current password"
                    >
                    <p class="text-sm text-gray-500 mt-1">Leave empty to keep current password</p>
                </div>

                <div class="md:col-span-2">
                    <label for="ssh_key" class="block text-sm font-medium text-gray-700 mb-2">SSH Private Key</label>
                    <textarea
                        id="ssh_key"
                        name="ssh_key"
                        rows="6"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="-----BEGIN PRIVATE KEY-----"
                    >{{ old('ssh_key', $server->ssh_key ? decrypt($server->ssh_key) : '') }}</textarea>
                    <p class="text-sm text-gray-500 mt-1">Update SSH private key (optional)</p>
                </div>
            </div>
        </div>

        <!-- Specifications -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Server Specifications</h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="specifications_cpu" class="block text-sm font-medium text-gray-700 mb-2">CPU</label>
                    <input
                        type="text"
                        id="specifications_cpu"
                        name="specifications[cpu]"
                        value="{{ old('specifications.cpu', $server->specifications['cpu'] ?? '') }}"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., 4 vCPU"
                    >
                </div>

                <div>
                    <label for="specifications_ram" class="block text-sm font-medium text-gray-700 mb-2">RAM</label>
                    <input
                        type="text"
                        id="specifications_ram"
                        name="specifications[ram]"
                        value="{{ old('specifications.ram', $server->specifications['ram'] ?? '') }}"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., 8 GB"
                    >
                </div>

                <div>
                    <label for="specifications_storage" class="block text-sm font-medium text-gray-700 mb-2">Storage</label>
                    <input
                        type="text"
                        id="specifications_storage"
                        name="specifications[storage]"
                        value="{{ old('specifications.storage', $server->specifications['storage'] ?? '') }}"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., 100 GB SSD"
                    >
                </div>
            </div>
        </div>

        <!-- Notes -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Additional Notes</h3>

            <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                <textarea
                    id="notes"
                    name="notes"
                    rows="4"
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Any additional notes about this server..."
                >{{ old('notes', $server->notes) }}</textarea>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="flex items-center justify-end space-x-4">
            <a href="{{ route('admin.servers.show', $server) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                Cancel
            </a>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Update Server</span>
            </button>
        </div>
    </form>
</div>
</x-admin-layout>
