<x-admin-layout>
    <x-slot name="title">Add New Server</x-slot>
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <svg class="w-8 h-8 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <span>Add New Server</span>
                </h1>
                <p class="text-gray-600 mt-1">Configure a new server for your hosting infrastructure</p>
            </div>
            <a href="{{ route('admin.servers.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg flex items-center space-x-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <span>Back to Servers</span>
            </a>
        </div>
    </div>

    <!-- Form -->
    <form method="POST" action="{{ route('admin.servers.store') }}" class="space-y-8" id="server-form">
        @csrf

        <!-- Connection Test Alert -->
        <div id="connection-alert" class="hidden bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Connection Test</h3>
                    <div class="mt-2 text-sm text-blue-700" id="connection-message">
                        Testing connection to server...
                    </div>
                </div>
            </div>
        </div>

        <!-- Basic Information -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">Basic Information</h3>
                <button type="button" id="test-connection" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm flex items-center space-x-2 disabled:opacity-50" disabled>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
                    </svg>
                    <span>Test Connection</span>
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Server Name *</label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        value="{{ old('name') }}"
                        required
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror"
                        placeholder="e.g., Web Server 01"
                    >
                    @error('name')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="hostname" class="block text-sm font-medium text-gray-700 mb-2">Hostname *</label>
                    <input
                        type="text"
                        id="hostname"
                        name="hostname"
                        value="{{ old('hostname') }}"
                        required
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('hostname') border-red-500 @enderror"
                        placeholder="e.g., web01.example.com"
                    >
                    @error('hostname')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="ip_address" class="block text-sm font-medium text-gray-700 mb-2">IP Address *</label>
                    <input
                        type="text"
                        id="ip_address"
                        name="ip_address"
                        value="{{ old('ip_address') }}"
                        required
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('ip_address') border-red-500 @enderror"
                        placeholder="e.g., *************"
                    >
                    @error('ip_address')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="port" class="block text-sm font-medium text-gray-700 mb-2">SSH Port *</label>
                    <input
                        type="number"
                        id="port"
                        name="port"
                        value="{{ old('port', 22) }}"
                        required
                        min="1"
                        max="65535"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('port') border-red-500 @enderror"
                    >
                    @error('port')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Server Type *</label>
                    <select
                        id="type"
                        name="type"
                        required
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('type') border-red-500 @enderror"
                    >
                        <option value="">Select Type</option>
                        <option value="web" {{ old('type') === 'web' ? 'selected' : '' }}>Web Server</option>
                        <option value="database" {{ old('type') === 'database' ? 'selected' : '' }}>Database Server</option>
                        <option value="mail" {{ old('type') === 'mail' ? 'selected' : '' }}>Mail Server</option>
                        <option value="dns" {{ old('type') === 'dns' ? 'selected' : '' }}>DNS Server</option>
                        <option value="load_balancer" {{ old('type') === 'load_balancer' ? 'selected' : '' }}>Load Balancer</option>
                        <option value="storage" {{ old('type') === 'storage' ? 'selected' : '' }}>Storage Server</option>
                    </select>
                    @error('type')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="operating_system" class="block text-sm font-medium text-gray-700 mb-2">Operating System</label>
                    <input
                        type="text"
                        id="operating_system"
                        name="operating_system"
                        value="{{ old('operating_system') }}"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., Ubuntu 22.04 LTS"
                    >
                </div>
            </div>
        </div>

        <!-- Authentication -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Authentication</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Username *</label>
                    <input
                        type="text"
                        id="username"
                        name="username"
                        value="{{ old('username') }}"
                        required
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('username') border-red-500 @enderror"
                        placeholder="e.g., root"
                    >
                    @error('username')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="control_panel" class="block text-sm font-medium text-gray-700 mb-2">Control Panel</label>
                    <input
                        type="text"
                        id="control_panel"
                        name="control_panel"
                        value="{{ old('control_panel') }}"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., cPanel, Plesk, DirectAdmin"
                    >
                </div>

                <div class="md:col-span-2">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Leave empty if using SSH key"
                    >
                    <p class="text-sm text-gray-500 mt-1">Either password or SSH key is required</p>
                </div>

                <div class="md:col-span-2">
                    <label for="ssh_key" class="block text-sm font-medium text-gray-700 mb-2">SSH Private Key</label>
                    <textarea
                        id="ssh_key"
                        name="ssh_key"
                        rows="6"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="-----BEGIN PRIVATE KEY-----"
                    >{{ old('ssh_key') }}</textarea>
                    <p class="text-sm text-gray-500 mt-1">Paste your SSH private key here (optional if password is provided)</p>
                </div>
            </div>
        </div>

        <!-- Specifications -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Server Specifications</h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="specifications_cpu" class="block text-sm font-medium text-gray-700 mb-2">CPU</label>
                    <input
                        type="text"
                        id="specifications_cpu"
                        name="specifications[cpu]"
                        value="{{ old('specifications.cpu') }}"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., 4 vCPU"
                    >
                </div>

                <div>
                    <label for="specifications_ram" class="block text-sm font-medium text-gray-700 mb-2">RAM</label>
                    <input
                        type="text"
                        id="specifications_ram"
                        name="specifications[ram]"
                        value="{{ old('specifications.ram') }}"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., 8 GB"
                    >
                </div>

                <div>
                    <label for="specifications_storage" class="block text-sm font-medium text-gray-700 mb-2">Storage</label>
                    <input
                        type="text"
                        id="specifications_storage"
                        name="specifications[storage]"
                        value="{{ old('specifications.storage') }}"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., 100 GB SSD"
                    >
                </div>
            </div>
        </div>

        <!-- Notes -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Additional Notes</h3>

            <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                <textarea
                    id="notes"
                    name="notes"
                    rows="4"
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Any additional notes about this server..."
                >{{ old('notes') }}</textarea>
            </div>
        </div>

        <!-- Quick Templates -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Quick Templates</h3>
            <p class="text-sm text-gray-600 mb-4">Choose a pre-configured template to quickly set up your server</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Web Server Template -->
                <button type="button" onclick="fillTemplate('web')" class="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200 text-left">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                            </svg>
                        </div>
                        <div class="text-sm font-medium text-gray-900">Web Server</div>
                    </div>
                    <div class="text-xs text-gray-500">Apache/Nginx + PHP + MySQL</div>
                </button>

                <!-- Database Server Template -->
                <button type="button" onclick="fillTemplate('database')" class="p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors duration-200 text-left">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                            </svg>
                        </div>
                        <div class="text-sm font-medium text-gray-900">Database Server</div>
                    </div>
                    <div class="text-xs text-gray-500">MySQL/PostgreSQL dedicated</div>
                </button>

                <!-- Mail Server Template -->
                <button type="button" onclick="fillTemplate('mail')" class="p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors duration-200 text-left">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="text-sm font-medium text-gray-900">Mail Server</div>
                    </div>
                    <div class="text-xs text-gray-500">Postfix + Dovecot + SpamAssassin</div>
                </button>

                <!-- DNS Server Template -->
                <button type="button" onclick="fillTemplate('dns')" class="p-4 border border-gray-200 rounded-lg hover:border-cyan-300 hover:bg-cyan-50 transition-colors duration-200 text-left">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-cyan-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                            </svg>
                        </div>
                        <div class="text-sm font-medium text-gray-900">DNS Server</div>
                    </div>
                    <div class="text-xs text-gray-500">BIND9 DNS server</div>
                </button>

                <!-- Load Balancer Template -->
                <button type="button" onclick="fillTemplate('load_balancer')" class="p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors duration-200 text-left">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="text-sm font-medium text-gray-900">Load Balancer</div>
                    </div>
                    <div class="text-xs text-gray-500">HAProxy + Nginx</div>
                </button>

                <!-- Storage Server Template -->
                <button type="button" onclick="fillTemplate('storage')" class="p-4 border border-gray-200 rounded-lg hover:border-pink-300 hover:bg-pink-50 transition-colors duration-200 text-left">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-pink-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-9 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"></path>
                            </svg>
                        </div>
                        <div class="text-sm font-medium text-gray-900">Storage Server</div>
                    </div>
                    <div class="text-xs text-gray-500">NFS + Samba file server</div>
                </button>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="flex items-center justify-end space-x-4">
            <a href="{{ route('admin.servers.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                Cancel
            </a>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2" id="submit-btn">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Create Server</span>
            </button>
        </div>
    </form>
</div>

<script>
// Form validation and connection testing
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('server-form');
    const testBtn = document.getElementById('test-connection');
    const submitBtn = document.getElementById('submit-btn');
    const connectionAlert = document.getElementById('connection-alert');
    const connectionMessage = document.getElementById('connection-message');

    // Required fields for connection test
    const requiredFields = ['hostname', 'ip_address', 'port', 'username'];

    // Check if required fields are filled
    function checkRequiredFields() {
        const allFilled = requiredFields.every(field => {
            const input = document.getElementById(field);
            return input && input.value.trim() !== '';
        });

        const hasAuth = document.getElementById('password').value.trim() !== '' ||
                       document.getElementById('ssh_key').value.trim() !== '';

        testBtn.disabled = !(allFilled && hasAuth);
    }

    // Add event listeners to required fields
    requiredFields.forEach(field => {
        const input = document.getElementById(field);
        if (input) {
            input.addEventListener('input', checkRequiredFields);
        }
    });

    document.getElementById('password').addEventListener('input', checkRequiredFields);
    document.getElementById('ssh_key').addEventListener('input', checkRequiredFields);

    // Test connection
    testBtn.addEventListener('click', function() {
        const formData = new FormData(form);

        testBtn.disabled = true;
        testBtn.innerHTML = `
            <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            <span>Testing...</span>
        `;

        connectionAlert.classList.remove('hidden');
        connectionAlert.className = 'bg-blue-50 border border-blue-200 rounded-lg p-4';
        connectionMessage.textContent = 'Testing connection to server...';

        // Real AJAX connection test
        fetch('{{ route("admin.servers.test-connection-form") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                hostname: document.getElementById('hostname').value,
                ip_address: document.getElementById('ip_address').value,
                port: document.getElementById('port').value,
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                ssh_key: document.getElementById('ssh_key').value
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                connectionAlert.className = 'bg-green-50 border border-green-200 rounded-lg p-4';
                let systemInfo = '';
                if (data.system_info) {
                    systemInfo = `
                        <div class="mt-2 text-xs text-green-600">
                            <strong>System Info:</strong> ${data.system_info.os} |
                            CPU: ${data.system_info.cpu_cores} cores |
                            RAM: ${data.system_info.memory_total}MB |
                            Uptime: ${data.system_info.uptime}
                        </div>
                    `;
                }
                connectionMessage.innerHTML = `
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        ${data.message}
                    </div>
                    ${systemInfo}
                `;
                submitBtn.disabled = false;
            } else {
                connectionAlert.className = 'bg-red-50 border border-red-200 rounded-lg p-4';
                connectionMessage.innerHTML = `
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        ${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            connectionAlert.className = 'bg-red-50 border border-red-200 rounded-lg p-4';
            connectionMessage.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-4 h-4 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Network error: Unable to test connection.
                </div>
            `;
        })
        .finally(() => {
            testBtn.disabled = false;
            testBtn.innerHTML = `
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
                </svg>
                <span>Test Connection</span>
            `;
        });
    });

    // Initial check
    checkRequiredFields();
});

// Template filling function
function fillTemplate(type) {
    const templates = {
        web: {
            name: 'Web Server 01',
            type: 'web',
            operating_system: 'Ubuntu 22.04 LTS',
            control_panel: 'cPanel',
            port: 22,
            username: 'root',
            specifications: {
                cpu: '4 vCPU',
                ram: '8 GB',
                storage: '100 GB SSD'
            }
        },
        database: {
            name: 'Database Server 01',
            type: 'database',
            operating_system: 'Ubuntu 22.04 LTS',
            control_panel: 'phpMyAdmin',
            port: 22,
            username: 'root',
            specifications: {
                cpu: '8 vCPU',
                ram: '16 GB',
                storage: '500 GB SSD'
            }
        },
        mail: {
            name: 'Mail Server 01',
            type: 'mail',
            operating_system: 'Ubuntu 22.04 LTS',
            control_panel: 'Postfix Admin',
            port: 22,
            username: 'root',
            specifications: {
                cpu: '2 vCPU',
                ram: '4 GB',
                storage: '200 GB SSD'
            }
        },
        dns: {
            name: 'DNS Server 01',
            type: 'dns',
            operating_system: 'Ubuntu 22.04 LTS',
            control_panel: 'Webmin',
            port: 22,
            username: 'root',
            specifications: {
                cpu: '2 vCPU',
                ram: '2 GB',
                storage: '50 GB SSD'
            }
        },
        load_balancer: {
            name: 'Load Balancer 01',
            type: 'load_balancer',
            operating_system: 'Ubuntu 22.04 LTS',
            control_panel: 'HAProxy Stats',
            port: 22,
            username: 'root',
            specifications: {
                cpu: '4 vCPU',
                ram: '8 GB',
                storage: '100 GB SSD'
            }
        },
        storage: {
            name: 'Storage Server 01',
            type: 'storage',
            operating_system: 'Ubuntu 22.04 LTS',
            control_panel: 'Webmin',
            port: 22,
            username: 'root',
            specifications: {
                cpu: '4 vCPU',
                ram: '8 GB',
                storage: '2 TB HDD'
            }
        }
    };

    const template = templates[type];
    if (!template) return;

    // Fill form fields
    document.getElementById('name').value = template.name;
    document.getElementById('type').value = template.type;
    document.getElementById('operating_system').value = template.operating_system;
    document.getElementById('control_panel').value = template.control_panel;
    document.getElementById('port').value = template.port;
    document.getElementById('username').value = template.username;
    document.getElementById('specifications_cpu').value = template.specifications.cpu;
    document.getElementById('specifications_ram').value = template.specifications.ram;
    document.getElementById('specifications_storage').value = template.specifications.storage;

    // Check required fields after filling
    setTimeout(() => {
        const event = new Event('input', { bubbles: true });
        document.getElementById('name').dispatchEvent(event);
    }, 100);
}
</script>

</x-admin-layout>
