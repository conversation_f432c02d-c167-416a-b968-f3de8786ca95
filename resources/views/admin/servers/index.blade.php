<x-admin-layout>
    <x-slot name="title">Server Management</x-slot>
<div class="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 px-3 sm:px-6 lg:px-8 py-6" x-data="serversPage()">
    <!-- Modern Header -->
    <div class="max-w-7xl mx-auto">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 pt-4 sm:pt-6 space-y-4 sm:space-y-0">
            <div class="space-y-2">
                <h1 class="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-purple-600 via-indigo-600 to-purple-800 bg-clip-text text-transparent flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                        </svg>
                    </div>
                    <span>Server Management</span>
                </h1>
                <p class="text-purple-600/80 text-base sm:text-lg font-medium">Monitor and manage your hosting servers</p>
                <div class="flex items-center space-x-3 text-sm text-purple-500">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span>System Online</span>
                    </div>
                    <span>•</span>
                    <span id="live-time">{{ now()->format('H:i:s') }}</span>
                </div>
            </div>
            <div class="flex flex-col sm:flex-row items-center space-y-3 sm:space-y-0 sm:space-x-4 w-full sm:w-auto">
                <button
                    @click="refreshAll()"
                    class="group inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-slate-600 to-gray-700 border border-transparent rounded-2xl font-semibold text-sm text-white shadow-lg hover:shadow-xl hover:from-slate-700 hover:to-gray-800 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 w-full sm:w-auto"
                >
                    <svg class="w-5 h-5 mr-2 group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    <span>Refresh All</span>
                </button>

                <a href="{{ route('admin.servers.create') }}" class="group inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 border border-transparent rounded-2xl font-semibold text-sm text-white shadow-lg hover:shadow-xl hover:from-purple-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 w-full sm:w-auto">
                    <svg class="w-5 h-5 mr-2 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <span>Add Server</span>
                </a>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto">
        <!-- Enhanced Flash Messages -->
        @if(session('success'))
            <div class="mb-6 bg-gradient-to-r from-emerald-50 to-green-50 border border-emerald-200/50 rounded-2xl p-4 shadow-lg backdrop-blur-sm">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <span class="text-emerald-800 font-semibold">{{ session('success') }}</span>
                </div>
            </div>
        @endif

        @if(session('error'))
            <div class="mb-6 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200/50 rounded-2xl p-4 shadow-lg backdrop-blur-sm">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                    <span class="text-red-800 font-semibold">{{ session('error') }}</span>
                </div>
            </div>
        @endif

        <!-- Enhanced Statistics Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 sm:gap-6 mb-8">
            <!-- Total Servers Card -->
            <div class="group bg-gradient-to-br from-purple-500 via-indigo-500 to-purple-600 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-purple-400/20 backdrop-blur-sm">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white">Total Servers</h3>
                            <p class="text-white/80 text-sm">All registered</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="counter-number text-white text-3xl font-bold">{{ $stats['total'] }}</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-white/80 text-sm">
                    <span>Servers</span>
                    <span class="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full font-medium">
                        Total
                    </span>
                </div>
            </div>

            <!-- Online Servers Card -->
            <div class="group bg-gradient-to-br from-emerald-500 via-green-500 to-teal-600 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-emerald-400/20 backdrop-blur-sm">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white">Online</h3>
                            <p class="text-white/80 text-sm">Active servers</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="counter-number text-white text-3xl font-bold">{{ $stats['online'] }}</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-white/80 text-sm">
                    <span>Offline</span>
                    <span class="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full font-medium">
                        Online
                    </span>
                </div>
            </div>

            <!-- Offline Servers Card -->
            <div class="group bg-gradient-to-br from-red-500 via-pink-500 to-rose-600 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-red-400/20 backdrop-blur-sm">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white">Offline</h3>
                            <p class="text-white/80 text-sm">Down servers</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="counter-number text-white text-3xl font-bold">{{ $stats['offline'] }}</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-white/80 text-sm">
                    <span>Online</span>
                    <span class="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full font-medium">
                        Offline
                    </span>
                </div>
            </div>

            <!-- Maintenance Servers Card -->
            <div class="group bg-gradient-to-br from-amber-500 via-orange-500 to-yellow-600 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-amber-400/20 backdrop-blur-sm">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white">Maintenance</h3>
                            <p class="text-white/80 text-sm">Under repair</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="counter-number text-white text-3xl font-bold">{{ $stats['maintenance'] }}</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-white/80 text-sm">
                    <span>Active</span>
                    <span class="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full font-medium">
                        Maintenance
                    </span>
                </div>
            </div>
        </div>

        <!-- Enhanced Filters and Search -->
        <div class="bg-white/80 backdrop-blur-sm shadow-xl rounded-2xl border border-purple-200/50 mb-8 overflow-hidden">
            <div class="bg-gradient-to-r from-purple-500/10 to-indigo-500/10 px-6 py-4 border-b border-purple-200/30">
                <h3 class="text-lg font-semibold text-purple-800 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    Search & Filter Servers
                </h3>
            </div>
            <div class="p-6">
                <form method="GET" action="{{ route('admin.servers.index') }}" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div>
                        <label class="block text-sm font-semibold text-purple-700 mb-2">Search Servers</label>
                        <div class="relative group">
                            <input
                                type="text"
                                name="search"
                                value="{{ request('search') }}"
                                placeholder="Search servers..."
                                class="w-full pl-12 pr-4 py-3 border border-purple-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white/50 backdrop-blur-sm text-gray-900 placeholder-purple-400 transition-all duration-300 group-hover:bg-white/70"
                            >
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-purple-400 group-hover:text-purple-600 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-semibold text-purple-700 mb-2">Status Filter</label>
                        <select name="status" class="w-full px-4 py-3 border border-purple-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white/50 backdrop-blur-sm text-gray-900 transition-all duration-300 hover:bg-white/70">
                            <option value="">All Status</option>
                            <option value="online" {{ request('status') === 'online' ? 'selected' : '' }}>Online</option>
                            <option value="offline" {{ request('status') === 'offline' ? 'selected' : '' }}>Offline</option>
                            <option value="maintenance" {{ request('status') === 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-semibold text-purple-700 mb-2">Type Filter</label>
                        <select name="type" class="w-full px-4 py-3 border border-purple-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white/50 backdrop-blur-sm text-gray-900 transition-all duration-300 hover:bg-white/70">
                            <option value="">All Types</option>
                            <option value="web" {{ request('type') === 'web' ? 'selected' : '' }}>Web Server</option>
                            <option value="database" {{ request('type') === 'database' ? 'selected' : '' }}>Database</option>
                            <option value="mail" {{ request('type') === 'mail' ? 'selected' : '' }}>Mail Server</option>
                            <option value="dns" {{ request('type') === 'dns' ? 'selected' : '' }}>DNS Server</option>
                            <option value="load_balancer" {{ request('type') === 'load_balancer' ? 'selected' : '' }}>Load Balancer</option>
                            <option value="storage" {{ request('type') === 'storage' ? 'selected' : '' }}>Storage</option>
                        </select>
                    </div>

                    <div class="flex items-end space-x-3">
                        <button type="submit" class="group flex-1 bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-xl hover:from-purple-700 hover:to-indigo-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl font-semibold">
                            <svg class="w-4 h-4 inline mr-2 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                            </svg>
                            Filter
                        </button>
                        <a href="{{ route('admin.servers.index') }}" class="group px-4 py-3 border border-purple-300 rounded-xl text-purple-700 hover:bg-purple-50 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 bg-white/50 backdrop-blur-sm font-semibold">
                            <svg class="w-4 h-4 group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </a>
                    </div>
                </form>
            </div>
        </div>

    <!-- Servers Table -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <div class="p-6 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Servers List</h3>

                <!-- Bulk Actions -->
                <div class="flex items-center space-x-4">
                    <form method="POST" action="{{ route('admin.servers.bulk-action') }}" x-ref="bulkForm">
                        @csrf
                        <input type="hidden" name="server_ids" x-model="selectedServers.join(',')">
                        <div class="flex items-center space-x-2">
                            <select name="action" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                <option value="">Bulk Actions</option>
                                <option value="ping">Ping Selected</option>
                                <option value="update_monitoring">Update Monitoring</option>
                                <option value="set_maintenance">Set Maintenance</option>
                                <option value="activate">Activate</option>
                                <option value="deactivate">Deactivate</option>
                                <option value="delete">Delete</option>
                            </select>
                            <button
                                type="submit"
                                :disabled="selectedServers.length === 0"
                                class="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-300 text-white px-4 py-2 rounded-lg text-sm transition-colors duration-200"
                            >
                                Apply
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        @if($servers->count() > 0)
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left">
                                <input
                                    type="checkbox"
                                    @change="toggleAll($event.target.checked)"
                                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                >
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Server</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resources</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Ping</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($servers as $server)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <input
                                        type="checkbox"
                                        :value="{{ $server->id }}"
                                        x-model="selectedServers"
                                        class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                    >
                                </td>
                                <td class="px-6 py-4">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $server->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $server->hostname }}</div>
                                        <div class="text-sm text-gray-500">{{ $server->ip_address }}:{{ $server->port }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    {!! $server->status_badge !!}
                                </td>
                                <td class="px-6 py-4">
                                    {!! $server->type_badge !!}
                                </td>
                                <td class="px-6 py-4">
                                    <div class="space-y-1">
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xs text-gray-500">CPU:</span>
                                            <div class="flex-1 bg-gray-200 rounded-full h-2">
                                                <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $server->cpu_usage }}%"></div>
                                            </div>
                                            <span class="text-xs text-gray-600">{{ $server->cpu_usage }}%</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xs text-gray-500">RAM:</span>
                                            <div class="flex-1 bg-gray-200 rounded-full h-2">
                                                <div class="bg-green-600 h-2 rounded-full" style="width: {{ $server->ram_usage }}%"></div>
                                            </div>
                                            <span class="text-xs text-gray-600">{{ $server->ram_usage }}%</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">
                                    {{ $server->last_ping ? $server->last_ping->diffForHumans() : 'Never' }}
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center space-x-2">
                                        <button
                                            @click="pingServer({{ $server->id }})"
                                            class="text-blue-600 hover:text-blue-900 text-sm"
                                            title="Ping Server"
                                        >
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                            </svg>
                                        </button>

                                        <a href="{{ route('admin.servers.show', $server) }}" class="text-green-600 hover:text-green-900 text-sm" title="View Details">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                        </a>

                                        <a href="{{ route('admin.servers.edit', $server) }}" class="text-yellow-600 hover:text-yellow-900 text-sm" title="Edit Server">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </a>

                                        <form method="POST" action="{{ route('admin.servers.destroy', $server) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this server?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-600 hover:text-red-900 text-sm" title="Delete Server">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-gray-100">
                {{ $servers->links() }}
            </div>
        @else
            <div class="p-12 text-center">
                <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No servers found</h3>
                <p class="text-gray-500 mb-6">Get started by adding your first server.</p>
                <a href="{{ route('admin.servers.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg inline-flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <span>Add Server</span>
                </a>
            </div>
        @endif
    </div>

    <!-- Alpine.js Data -->
    <script>
        function serversPage() {
            return {
                selectedServers: [],

                toggleAll(checked) {
                    if (checked) {
                        this.selectedServers = @json($servers->pluck('id')->toArray());
                    } else {
                        this.selectedServers = [];
                    }
                },

                async pingServer(serverId) {
                    try {
                        const response = await fetch(`/admin/servers/${serverId}/ping`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        });

                        const data = await response.json();

                        if (data.success) {
                            this.showNotification(data.message, 'success');
                            // Refresh the page to show updated status
                            setTimeout(() => window.location.reload(), 1000);
                        }
                    } catch (error) {
                        this.showNotification('Failed to ping server', 'error');
                    }
                },

                async refreshAll() {
                    this.showNotification('Refreshing all servers...', 'info');
                    // In a real app, you'd make API calls to refresh all servers
                    setTimeout(() => window.location.reload(), 1000);
                },

                showNotification(message, type = 'success') {
                    // Create notification element
                    const notification = document.createElement('div');
                    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

                    notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-4 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
                    notification.innerHTML = `
                        <div class="flex items-center space-x-3">
                            <span>${message}</span>
                            <button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    `;

                    document.body.appendChild(notification);

                    // Animate in
                    setTimeout(() => {
                        notification.classList.remove('translate-x-full');
                    }, 100);

                    // Auto remove after 5 seconds
                    setTimeout(() => {
                        notification.classList.add('translate-x-full');
                        setTimeout(() => {
                            if (notification.parentElement) {
                                notification.remove();
                            }
                        }, 300);
                    }, 5000);
                }
            }
        }
    </script>

    <!-- Enhanced Interactive Features -->
    <script>
        // Live Clock Update
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const clockElement = document.getElementById('live-time');
            if (clockElement) {
                clockElement.textContent = timeString;
            }
        }

        // Update clock every second
        setInterval(updateClock, 1000);
        updateClock(); // Initial call

        // Animate counters on load
        document.addEventListener('DOMContentLoaded', function() {
            const counters = document.querySelectorAll('.counter-number');
            counters.forEach(counter => {
                const target = parseInt(counter.textContent);
                let current = 0;
                const increment = target / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current);
                }, 30);
            });
        });
    </script>
</div>
</x-admin-layout>
