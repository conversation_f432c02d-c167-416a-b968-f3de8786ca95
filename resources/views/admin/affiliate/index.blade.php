<x-admin-layout>
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- <PERSON>er -->
        <div class="sm:flex sm:items-center sm:justify-between mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Affiliate Program</h1>
                <p class="mt-2 text-sm text-gray-700">Manage affiliate programs, partners, and commission structures</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <button onclick="refreshData()" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                </button>
                <button onclick="showCreateProgramModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    New Program
                </button>
            </div>
        </div>

        <!-- Affiliate Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Affiliates</p>
                            <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_affiliates']) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Monthly Sales</p>
                            <p class="text-2xl font-bold text-green-600">${{ number_format($stats['monthly_sales'], 2) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Commissions</p>
                            <p class="text-2xl font-bold text-purple-600">${{ number_format($stats['total_commissions'], 2) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Conversion Rate</p>
                            <p class="text-2xl font-bold text-yellow-600">{{ $stats['conversion_rate'] }}%</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Commission Structures -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Commission Structures</h3>
                <p class="text-sm text-gray-500">Tiered commission rates based on performance</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    @foreach($commissionStructures as $structure)
                    <div class="border border-gray-200 rounded-lg p-6 {{ $structure['name'] === 'VIP' ? 'border-purple-300 bg-purple-50' : '' }} hover:shadow-lg transition-shadow duration-200">
                        <div class="text-center mb-4">
                            <h4 class="text-lg font-bold text-gray-900">{{ $structure['name'] }}</h4>
                            <p class="text-sm text-gray-500">{{ $structure['description'] }}</p>
                            <p class="text-xs text-gray-400 mt-1">{{ $structure['programs_count'] }} programs</p>
                        </div>
                        
                        <div class="space-y-3">
                            @foreach($structure['tiers'] as $tier)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <span class="text-sm font-medium text-gray-700">${{ $tier['sales_range'] }}</span>
                                <span class="text-sm font-bold text-blue-600">{{ $tier['rate'] }}%</span>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Affiliate Programs & Top Affiliates -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- Affiliate Programs -->
            <div class="lg:col-span-2">
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Affiliate Programs</h3>
                        <p class="text-sm text-gray-500">Manage product affiliate programs</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            @foreach($programs as $program)
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="text-sm font-semibold text-gray-900">{{ $program['name'] }}</h4>
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            {{ $program['status'] === 'active' ? 'bg-green-100 text-green-800' : 
                                               ($program['status'] === 'paused' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                                            {{ ucfirst($program['status']) }}
                                        </span>
                                        <button onclick="updateProgramStatus({{ $program['id'] }}, '{{ $program['status'] === 'active' ? 'paused' : 'active' }}')" 
                                                class="text-blue-600 hover:text-blue-900 text-xs">
                                            {{ $program['status'] === 'active' ? 'Pause' : 'Activate' }}
                                        </button>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-600 mb-3">{{ $program['description'] }}</p>
                                
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                                    <div>
                                        <span class="text-gray-500">Commission:</span>
                                        <p class="font-medium text-blue-600">{{ $program['commission_rate'] }}%</p>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">Cookie:</span>
                                        <p class="font-medium">{{ $program['cookie_duration'] }} days</p>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">Affiliates:</span>
                                        <p class="font-medium">{{ $program['affiliates_count'] }}</p>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">Monthly Sales:</span>
                                        <p class="font-medium text-green-600">${{ number_format($program['monthly_sales'], 2) }}</p>
                                    </div>
                                </div>
                                
                                <div class="mt-3 pt-3 border-t border-gray-100">
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-500">Monthly Commissions:</span>
                                        <span class="text-xs font-medium text-purple-600">${{ number_format($program['monthly_commissions'], 2) }}</span>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Affiliates -->
            <div>
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Top Affiliates</h3>
                        <p class="text-sm text-gray-500">Best performing partners</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            @foreach($topAffiliates as $index => $affiliate)
                            <div class="flex items-start space-x-3 p-3 border border-gray-100 rounded-lg hover:bg-gray-50">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 rounded-full flex items-center justify-center font-bold text-white
                                        {{ $index === 0 ? 'bg-yellow-500' : ($index === 1 ? 'bg-gray-400' : ($index === 2 ? 'bg-orange-500' : 'bg-blue-500')) }}">
                                        {{ $index + 1 }}
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900">{{ $affiliate['name'] }}</p>
                                    <p class="text-xs text-gray-500">{{ $affiliate['email'] }}</p>
                                    <div class="mt-2 space-y-1">
                                        <div class="flex justify-between text-xs">
                                            <span class="text-gray-500">Monthly Sales:</span>
                                            <span class="font-medium text-green-600">${{ number_format($affiliate['monthly_sales'], 2) }}</span>
                                        </div>
                                        <div class="flex justify-between text-xs">
                                            <span class="text-gray-500">Commission:</span>
                                            <span class="font-medium text-purple-600">${{ number_format($affiliate['monthly_commissions'], 2) }}</span>
                                        </div>
                                        <div class="flex justify-between text-xs">
                                            <span class="text-gray-500">Conversion:</span>
                                            <span class="font-medium text-blue-600">{{ $affiliate['conversion_rate'] }}%</span>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <div class="flex flex-wrap gap-1">
                                            @foreach($affiliate['programs'] as $program)
                                            <span class="inline-flex px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-700">{{ $program }}</span>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Recent Activities</h3>
                <p class="text-sm text-gray-500">Latest affiliate program activities</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Affiliate</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Program</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Commission</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($recentActivities as $activity)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full flex items-center justify-center mr-3
                                        {{ $activity['type'] === 'sale' ? 'bg-green-100' : 
                                           ($activity['type'] === 'signup' ? 'bg-blue-100' : 
                                           ($activity['type'] === 'payout' ? 'bg-purple-100' : 'bg-yellow-100')) }}">
                                        @if($activity['type'] === 'sale')
                                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                            </svg>
                                        @elseif($activity['type'] === 'signup')
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                                            </svg>
                                        @elseif($activity['type'] === 'payout')
                                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                            </svg>
                                        @else
                                            <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                            </svg>
                                        @endif
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ ucfirst($activity['type']) }}</div>
                                        <div class="text-xs text-gray-500">{{ $activity['description'] }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $activity['affiliate'] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $activity['program'] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                @if($activity['amount'] > 0)
                                    ${{ number_format($activity['amount'], 2) }}
                                @else
                                    -
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-purple-600">
                                @if($activity['commission'] > 0)
                                    ${{ number_format($activity['commission'], 2) }}
                                @else
                                    -
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $activity['timestamp']->diffForHumans() }}
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Create Program Modal -->
    <div id="createProgramModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
            <div class="relative bg-white rounded-lg max-w-lg w-full">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Create Affiliate Program</h3>
                </div>
                <form id="createProgramForm" class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Program Name</label>
                        <input type="text" name="name" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea name="description" rows="3" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"></textarea>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Commission Rate (%)</label>
                            <input type="number" name="commission_rate" min="1" max="50" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Cookie Duration (days)</label>
                            <input type="number" name="cookie_duration" min="1" max="365" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Minimum Payout ($)</label>
                        <input type="number" name="min_payout" min="10" step="0.01" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Commission Structure</label>
                        <select name="commission_structure" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            @foreach($commissionStructures as $structure)
                            <option value="{{ $structure['name'] }}">{{ $structure['name'] }} - {{ $structure['description'] }}</option>
                            @endforeach
                        </select>
                    </div>
                </form>
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button onclick="hideCreateProgramModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">Cancel</button>
                    <button onclick="createProgram()" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">Create Program</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Toast -->
    <div id="notification" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
            <div class="flex items-center">
                <div id="notification-icon" class="flex-shrink-0 mr-3">
                    <!-- Icon will be inserted here -->
                </div>
                <div>
                    <p id="notification-title" class="text-sm font-medium text-gray-900"></p>
                    <p id="notification-message" class="text-sm text-gray-500"></p>
                </div>
                <button onclick="hideNotification()" class="ml-4 text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        function showNotification(type, title, message) {
            const notification = document.getElementById('notification');
            const icon = document.getElementById('notification-icon');
            const titleEl = document.getElementById('notification-title');
            const messageEl = document.getElementById('notification-message');

            titleEl.textContent = title;
            messageEl.textContent = message;

            if (type === 'success') {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                `;
            } else {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                `;
            }

            notification.classList.remove('hidden');
            setTimeout(() => {
                hideNotification();
            }, 5000);
        }

        function hideNotification() {
            document.getElementById('notification').classList.add('hidden');
        }

        function showCreateProgramModal() {
            document.getElementById('createProgramModal').classList.remove('hidden');
        }

        function hideCreateProgramModal() {
            document.getElementById('createProgramModal').classList.add('hidden');
            document.getElementById('createProgramForm').reset();
        }

        function createProgram() {
            const form = document.getElementById('createProgramForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Creating...';
            button.disabled = true;

            fetch('/admin/affiliate/create-program', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Program Created', data.message);
                    hideCreateProgramModal();
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('error', 'Creation Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Creation Failed', 'Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
        }

        function updateProgramStatus(programId, status) {
            if (!confirm(`Are you sure you want to ${status} this program?`)) return;

            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Updating...';
            button.disabled = true;

            fetch('/admin/affiliate/update-program-status', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ program_id: programId, status: status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Status Updated', data.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('error', 'Update Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Update Failed', 'Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
        }

        function refreshData() {
            const refreshButton = document.querySelector('button[onclick="refreshData()"]');
            const originalText = refreshButton.innerHTML;

            refreshButton.innerHTML = `
                <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refreshing...
            `;

            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // Auto-refresh every 3 minutes for affiliate tracking
        setInterval(() => {
            fetch('/admin/affiliate/real-time-data')
                .then(response => response.json())
                .then(data => {
                    console.log('Affiliate data refreshed:', data.timestamp);
                })
                .catch(error => console.error('Auto-refresh failed:', error));
        }, 180000);

        // Add smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Close modal when clicking outside
            document.getElementById('createProgramModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    hideCreateProgramModal();
                }
            });

            // Add special effects for VIP commission structure
            const vipStructure = document.querySelector('.border-purple-300');
            if (vipStructure) {
                setInterval(() => {
                    vipStructure.style.boxShadow = '0 0 20px rgba(147, 51, 234, 0.3)';
                    setTimeout(() => {
                        vipStructure.style.boxShadow = '';
                    }, 1000);
                }, 4000);
            }

            // Add hover effects to program cards
            const programCards = document.querySelectorAll('.border-gray-200.rounded-lg.p-4');
            programCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'transform 0.2s ease';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Animate ranking numbers
            const rankingNumbers = document.querySelectorAll('.w-8.h-8.rounded-full');
            rankingNumbers.forEach((number, index) => {
                setTimeout(() => {
                    number.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        number.style.transform = 'scale(1)';
                    }, 200);
                }, index * 300);
            });
        });

        // Affiliate-specific features
        function showSaleAlert() {
            showNotification('success', 'New Sale', 'A new affiliate sale has been generated!');
        }

        function showSignupAlert() {
            showNotification('success', 'New Affiliate', 'A new affiliate has joined the program!');
        }

        // Simulate affiliate notifications
        setInterval(() => {
            if (Math.random() > 0.9) { // 10% chance for sale
                showSaleAlert();
            } else if (Math.random() > 0.95) { // 5% chance for signup
                showSignupAlert();
            }
        }, 300000); // Every 5 minutes
    </script>
</x-admin-layout>
