<x-admin-layout>
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- <PERSON> Header -->
        <div class="sm:flex sm:items-center sm:justify-between mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Billing Dashboard</h1>
                <p class="mt-2 text-sm text-gray-700">Monitor revenue, transactions, and subscription metrics</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <button onclick="refreshData()" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                </button>
                <button class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export Report
                </button>
            </div>
        </div>

        <!-- Revenue Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Revenue</p>
                            <p class="text-2xl font-bold text-gray-900">${{ number_format($stats['total_revenue'], 2) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Monthly Revenue</p>
                            <p class="text-2xl font-bold text-blue-600">${{ number_format($stats['monthly_revenue'], 2) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Active Subscriptions</p>
                            <p class="text-2xl font-bold text-purple-600">{{ $stats['active_subscriptions'] }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Pending Payments</p>
                            <p class="text-2xl font-bold text-yellow-600">${{ number_format($stats['pending_payments'], 2) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Analytics & Payment Methods -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <div class="lg:col-span-2">
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Revenue Analytics</h3>
                        <p class="text-sm text-gray-500">Monthly revenue trends and growth metrics</p>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-green-600">{{ $revenueAnalytics['yearly_growth'] }}%</div>
                                <div class="text-sm text-gray-500">Yearly Growth</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-blue-600">{{ $revenueAnalytics['quarterly_growth'] }}%</div>
                                <div class="text-sm text-gray-500">Quarterly Growth</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-purple-600">${{ number_format($revenueAnalytics['projected_annual']) }}</div>
                                <div class="text-sm text-gray-500">Projected Annual</div>
                            </div>
                        </div>
                        <div class="space-y-3">
                            @foreach($revenueAnalytics['monthly_data'] as $month => $revenue)
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-700">{{ $month }}</span>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ ($revenue / 20000) * 100 }}%"></div>
                                    </div>
                                    <span class="text-sm font-semibold text-gray-900">${{ number_format($revenue, 2) }}</span>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Methods -->
            <div>
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Payment Methods</h3>
                        <p class="text-sm text-gray-500">Payment method distribution</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            @foreach($paymentMethods as $method)
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 rounded-full {{ $method['method'] === 'Credit Card' ? 'bg-blue-500' : ($method['method'] === 'PayPal' ? 'bg-yellow-500' : ($method['method'] === 'Bank Transfer' ? 'bg-green-500' : 'bg-purple-500')) }}"></div>
                                    <span class="text-sm font-medium text-gray-700">{{ $method['method'] }}</span>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-gray-900">{{ $method['percentage'] }}%</div>
                                    <div class="text-xs text-gray-500">${{ number_format($method['revenue'], 2) }}</div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subscription Plans -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Subscription Plans Performance</h3>
                <p class="text-sm text-gray-500">Revenue and growth by subscription plan</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                    @foreach($subscriptions as $subscription)
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                        <div class="text-center">
                            <h4 class="text-lg font-semibold text-gray-900">{{ $subscription['plan'] }}</h4>
                            <div class="mt-2">
                                <span class="text-2xl font-bold text-blue-600">${{ $subscription['price'] }}</span>
                                <span class="text-gray-500">/month</span>
                            </div>
                        </div>
                        <div class="mt-4 space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-500">Subscribers:</span>
                                <span class="font-medium">{{ $subscription['subscribers'] }}</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-500">Revenue:</span>
                                <span class="font-medium">${{ number_format($subscription['revenue'], 2) }}</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-500">Growth:</span>
                                <span class="font-medium text-green-600">+{{ $subscription['growth'] }}%</span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Recent Transactions & Activities -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Recent Transactions -->
            <div class="lg:col-span-2">
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Recent Transactions</h3>
                        <p class="text-sm text-gray-500">Latest payment transactions</p>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($recentTransactions as $transaction)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $transaction['customer'] }}</div>
                                            <div class="text-sm text-gray-500">{{ $transaction['email'] }}</div>
                                            <div class="text-xs text-gray-400">{{ $transaction['description'] }}</div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">${{ number_format($transaction['amount'], 2) }}</div>
                                        <div class="text-xs text-gray-500">{{ $transaction['invoice_id'] }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            {{ $transaction['status'] === 'completed' ? 'bg-green-100 text-green-800' : 
                                               ($transaction['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                               ($transaction['status'] === 'failed' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800')) }}">
                                            {{ ucfirst($transaction['status']) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ ucfirst(str_replace('_', ' ', $transaction['payment_method'])) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2">
                                            <button class="text-blue-600 hover:text-blue-900">View</button>
                                            @if($transaction['status'] === 'failed')
                                                <button onclick="retryPayment('{{ $transaction['id'] }}')" class="text-green-600 hover:text-green-900">Retry</button>
                                            @endif
                                            @if($transaction['status'] === 'completed')
                                                <button onclick="processRefund('{{ $transaction['id'] }}', {{ $transaction['amount'] }})" class="text-red-600 hover:text-red-900">Refund</button>
                                            @endif
                                            <button onclick="generateInvoice('{{ $transaction['id'] }}')" class="text-purple-600 hover:text-purple-900">Invoice</button>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Billing Activities -->
            <div>
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Billing Activities</h3>
                        <p class="text-sm text-gray-500">Recent billing events</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            @foreach($billingActivities as $activity)
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 {{ $activity['type'] === 'payment_received' ? 'bg-green-100' : ($activity['type'] === 'payment_failed' ? 'bg-red-100' : 'bg-blue-100') }} rounded-full flex items-center justify-center">
                                        @if($activity['type'] === 'payment_received')
                                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        @elseif($activity['type'] === 'payment_failed')
                                            <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        @else
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                            </svg>
                                        @endif
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900">{{ $activity['description'] }}</p>
                                    <p class="text-xs text-gray-500">{{ $activity['timestamp']->diffForHumans() }}</p>
                                    @if($activity['amount'] > 0)
                                        <p class="text-xs font-medium {{ $activity['type'] === 'payment_received' ? 'text-green-600' : ($activity['type'] === 'payment_failed' ? 'text-red-600' : 'text-blue-600') }}">
                                            ${{ number_format($activity['amount'], 2) }}
                                        </p>
                                    @endif
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Toast -->
    <div id="notification" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
            <div class="flex items-center">
                <div id="notification-icon" class="flex-shrink-0 mr-3">
                    <!-- Icon will be inserted here -->
                </div>
                <div>
                    <p id="notification-title" class="text-sm font-medium text-gray-900"></p>
                    <p id="notification-message" class="text-sm text-gray-500"></p>
                </div>
                <button onclick="hideNotification()" class="ml-4 text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        function showNotification(type, title, message) {
            const notification = document.getElementById('notification');
            const icon = document.getElementById('notification-icon');
            const titleEl = document.getElementById('notification-title');
            const messageEl = document.getElementById('notification-message');

            titleEl.textContent = title;
            messageEl.textContent = message;

            if (type === 'success') {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                `;
            } else {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                `;
            }

            notification.classList.remove('hidden');
            setTimeout(() => {
                hideNotification();
            }, 5000);
        }

        function hideNotification() {
            document.getElementById('notification').classList.add('hidden');
        }

        function processRefund(transactionId, amount) {
            const reason = prompt('Please enter the reason for refund:');
            if (!reason) return;

            if (!confirm(`Are you sure you want to process a refund of $${amount}?`)) return;

            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Processing...';
            button.disabled = true;

            fetch('/admin/billing/process-refund', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    transaction_id: transactionId,
                    amount: amount,
                    reason: reason
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Refund Processed', data.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('error', 'Refund Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Refund Failed', 'Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
        }

        function retryPayment(transactionId) {
            if (!confirm('Are you sure you want to retry this payment?')) return;

            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Retrying...';
            button.disabled = true;

            fetch('/admin/billing/retry-payment', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ transaction_id: transactionId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Payment Retry', data.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('error', 'Retry Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Retry Failed', 'Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
        }

        function generateInvoice(transactionId) {
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Generating...';
            button.disabled = true;

            fetch('/admin/billing/generate-invoice', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ transaction_id: transactionId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification('success', 'Invoice Generated', data.message);
                    if (data.download_url) {
                        // Simulate download
                        setTimeout(() => {
                            showNotification('success', 'Download Ready', 'Invoice is ready for download');
                        }, 1000);
                    }
                } else {
                    showNotification('error', 'Generation Failed', data.message);
                }
            })
            .catch(error => {
                showNotification('error', 'Generation Failed', 'Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
        }

        function refreshData() {
            const refreshButton = document.querySelector('button[onclick="refreshData()"]');
            const originalText = refreshButton.innerHTML;

            refreshButton.innerHTML = `
                <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refreshing...
            `;

            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // Auto-refresh every 30 seconds
        setInterval(() => {
            fetch('/admin/billing/real-time-data')
                .then(response => response.json())
                .then(data => {
                    console.log('Data refreshed:', data.timestamp);
                    // Update specific elements here instead of full page reload
                })
                .catch(error => console.error('Auto-refresh failed:', error));
        }, 30000);

        // Add smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Add hover effects to transaction rows
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.01)';
                    this.style.transition = 'transform 0.2s ease';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });

        // Real-time revenue counter animation
        function animateValue(element, start, end, duration) {
            let startTimestamp = null;
            const step = (timestamp) => {
                if (!startTimestamp) startTimestamp = timestamp;
                const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                const current = progress * (end - start) + start;
                element.textContent = '$' + current.toLocaleString('en-US', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
                if (progress < 1) {
                    window.requestAnimationFrame(step);
                }
            };
            window.requestAnimationFrame(step);
        }

        // Animate revenue numbers on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                const revenueElements = document.querySelectorAll('.dashboard-card .text-2xl');
                revenueElements.forEach(element => {
                    const text = element.textContent;
                    if (text.includes('$')) {
                        const value = parseFloat(text.replace(/[$,]/g, ''));
                        element.textContent = '$0.00';
                        animateValue(element, 0, value, 2000);
                    }
                });
            }, 500);
        });
    </script>
</x-admin-layout>
