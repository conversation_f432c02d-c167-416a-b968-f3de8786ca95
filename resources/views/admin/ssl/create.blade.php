<x-admin-layout>
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- <PERSON>er -->
        <div class="sm:flex sm:items-center sm:justify-between mb-8">
            <div>
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <a href="{{ route('admin.domains.index') }}" class="text-gray-400 hover:text-gray-500">
                                <svg class="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L9 5.414V17a1 1 0 102 0V5.414l5.293 5.293a1 1 0 001.414-1.414l-7-7z"/>
                                </svg>
                                <span class="sr-only">Domains</span>
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                </svg>
                                <a href="{{ route('admin.domains.show', $domain) }}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">{{ $domain->name }}</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                </svg>
                                <a href="{{ route('admin.domains.ssl.index', $domain) }}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">SSL Certificates</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                </svg>
                                <span class="ml-4 text-sm font-medium text-gray-500">Create Certificate</span>
                            </div>
                        </li>
                    </ol>
                </nav>
                <h1 class="mt-2 text-2xl font-bold text-gray-900">Create SSL Certificate</h1>
                <p class="mt-2 text-sm text-gray-700">Create a new SSL certificate for {{ $domain->name }}</p>
            </div>
            <div class="mt-4 sm:mt-0">
                <a href="{{ route('admin.domains.ssl.index', $domain) }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to SSL
                </a>
            </div>
        </div>

        <!-- Create Form -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <form method="POST" action="{{ route('admin.domains.ssl.store', $domain) }}">
                @csrf
                
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Certificate Information</h3>
                    <p class="mt-1 text-sm text-gray-600">Configure your SSL certificate settings</p>
                </div>

                <div class="p-6 space-y-6">
                    <!-- Certificate Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Certificate Name</label>
                        <input type="text" name="name" id="name" value="{{ old('name', $domain->name . ' SSL') }}" 
                               class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('name') border-red-300 @enderror">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Certificate Type -->
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Certificate Type</label>
                        <select name="type" id="type" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('type') border-red-300 @enderror" onchange="toggleCertificateFields()">
                            <option value="letsencrypt" {{ old('type') === 'letsencrypt' ? 'selected' : '' }}>Let's Encrypt (Free)</option>
                            <option value="custom" {{ old('type') === 'custom' ? 'selected' : '' }}>Custom Certificate</option>
                            <option value="self-signed" {{ old('type') === 'self-signed' ? 'selected' : '' }}>Self-Signed</option>
                            <option value="cloudflare" {{ old('type') === 'cloudflare' ? 'selected' : '' }}>Cloudflare</option>
                        </select>
                        @error('type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Domains -->
                    <div>
                        <label for="domains" class="block text-sm font-medium text-gray-700 mb-2">Domains</label>
                        <div id="domains-container">
                            <div class="flex items-center space-x-2 mb-2">
                                <input type="text" name="domains[]" value="{{ old('domains.0', $domain->name) }}" 
                                       class="flex-1 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" 
                                       placeholder="example.com">
                                <button type="button" onclick="removeDomain(this)" class="px-3 py-2 text-red-600 hover:text-red-800">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                            <div class="flex items-center space-x-2 mb-2">
                                <input type="text" name="domains[]" value="{{ old('domains.1', 'www.' . $domain->name) }}" 
                                       class="flex-1 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" 
                                       placeholder="www.example.com">
                                <button type="button" onclick="removeDomain(this)" class="px-3 py-2 text-red-600 hover:text-red-800">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <button type="button" onclick="addDomain()" class="mt-2 inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Domain
                        </button>
                        @error('domains')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Auto Renewal Settings -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="flex items-center">
                                <input type="checkbox" name="auto_renew" id="auto_renew" value="1" 
                                       {{ old('auto_renew', true) ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="auto_renew" class="ml-2 block text-sm text-gray-900">
                                    Enable Auto Renewal
                                </label>
                            </div>
                            <p class="mt-1 text-sm text-gray-500">Automatically renew certificate before expiration</p>
                        </div>

                        <div>
                            <label for="days_before_renewal" class="block text-sm font-medium text-gray-700 mb-2">Days Before Renewal</label>
                            <input type="number" name="days_before_renewal" id="days_before_renewal" 
                                   value="{{ old('days_before_renewal', 30) }}" min="1" max="90"
                                   class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <p class="mt-1 text-sm text-gray-500">Renew certificate this many days before expiration</p>
                        </div>
                    </div>

                    <!-- Custom Certificate Fields (Hidden by default) -->
                    <div id="custom-certificate-fields" class="space-y-6" style="display: none;">
                        <div>
                            <label for="certificate" class="block text-sm font-medium text-gray-700 mb-2">Certificate (PEM Format)</label>
                            <textarea name="certificate" id="certificate" rows="8" 
                                      class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 font-mono text-sm @error('certificate') border-red-300 @enderror"
                                      placeholder="-----BEGIN CERTIFICATE-----
...
-----END CERTIFICATE-----">{{ old('certificate') }}</textarea>
                            @error('certificate')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="private_key" class="block text-sm font-medium text-gray-700 mb-2">Private Key (PEM Format)</label>
                            <textarea name="private_key" id="private_key" rows="8" 
                                      class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 font-mono text-sm @error('private_key') border-red-300 @enderror"
                                      placeholder="-----BEGIN PRIVATE KEY-----
...
-----END PRIVATE KEY-----">{{ old('private_key') }}</textarea>
                            @error('private_key')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="certificate_chain" class="block text-sm font-medium text-gray-700 mb-2">Certificate Chain (Optional)</label>
                            <textarea name="certificate_chain" id="certificate_chain" rows="6" 
                                      class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 font-mono text-sm @error('certificate_chain') border-red-300 @enderror"
                                      placeholder="-----BEGIN CERTIFICATE-----
...
-----END CERTIFICATE-----">{{ old('certificate_chain') }}</textarea>
                            @error('certificate_chain')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                    <a href="{{ route('admin.domains.ssl.index', $domain) }}" 
                       class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Create Certificate
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function toggleCertificateFields() {
            const type = document.getElementById('type').value;
            const customFields = document.getElementById('custom-certificate-fields');
            
            if (type === 'custom') {
                customFields.style.display = 'block';
            } else {
                customFields.style.display = 'none';
            }
        }

        function addDomain() {
            const container = document.getElementById('domains-container');
            const div = document.createElement('div');
            div.className = 'flex items-center space-x-2 mb-2';
            div.innerHTML = `
                <input type="text" name="domains[]" 
                       class="flex-1 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" 
                       placeholder="subdomain.example.com">
                <button type="button" onclick="removeDomain(this)" class="px-3 py-2 text-red-600 hover:text-red-800">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            `;
            container.appendChild(div);
        }

        function removeDomain(button) {
            const container = document.getElementById('domains-container');
            if (container.children.length > 1) {
                button.parentElement.remove();
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleCertificateFields();
        });
    </script>
</x-admin-layout>
