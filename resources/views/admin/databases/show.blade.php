<x-layouts.admin title="Database Details - {{ $database->name }}" header="Database Details">
    <!-- Dashboard Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                        {{ $database->name }}
                    </span>
                </h1>
                <p class="text-gray-600 mt-1">Database details and configuration</p>
            </div>
            <div class="flex items-center space-x-4">
                <form method="POST" action="{{ route('admin.databases.update-stats', $database) }}" class="inline">
                    @csrf
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span>Update Stats</span>
                    </button>
                </form>
                <a href="{{ route('admin.databases.edit', $database) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    <span>Edit Database</span>
                </a>
                <a href="{{ route('admin.databases.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    <span>Back to Databases</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Database Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <!-- Status -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Status</p>
                        <p class="text-2xl font-bold {{ $database->is_active ? 'text-green-600' : 'text-red-600' }}">
                            {{ $database->is_active ? 'Active' : 'Inactive' }}
                        </p>
                    </div>
                    <div class="w-12 h-12 {{ $database->is_active ? 'bg-green-100' : 'bg-red-100' }} rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 {{ $database->is_active ? 'text-green-600' : 'text-red-600' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            @if($database->is_active)
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            @else
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            @endif
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Type -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Database Type</p>
                        <p class="text-2xl font-bold {{ $database->type === 'mysql' ? 'text-blue-600' : ($database->type === 'postgresql' ? 'text-purple-600' : 'text-green-600') }}">
                            {{ ucfirst($database->type) }}
                        </p>
                    </div>
                    <div class="w-12 h-12 {{ $database->type === 'mysql' ? 'bg-blue-100' : ($database->type === 'postgresql' ? 'bg-purple-100' : 'bg-green-100') }} rounded-lg flex items-center justify-center">
                        <span class="text-xs font-bold {{ $database->type === 'mysql' ? 'text-blue-600' : ($database->type === 'postgresql' ? 'text-purple-600' : 'text-green-600') }}">
                            {{ strtoupper(substr($database->type, 0, 2)) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Size -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Current Size</p>
                        <p class="text-2xl font-bold text-indigo-600">{{ $database->size_in_mb }} MB</p>
                        @if($database->max_size_bytes)
                            <p class="text-xs text-gray-500">{{ $database->usage_percentage }}% used</p>
                        @endif
                    </div>
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table Count -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Tables</p>
                        <p class="text-2xl font-bold text-orange-600">{{ $database->table_count }}</p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Storage Usage Progress -->
    @if($database->max_size_bytes)
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 mb-8">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Storage Usage</h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Used: {{ $database->size_in_mb }} MB</span>
                    <span class="text-sm text-gray-600">Limit: {{ round($database->max_size_bytes / 1024 / 1024, 2) }} MB</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-4">
                    <div class="bg-gradient-to-r from-blue-500 to-indigo-600 h-4 rounded-full transition-all duration-300" 
                         style="width: {{ min($database->usage_percentage, 100) }}%"></div>
                </div>
                <div class="flex justify-between items-center text-xs text-gray-500">
                    <span>0%</span>
                    <span class="font-medium {{ $database->usage_percentage >= 80 ? 'text-orange-600' : 'text-gray-600' }}">
                        {{ $database->usage_percentage }}% used
                    </span>
                    <span>100%</span>
                </div>
                @if($database->usage_percentage >= 80)
                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-3">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-orange-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <span class="text-sm text-orange-800">
                                Warning: Database is approaching storage limit. Consider increasing quota or cleaning up data.
                            </span>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Database Information -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Database Information</h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Database Name</span>
                    <span class="text-sm text-gray-900 font-medium">{{ $database->name }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Owner</span>
                    <span class="text-sm text-gray-900">{{ $database->user->name ?? 'N/A' }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Associated Domain</span>
                    <span class="text-sm text-gray-900">{{ $database->domain->name ?? 'No domain' }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Database Type</span>
                    <span class="text-sm text-gray-900">{{ ucfirst($database->type) }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Created</span>
                    <span class="text-sm text-gray-900">{{ $database->created_at->format('M d, Y H:i') }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Last Backup</span>
                    <span class="text-sm text-gray-900">{{ $database->last_backup_at ? $database->last_backup_at->format('M d, Y H:i') : 'Never' }}</span>
                </div>
            </div>
        </div>

        <!-- Connection Details -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Connection Details</h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Host</span>
                    <span class="text-sm text-gray-900 font-mono">{{ $database->host }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-600">Port</span>
                    <span class="text-sm text-gray-900 font-mono">{{ $database->port }}</span>
                </div>
                @if($database->type === 'mysql' || $database->type === 'postgresql')
                    <div class="flex justify-between items-center py-2 border-b border-gray-100">
                        <span class="text-sm font-medium text-gray-600">Character Set</span>
                        <span class="text-sm text-gray-900">{{ $database->charset ?? 'N/A' }}</span>
                    </div>
                    <div class="flex justify-between items-center py-2 border-b border-gray-100">
                        <span class="text-sm font-medium text-gray-600">Collation</span>
                        <span class="text-sm text-gray-900">{{ $database->collation ?? 'N/A' }}</span>
                    </div>
                @endif
                <div class="bg-gray-50 rounded-lg p-4 mt-4">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Connection String</h4>
                    <code class="text-xs text-gray-600 break-all">
                        {{ $database->type }}://username:password@{{ $database->host }}:{{ $database->port }}/{{ $database->name }}
                    </code>
                </div>
            </div>
        </div>

        <!-- Database Users -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Database Users</h3>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors duration-200">
                        Add User
                    </button>
                </div>
            </div>
            <div class="p-6">
                @if($database->databaseUsers->count() > 0)
                    <div class="space-y-3">
                        @foreach($database->databaseUsers as $dbUser)
                            <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $dbUser->username }}</div>
                                    <div class="text-xs text-gray-500">{{ $dbUser->host }}</div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $dbUser->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $dbUser->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                    <button class="text-blue-600 hover:text-blue-900 text-sm">Edit</button>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-sm text-gray-500 text-center py-4">No database users configured</p>
                @endif
            </div>
        </div>

        <!-- Description -->
        @if($database->description)
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Description</h3>
            </div>
            <div class="p-6">
                <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ $database->description }}</p>
            </div>
        </div>
        @endif
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 bg-white rounded-xl shadow-lg border border-gray-100">
        <div class="p-6 border-b border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <button class="bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg p-4 text-center transition-colors duration-200">
                    <svg class="w-8 h-8 text-blue-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2 2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <div class="text-sm font-medium text-blue-900">Create Backup</div>
                    <div class="text-xs text-blue-600">Export database</div>
                </button>

                <button class="bg-green-50 hover:bg-green-100 border border-green-200 rounded-lg p-4 text-center transition-colors duration-200">
                    <svg class="w-8 h-8 text-green-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    <div class="text-sm font-medium text-green-900">Manage Users</div>
                    <div class="text-xs text-green-600">Add/edit users</div>
                </button>

                <button class="bg-purple-50 hover:bg-purple-100 border border-purple-200 rounded-lg p-4 text-center transition-colors duration-200">
                    <svg class="w-8 h-8 text-purple-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <div class="text-sm font-medium text-purple-900">View Tables</div>
                    <div class="text-xs text-purple-600">Browse structure</div>
                </button>

                <button class="bg-orange-50 hover:bg-orange-100 border border-orange-200 rounded-lg p-4 text-center transition-colors duration-200">
                    <svg class="w-8 h-8 text-orange-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <div class="text-sm font-medium text-orange-900">Settings</div>
                    <div class="text-xs text-orange-600">Configure options</div>
                </button>
            </div>
        </div>
    </div>
</x-layouts.admin>
