
<x-layouts.admin title="Edit Database - {{ $database->name }}" header="Edit Database">
    <!-- Dashboard Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                        Edit Database
                    </span>
                </h1>
                <p class="text-gray-600 mt-1">Update database configuration for {{ $database->name }}</p>
            </div>
            <div class="flex items-center space-x-4">
                <a href="{{ route('admin.databases.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    <span>Back to Databases</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Create Database Form -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100">
        <div class="p-6 border-b border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900">Database Configuration</h3>
            <p class="text-sm text-gray-600 mt-1">Enter the database details and configuration settings</p>
        </div>

        <form method="POST" action="{{ route('admin.databases.update', $database) }}" class="p-6 space-y-6">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Database Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Database Name *</label>
                    <input type="text" name="name" id="name" value="{{ old('name', $database->name) }}" required
                           placeholder="my_database"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror">
                    <p class="text-xs text-gray-500 mt-1">Only letters, numbers, and underscores allowed</p>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Database Type -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Database Type *</label>
                    <select name="type" id="type" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('type') border-red-500 @enderror">
                        <option value="">Select Database Type</option>
                        <option value="mysql" {{ old('type', $database->type) === 'mysql' ? 'selected' : '' }}>MySQL</option>
                        <option value="postgresql" {{ old('type', $database->type) === 'postgresql' ? 'selected' : '' }}>PostgreSQL</option>
                        <option value="mongodb" {{ old('type', $database->type) === 'mongodb' ? 'selected' : '' }}>MongoDB</option>
                    </select>
                    @error('type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Owner -->
                <div>
                    <label for="user_id" class="block text-sm font-medium text-gray-700 mb-2">Database Owner *</label>
                    <select name="user_id" id="user_id" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('user_id') border-red-500 @enderror">
                        <option value="">Select Owner</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}" {{ old('user_id', $database->user_id) == $user->id ? 'selected' : '' }}>
                                {{ $user->name }} ({{ $user->email }})
                            </option>
                        @endforeach
                    </select>
                    @error('user_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Associated Domain -->
                <div>
                    <label for="domain_id" class="block text-sm font-medium text-gray-700 mb-2">Associated Domain</label>
                    <select name="domain_id" id="domain_id"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('domain_id') border-red-500 @enderror">
                        <option value="">No Domain Association</option>
                        @foreach($domains as $domain)
                            <option value="{{ $domain->id }}" {{ old('domain_id', $database->domain_id) == $domain->id ? 'selected' : '' }}>
                                {{ $domain->name }}
                            </option>
                        @endforeach
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Optional: Link this database to a specific domain</p>
                    @error('domain_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Connection Settings -->
            <div class="space-y-4">
                <h4 class="text-md font-medium text-gray-900">Connection Settings</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Host -->
                    <div>
                        <label for="host" class="block text-sm font-medium text-gray-700 mb-2">Host *</label>
                        <input type="text" name="host" id="host" value="{{ old('host', $database->host) }}" required
                               placeholder="localhost"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('host') border-red-500 @enderror">
                        @error('host')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Port -->
                    <div>
                        <label for="port" class="block text-sm font-medium text-gray-700 mb-2">Port</label>
                        <input type="number" name="port" id="port" value="{{ old('port', $database->port) }}"
                               placeholder="Auto-detect based on type"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('port') border-red-500 @enderror">
                        <p class="text-xs text-gray-500 mt-1">Leave empty for default port (MySQL: 3306, PostgreSQL: 5432, MongoDB: 27017)</p>
                        @error('port')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- MySQL/PostgreSQL Specific Settings -->
            <div id="sql-settings" class="space-y-4" style="display: none;">
                <h4 class="text-md font-medium text-gray-900">Character Set & Collation</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Charset -->
                    <div>
                        <label for="charset" class="block text-sm font-medium text-gray-700 mb-2">Character Set</label>
                        <select name="charset" id="charset"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('charset') border-red-500 @enderror">
                            <option value="utf8mb4" {{ old('charset', $database->charset) === 'utf8mb4' ? 'selected' : '' }}>utf8mb4 (Recommended)</option>
                            <option value="utf8" {{ old('charset', $database->charset) === 'utf8' ? 'selected' : '' }}>utf8</option>
                            <option value="latin1" {{ old('charset', $database->charset) === 'latin1' ? 'selected' : '' }}>latin1</option>
                        </select>
                        @error('charset')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Collation -->
                    <div>
                        <label for="collation" class="block text-sm font-medium text-gray-700 mb-2">Collation</label>
                        <select name="collation" id="collation"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('collation') border-red-500 @enderror">
                            <option value="utf8mb4_unicode_ci" {{ old('collation', $database->collation) === 'utf8mb4_unicode_ci' ? 'selected' : '' }}>utf8mb4_unicode_ci (Recommended)</option>
                            <option value="utf8mb4_general_ci" {{ old('collation', $database->collation) === 'utf8mb4_general_ci' ? 'selected' : '' }}>utf8mb4_general_ci</option>
                            <option value="utf8_unicode_ci" {{ old('collation', $database->collation) === 'utf8_unicode_ci' ? 'selected' : '' }}>utf8_unicode_ci</option>
                        </select>
                        @error('collation')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Storage Settings -->
            <div class="space-y-4">
                <h4 class="text-md font-medium text-gray-900">Storage Settings</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Max Size -->
                    <div>
                        <label for="max_size_mb" class="block text-sm font-medium text-gray-700 mb-2">Maximum Size (MB)</label>
                        <input type="number" name="max_size_mb" id="max_size_mb" value="{{ old('max_size_mb', $database->max_size_bytes ? round($database->max_size_bytes / 1024 / 1024, 2) : '') }}"
                               placeholder="Leave empty for unlimited"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('max_size_mb') border-red-500 @enderror">
                        <p class="text-xs text-gray-500 mt-1">Set storage quota for this database</p>
                        @error('max_size_mb')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea name="description" id="description" rows="4" placeholder="Optional description for this database..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-500 @enderror">{{ old('description', $database->description) }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-100">
                <a href="{{ route('admin.databases.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                    Cancel
                </a>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                    Update Database
                </button>
            </div>
        </form>
    </div>

    <script>
        // Show/hide SQL-specific settings based on database type
        const typeSelect = document.getElementById('type');
        const sqlSettings = document.getElementById('sql-settings');
        const portInput = document.getElementById('port');

        typeSelect.addEventListener('change', function() {
            const selectedType = this.value;

            // Show/hide SQL settings
            if (selectedType === 'mysql' || selectedType === 'postgresql') {
                sqlSettings.style.display = 'block';
            } else {
                sqlSettings.style.display = 'none';
            }

            // Set default port if empty
            if (!portInput.value) {
                switch (selectedType) {
                    case 'mysql':
                        portInput.placeholder = '3306 (MySQL default)';
                        break;
                    case 'postgresql':
                        portInput.placeholder = '5432 (PostgreSQL default)';
                        break;
                    case 'mongodb':
                        portInput.placeholder = '27017 (MongoDB default)';
                        break;
                    default:
                        portInput.placeholder = 'Auto-detect based on type';
                }
            }
        });

        // Trigger change event on page load if type is already selected
        if (typeSelect.value) {
            typeSelect.dispatchEvent(new Event('change'));
        }

        // Update collation options based on charset
        const charsetSelect = document.getElementById('charset');
        const collationSelect = document.getElementById('collation');

        charsetSelect.addEventListener('change', function() {
            const charset = this.value;
            collationSelect.innerHTML = '';

            let collations = [];
            switch (charset) {
                case 'utf8mb4':
                    collations = [
                        { value: 'utf8mb4_unicode_ci', text: 'utf8mb4_unicode_ci (Recommended)' },
                        { value: 'utf8mb4_general_ci', text: 'utf8mb4_general_ci' },
                        { value: 'utf8mb4_bin', text: 'utf8mb4_bin' }
                    ];
                    break;
                case 'utf8':
                    collations = [
                        { value: 'utf8_unicode_ci', text: 'utf8_unicode_ci (Recommended)' },
                        { value: 'utf8_general_ci', text: 'utf8_general_ci' },
                        { value: 'utf8_bin', text: 'utf8_bin' }
                    ];
                    break;
                case 'latin1':
                    collations = [
                        { value: 'latin1_swedish_ci', text: 'latin1_swedish_ci (Default)' },
                        { value: 'latin1_general_ci', text: 'latin1_general_ci' },
                        { value: 'latin1_bin', text: 'latin1_bin' }
                    ];
                    break;
            }

            collations.forEach(collation => {
                const option = document.createElement('option');
                option.value = collation.value;
                option.textContent = collation.text;
                collationSelect.appendChild(option);
            });
        });
    </script>
</x-layouts.admin>
