<x-admin-layout>
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- <PERSON> Header -->
        <div class="sm:flex sm:items-center sm:justify-between mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Dashboard Overview</h1>
                <p class="mt-2 text-sm text-gray-700">Monitor your system performance and user statistics</p>
            </div>
            <div class="mt-4 sm:mt-0 flex items-center space-x-4">
                <!-- Real-time status indicator -->
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <span class="text-sm text-gray-600">System Online</span>
                </div>
                <!-- Live clock -->
                <div class="text-sm text-gray-500">
                    <span id="live-clock" class="font-medium">{{ now()->format('H:i:s') }}</span>
                </div>
                <!-- Refresh button -->
                <button onclick="location.reload()"
                        class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh Dashboard
                </button>
            </div>
        </div>

        <!-- System Performance Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- CPU Usage -->
            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">CPU Usage</h3>
                            <p class="text-sm text-gray-500">Current Usage</p>
                        </div>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Current Usage</span>
                        <span class="text-sm font-medium text-gray-900" id="cpu-usage-text">{{ $systemInfo['cpu'] ?? 0 }}%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Status</span>
                        <span class="text-sm font-medium text-gray-900" id="cpu-status">
                            {{ ($systemInfo['cpu'] ?? 0) < 70 ? 'Normal' : (($systemInfo['cpu'] ?? 0) < 90 ? 'High' : 'Critical') }}
                        </span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" id="cpu-progress" style="width: {{ $systemInfo['cpu'] ?? 0 }}%"></div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-2xl font-bold text-gray-900" id="cpu-percentage">{{ $systemInfo['cpu'] ?? 0 }}%</span>
                        <span class="text-sm {{ ($systemInfo['cpu'] ?? 0) < 50 ? 'text-green-600 bg-green-50' : (($systemInfo['cpu'] ?? 0) < 80 ? 'text-orange-600 bg-orange-50' : 'text-red-600 bg-red-50') }} px-2 py-1 rounded-full" id="cpu-badge">
                            {{ ($systemInfo['cpu'] ?? 0) < 50 ? 'Normal' : (($systemInfo['cpu'] ?? 0) < 80 ? 'Moderate' : 'High') }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

            <!-- Memory Usage -->
            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center stats-icon">
                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Memory Usage</h3>
                            <p class="text-sm text-gray-500">RAM Usage</p>
                        </div>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Used</span>
                        <span class="text-sm font-medium text-gray-900" id="memory-used">{{ $systemInfo['memory']['used'] ?? 0 }} MB</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Total</span>
                        <span class="text-sm font-medium text-gray-900" id="memory-total">{{ $systemInfo['memory']['total'] ?? 0 }} MB</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-purple-600 h-2 rounded-full transition-all duration-300" id="memory-progress" style="width: {{ $systemInfo['memory']['percent_used'] ?? 0 }}%"></div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-2xl font-bold text-gray-900" id="memory-percentage">{{ $systemInfo['memory']['percent_used'] ?? 0 }}%</span>
                        <span class="text-sm text-green-600 bg-green-50 px-2 py-1 rounded-full" id="memory-free">Free {{ $systemInfo['memory']['free'] ?? 0 }} MB</span>
                    </div>
                </div>
            </div>
        </div>

            <!-- Disk Usage -->
            <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center stats-icon">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Disk Usage</h3>
                            <p class="text-sm text-gray-500">Storage</p>
                        </div>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Used</span>
                        <span class="text-sm font-medium text-gray-900" id="disk-used">{{ $systemInfo['disk']['used'] ?? '0' }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Total</span>
                        <span class="text-sm font-medium text-gray-900" id="disk-total">{{ $systemInfo['disk']['size'] ?? '0' }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-600 h-2 rounded-full transition-all duration-300" id="disk-progress" style="width: {{ $systemInfo['disk']['percent_used'] ?? 0 }}%"></div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-2xl font-bold text-gray-900" id="disk-percentage">{{ $systemInfo['disk']['percent_used'] ?? 0 }}%</span>
                        <span class="text-sm text-green-600 bg-green-50 px-2 py-1 rounded-full" id="disk-free">Free {{ $systemInfo['disk']['available'] ?? '0' }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <!-- Server Information -->
        <div class="dashboard-card bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 mb-8">
        <div class="p-6 border-b border-gray-100">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">Server Information</h3>
            </div>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="flex items-center justify-center space-x-2 mb-2">
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                        </svg>
                        <span class="text-sm text-gray-600">Operating System</span>
                    </div>
                    <p class="text-lg font-semibold text-gray-900" id="linux-version">{{ $systemInfo['linux_version'] ?? 'Unknown' }}</p>
                </div>
                <div class="text-center">
                    <div class="flex items-center justify-center space-x-2 mb-2">
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                        </svg>
                        <span class="text-sm text-gray-600">PHP Version</span>
                    </div>
                    <p class="text-lg font-semibold text-gray-900" id="php-version">{{ $systemInfo['php_version'] ?? 'Unknown' }}</p>
                </div>
                <div class="text-center">
                    <div class="flex items-center justify-center space-x-2 mb-2">
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-sm text-gray-600">System Uptime</span>
                    </div>
                    <p class="text-lg font-semibold text-gray-900" id="system-uptime">{{ $systemInfo['uptime'] ?? 'Unknown' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- User Statistics -->
    <div class="bg-blue-600 rounded-xl shadow-lg mb-8 overflow-hidden">
        <div class="p-6">
            <div class="flex items-center space-x-3 mb-6">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-white">User Statistics</h3>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div class="text-3xl font-bold text-white mb-1">{{ $userStats['total_users'] }}</div>
                    <div class="text-blue-200 text-sm">Total Users</div>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <div class="text-3xl font-bold text-white mb-1">{{ $userStats['admin_users'] }}</div>
                    <div class="text-blue-200 text-sm">Administrators</div>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="text-3xl font-bold text-white mb-1">{{ $userStats['reseller_users'] }}</div>
                    <div class="text-blue-200 text-sm">Resellers</div>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div class="text-3xl font-bold text-white mb-1">{{ $userStats['client_users'] }}</div>
                    <div class="text-blue-200 text-sm">Clients</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Domain & Database Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- Domain Statistics -->
        <div class="bg-green-600 rounded-xl shadow-lg overflow-hidden">
            <div class="p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-white">Domain Statistics</h3>
                </div>
                <div class="grid grid-cols-2 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-1" id="total-domains">{{ $domainStats['total_domains'] ?? 0 }}</div>
                        <div class="text-green-200 text-sm">Total Domains</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-1" id="active-domains">{{ $domainStats['active_domains'] ?? 0 }}</div>
                        <div class="text-green-200 text-sm">Active Domains</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-1" id="domains-today">{{ $domainStats['domains_today'] ?? 0 }}</div>
                        <div class="text-green-200 text-sm">Added Today</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-1" id="domains-month">{{ $domainStats['domains_this_month'] ?? 0 }}</div>
                        <div class="text-green-200 text-sm">This Month</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Statistics -->
        <div class="bg-indigo-600 rounded-xl shadow-lg overflow-hidden">
            <div class="p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-white">Database Statistics</h3>
                </div>
                <div class="grid grid-cols-2 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-1" id="total-databases">{{ $databaseStats['total_databases'] ?? 0 }}</div>
                        <div class="text-indigo-200 text-sm">Total Databases</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-1" id="total-db-users">{{ $databaseStats['total_db_users'] ?? 0 }}</div>
                        <div class="text-indigo-200 text-sm">DB Users</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-1" id="databases-today">{{ $databaseStats['databases_today'] ?? 0 }}</div>
                        <div class="text-indigo-200 text-sm">Created Today</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-1" id="databases-month">{{ $databaseStats['databases_this_month'] ?? 0 }}</div>
                        <div class="text-indigo-200 text-sm">This Month</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Information & Recent Users -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- User Information -->
        <div class="bg-orange-500 rounded-xl shadow-lg overflow-hidden">
            <div class="p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-white">User Information</h3>
                </div>
                <div class="grid grid-cols-2 gap-6">
                    <div>
                        <div class="flex items-center space-x-2 mb-2">
                            <svg class="w-4 h-4 text-orange-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <span class="text-orange-200 text-sm">Name</span>
                        </div>
                        <p class="text-white font-semibold">{{ auth()->user()->name }}</p>
                    </div>
                    <div>
                        <div class="flex items-center space-x-2 mb-2">
                            <svg class="w-4 h-4 text-orange-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                            <span class="text-orange-200 text-sm">Roles</span>
                        </div>
                        <p class="text-white font-semibold">Administrator</p>
                    </div>
                    <div>
                        <div class="flex items-center space-x-2 mb-2">
                            <svg class="w-4 h-4 text-orange-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <span class="text-orange-200 text-sm">Email</span>
                        </div>
                        <p class="text-white font-semibold">{{ auth()->user()->email }}</p>
                    </div>
                    <div>
                        <div class="flex items-center space-x-2 mb-2">
                            <svg class="w-4 h-4 text-orange-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-1 12a2 2 0 002 2h6a2 2 0 002-2L15 7"></path>
                            </svg>
                            <span class="text-orange-200 text-sm">Account Created</span>
                        </div>
                        <p class="text-white font-semibold">{{ auth()->user()->created_at->format('Y-m-d H:i') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Users -->
        <div class="bg-purple-600 rounded-xl shadow-lg overflow-hidden">
            <div class="p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-white">Recent Users</h3>
                </div>
                <div class="space-y-4">
                    @foreach($recentUsers as $user)
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <span class="text-white font-semibold text-sm">{{ substr($user->name, 0, 1) }}</span>
                        </div>
                        <div class="flex-1">
                            <p class="text-white font-medium">{{ $user->name }}</p>
                            <p class="text-purple-200 text-sm">{{ $user->email }}</p>
                        </div>
                        <div class="text-right">
                            <span class="text-purple-200 text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">
                                Client
                            </span>
                        </div>
                    </div>
                    @endforeach
                </div>
                <div class="mt-6">
                    <a href="{{ route('admin.users.index') }}" class="text-white hover:text-purple-200 text-sm font-medium flex items-center">
                        View All Users
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Updates Script -->
    <script>
        // Auto-refresh dashboard data every 30 seconds
        setInterval(function() {
            fetch('{{ route("admin.dashboard.data") }}')
                .then(response => response.json())
                .then(data => {
                    // Update timestamp
                    const clockElement = document.getElementById('live-clock');
                    if (clockElement) {
                        clockElement.textContent = data.timestamp;
                    }

                    // Update CPU data
                    if (data.systemInfo && data.systemInfo.cpu !== undefined) {
                        updateElement('cpu-usage-text', data.systemInfo.cpu + '%');
                        updateElement('cpu-percentage', data.systemInfo.cpu + '%');
                        updateElement('cpu-status', data.systemInfo.cpu < 70 ? 'Normal' : (data.systemInfo.cpu < 90 ? 'High' : 'Critical'));
                        updateProgressBar('cpu-progress', data.systemInfo.cpu);
                        updateCpuBadge(data.systemInfo.cpu);
                    }

                    // Update Memory data
                    if (data.systemInfo && data.systemInfo.memory) {
                        updateElement('memory-used', data.systemInfo.memory.used + ' MB');
                        updateElement('memory-total', data.systemInfo.memory.total + ' MB');
                        updateElement('memory-percentage', data.systemInfo.memory.percent_used + '%');
                        updateElement('memory-free', 'Free ' + data.systemInfo.memory.free + ' MB');
                        updateProgressBar('memory-progress', data.systemInfo.memory.percent_used);
                    }

                    // Update Disk data
                    if (data.systemInfo && data.systemInfo.disk) {
                        updateElement('disk-used', data.systemInfo.disk.used);
                        updateElement('disk-total', data.systemInfo.disk.size);
                        updateElement('disk-percentage', data.systemInfo.disk.percent_used + '%');
                        updateElement('disk-free', 'Free ' + data.systemInfo.disk.available);
                        updateProgressBar('disk-progress', data.systemInfo.disk.percent_used);
                    }

                    // Update System Info
                    if (data.systemInfo) {
                        updateElement('linux-version', data.systemInfo.linux_version || 'Unknown');
                        updateElement('php-version', data.systemInfo.php_version || 'Unknown');
                        updateElement('system-uptime', data.systemInfo.uptime || 'Unknown');
                    }

                    // Update Domain Statistics
                    if (data.domainStats) {
                        updateElement('total-domains', data.domainStats.total_domains || 0);
                        updateElement('active-domains', data.domainStats.active_domains || 0);
                        updateElement('domains-today', data.domainStats.domains_today || 0);
                        updateElement('domains-month', data.domainStats.domains_this_month || 0);
                    }

                    // Update Database Statistics
                    if (data.databaseStats) {
                        updateElement('total-databases', data.databaseStats.total_databases || 0);
                        updateElement('total-db-users', data.databaseStats.total_db_users || 0);
                        updateElement('databases-today', data.databaseStats.databases_today || 0);
                        updateElement('databases-month', data.databaseStats.databases_this_month || 0);
                    }

                    console.log('Dashboard data refreshed at', data.timestamp);
                })
                .catch(error => {
                    console.error('Error refreshing dashboard data:', error);
                });
        }, 30000); // 30 seconds

        // Helper functions
        function updateElement(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        }

        function updateProgressBar(id, percentage) {
            const element = document.getElementById(id);
            if (element) {
                element.style.width = percentage + '%';
            }
        }

        function updateCpuBadge(cpuUsage) {
            const badge = document.getElementById('cpu-badge');
            if (badge) {
                // Remove existing classes
                badge.className = badge.className.replace(/text-\w+-600|bg-\w+-50/g, '');

                // Add new classes based on CPU usage
                if (cpuUsage < 50) {
                    badge.classList.add('text-green-600', 'bg-green-50');
                    badge.textContent = 'Normal';
                } else if (cpuUsage < 80) {
                    badge.classList.add('text-orange-600', 'bg-orange-50');
                    badge.textContent = 'Moderate';
                } else {
                    badge.classList.add('text-red-600', 'bg-red-50');
                    badge.textContent = 'High';
                }
            }
        }

        // Manual refresh function
        function refreshDashboard() {
            location.reload();
        }

        // Add click handler to refresh button
        document.addEventListener('DOMContentLoaded', function() {
            const refreshButton = document.querySelector('button[onclick="location.reload()"]');
            if (refreshButton) {
                refreshButton.onclick = function(e) {
                    e.preventDefault();
                    refreshDashboard();
                };
            }
        });
    </script>
    </div>
</x-admin-layout>
