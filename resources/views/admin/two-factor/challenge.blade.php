<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Two-Factor Authentication - {{ config('app.name') }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .glassmorphism {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Logo/Header -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-white rounded-full shadow-lg mb-4">
                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
            </div>
            <h1 class="text-2xl font-bold text-white mb-2">Two-Factor Authentication</h1>
            <p class="text-white/80">Please enter your authentication code to continue</p>
        </div>

        <!-- Main Card -->
        <div class="glassmorphism rounded-2xl p-8 shadow-xl">
            <!-- Error Messages -->
            @if($errors->any())
                <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="font-medium">{{ $errors->first() }}</span>
                    </div>
                </div>
            @endif

            <!-- Authentication Form -->
            <form method="POST" action="{{ route('admin.two-factor.verify') }}" class="space-y-6">
                @csrf
                
                <!-- Code Input -->
                <div>
                    <label for="code" class="block text-sm font-medium text-white mb-2">
                        Authentication Code
                    </label>
                    <input type="text" 
                           id="code" 
                           name="code" 
                           maxlength="8"
                           class="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent text-white placeholder-white/60 text-center text-lg font-mono tracking-widest"
                           placeholder="000000"
                           required
                           autocomplete="off"
                           autofocus>
                    <p class="text-white/70 text-xs mt-2">
                        Enter the 6-digit code from your authenticator app or an 8-character recovery code
                    </p>
                </div>

                <!-- Submit Button -->
                <button type="submit" 
                        class="w-full bg-white text-blue-600 py-3 px-4 rounded-lg font-semibold hover:bg-white/90 transition-all duration-200 shadow-lg">
                    Verify Code
                </button>
            </form>

            <!-- Help Section -->
            <div class="mt-8 pt-6 border-t border-white/20">
                <div class="text-center">
                    <h3 class="text-white font-medium mb-3">Need Help?</h3>
                    <div class="space-y-2 text-sm text-white/80">
                        <p>• Use your authenticator app to get a 6-digit code</p>
                        <p>• Or use one of your 8-character recovery codes</p>
                        <p>• Contact support if you've lost access to both</p>
                    </div>
                </div>
            </div>

            <!-- Back to Login -->
            <div class="mt-6 text-center">
                <form method="POST" action="{{ route('logout') }}" class="inline">
                    @csrf
                    <button type="submit" class="text-white/80 hover:text-white text-sm underline">
                        Sign out and return to login
                    </button>
                </form>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8">
            <p class="text-white/60 text-sm">
                © {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
            </p>
        </div>
    </div>

    <script>
        // Auto-format code input
        document.getElementById('code').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
            
            // Limit to 8 characters (recovery codes) or 6 digits (TOTP)
            if (value.length > 8) {
                value = value.substring(0, 8);
            }
            
            e.target.value = value;
        });

        // Auto-submit when 6 digits are entered (TOTP codes)
        document.getElementById('code').addEventListener('input', function(e) {
            if (e.target.value.length === 6 && /^\d{6}$/.test(e.target.value)) {
                // Small delay to allow user to see the complete code
                setTimeout(() => {
                    e.target.form.submit();
                }, 500);
            }
        });

        // Focus on input when page loads
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('code').focus();
        });
    </script>
</body>
</html>
