<x-layouts.admin title="Two-Factor Authentication Setup" header="Two-Factor Authentication Setup">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">
                <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    Two-Factor Authentication Setup
                </span>
            </h1>
            <p class="text-gray-600 mt-2">Secure your account with two-factor authentication</p>
        </div>

        <!-- Setup Card -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-8">
                <!-- Step 1: Install App -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <span class="text-blue-600 font-semibold">1</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Install Authenticator App</h3>
                    </div>
                    <p class="text-gray-600 ml-11 mb-4">
                        Install a two-factor authentication app on your phone such as:
                    </p>
                    <div class="ml-11 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 2L3 7v11c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V7l-7-5z"/>
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Google Authenticator</span>
                        </div>
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 2L3 7v11c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V7l-7-5z"/>
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Microsoft Authenticator</span>
                        </div>
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 2L3 7v11c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V7l-7-5z"/>
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Authy</span>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Scan QR Code -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <span class="text-blue-600 font-semibold">2</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Scan QR Code</h3>
                    </div>
                    <div class="ml-11">
                        <p class="text-gray-600 mb-4">
                            Scan this QR code with your authenticator app:
                        </p>
                        <div class="flex flex-col md:flex-row items-start space-y-4 md:space-y-0 md:space-x-8">
                            <!-- QR Code -->
                            <div class="bg-white p-4 rounded-lg border-2 border-gray-200">
                                <div id="qr-code" class="w-48 h-48 flex items-center justify-center bg-gray-50 rounded-lg">
                                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={{ urlencode($qrCodeUrl) }}" 
                                         alt="QR Code" class="w-full h-full">
                                </div>
                            </div>
                            
                            <!-- Manual Entry -->
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900 mb-2">Can't scan the code?</h4>
                                <p class="text-sm text-gray-600 mb-3">Enter this code manually in your app:</p>
                                <div class="bg-gray-50 p-3 rounded-lg border">
                                    <code class="text-sm font-mono text-gray-800 break-all">{{ $secret }}</code>
                                </div>
                                <button onclick="copySecret()" class="mt-2 text-sm text-blue-600 hover:text-blue-700">
                                    Copy to clipboard
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Verify -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <span class="text-blue-600 font-semibold">3</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Verify Setup</h3>
                    </div>
                    <div class="ml-11">
                        <p class="text-gray-600 mb-4">
                            Enter the 6-digit code from your authenticator app to complete setup:
                        </p>
                        
                        <form method="POST" action="{{ route('admin.two-factor.enable') }}" class="max-w-md">
                            @csrf
                            
                            <div class="mb-4">
                                <label for="code" class="block text-sm font-medium text-gray-700 mb-2">
                                    Authentication Code
                                </label>
                                <input type="text" 
                                       id="code" 
                                       name="code" 
                                       maxlength="6" 
                                       pattern="[0-9]{6}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center text-lg font-mono tracking-widest"
                                       placeholder="000000"
                                       required
                                       autocomplete="off">
                                @error('code')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div class="flex space-x-4">
                                <button type="submit" 
                                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                    Enable Two-Factor Authentication
                                </button>
                                <a href="{{ route('admin.settings') }}" 
                                   class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors">
                                    Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function copySecret() {
            const secret = '{{ $secret }}';
            navigator.clipboard.writeText(secret).then(function() {
                // Show success message
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.classList.add('text-green-600');
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('text-green-600');
                }, 2000);
            });
        }

        // Auto-focus on code input
        document.getElementById('code').focus();
        
        // Auto-format code input
        document.getElementById('code').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 6) {
                value = value.substring(0, 6);
            }
            e.target.value = value;
        });
    </script>
</x-layouts.admin>
