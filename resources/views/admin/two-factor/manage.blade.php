<x-layouts.admin title="Two-Factor Authentication" header="Two-Factor Authentication">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">
                <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    Two-Factor Authentication
                </span>
            </h1>
            <p class="text-gray-600 mt-2">Manage your two-factor authentication settings</p>
        </div>

        <!-- Success Messages -->
        @if(session('success'))
            <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {{ session('error') }}
            </div>
        @endif

        <!-- Status Card -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 mb-8">
            <div class="p-6 border-b border-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Two-Factor Authentication</h3>
                            <p class="text-sm text-gray-500">
                                Status: <span class="text-green-600 font-medium">Enabled</span>
                            </p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                            Active
                        </span>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <p class="text-gray-600 mb-4">
                    Two-factor authentication is currently enabled for your account. This adds an extra layer of security by requiring a code from your authenticator app when signing in.
                </p>
                <p class="text-sm text-gray-500">
                    Enabled on: {{ $user->two_factor_confirmed_at->format('F j, Y \a\t g:i A') }}
                </p>
            </div>
        </div>

        <!-- Recovery Codes -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 mb-8">
            <div class="p-6 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Recovery Codes</h3>
                </div>
            </div>
            <div class="p-6">
                <p class="text-gray-600 mb-4">
                    Recovery codes can be used to access your account if you lose your authenticator device. Store them in a safe place.
                </p>
                
                @if(session('new_recovery_codes'))
                    <div class="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <p class="text-yellow-800 text-sm font-medium mb-2">
                            ⚠️ New recovery codes generated! Save these codes now - they won't be shown again.
                        </p>
                    </div>
                    <div class="grid grid-cols-2 gap-2 mb-4 p-4 bg-gray-50 rounded-lg font-mono text-sm">
                        @foreach(session('new_recovery_codes') as $code)
                            <div class="p-2 bg-white rounded border">{{ $code }}</div>
                        @endforeach
                    </div>
                @else
                    <div class="grid grid-cols-2 gap-2 mb-4 p-4 bg-gray-50 rounded-lg font-mono text-sm">
                        @foreach($recoveryCodes as $code)
                            <div class="p-2 bg-white rounded border">{{ $code }}</div>
                        @endforeach
                    </div>
                @endif
                
                <div class="flex space-x-4">
                    <button onclick="downloadRecoveryCodes()" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        Download Codes
                    </button>
                    <button onclick="showRegenerateModal()" 
                            class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        Generate New Codes
                    </button>
                </div>
            </div>
        </div>

        <!-- Disable 2FA -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Disable Two-Factor Authentication</h3>
                </div>
            </div>
            <div class="p-6">
                <p class="text-gray-600 mb-4">
                    Disabling two-factor authentication will make your account less secure. You can re-enable it at any time.
                </p>
                <button onclick="showDisableModal()" 
                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    Disable Two-Factor Authentication
                </button>
            </div>
        </div>
    </div>

    <!-- Disable Modal -->
    <div id="disableModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Disable Two-Factor Authentication</h3>
            <p class="text-gray-600 mb-4">
                Please enter your password to confirm that you want to disable two-factor authentication.
            </p>
            
            <form method="POST" action="{{ route('admin.two-factor.disable') }}">
                @csrf
                <div class="mb-4">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input type="password" id="password" name="password" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="flex space-x-4">
                    <button type="submit" 
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                        Disable 2FA
                    </button>
                    <button type="button" onclick="hideDisableModal()" 
                            class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Regenerate Modal -->
    <div id="regenerateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Generate New Recovery Codes</h3>
            <p class="text-gray-600 mb-4">
                This will invalidate your current recovery codes and generate new ones. Please enter your password to confirm.
            </p>
            
            <form method="POST" action="{{ route('admin.two-factor.recovery-codes') }}">
                @csrf
                <div class="mb-4">
                    <label for="regenerate_password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input type="password" id="regenerate_password" name="password" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="flex space-x-4">
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                        Generate New Codes
                    </button>
                    <button type="button" onclick="hideRegenerateModal()" 
                            class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function showDisableModal() {
            document.getElementById('disableModal').classList.remove('hidden');
            document.getElementById('disableModal').classList.add('flex');
        }

        function hideDisableModal() {
            document.getElementById('disableModal').classList.add('hidden');
            document.getElementById('disableModal').classList.remove('flex');
        }

        function showRegenerateModal() {
            document.getElementById('regenerateModal').classList.remove('hidden');
            document.getElementById('regenerateModal').classList.add('flex');
        }

        function hideRegenerateModal() {
            document.getElementById('regenerateModal').classList.add('hidden');
            document.getElementById('regenerateModal').classList.remove('flex');
        }

        function downloadRecoveryCodes() {
            const codes = @json(session('new_recovery_codes', $recoveryCodes));
            const content = 'Two-Factor Authentication Recovery Codes\n' +
                           'Generated: ' + new Date().toLocaleString() + '\n\n' +
                           codes.join('\n') + '\n\n' +
                           'Keep these codes safe and secure!';
            
            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'recovery-codes.txt';
            a.click();
            window.URL.revokeObjectURL(url);
        }
    </script>
</x-layouts.admin>
