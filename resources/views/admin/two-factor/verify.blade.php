<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Two-Factor Authentication - {{ config('app.name') }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <!-- Logo -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-white rounded-full shadow-lg mb-4">
                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">Two-Factor Authentication</h1>
            <p class="text-blue-100">Enter the verification code from your authenticator app</p>
        </div>

        <!-- Main Card -->
        <div class="bg-white rounded-2xl shadow-2xl p-8">
            <!-- User Info -->
            <div class="text-center mb-6">
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span class="text-blue-600 font-semibold text-lg">{{ substr(auth()->user()->name, 0, 1) }}</span>
                </div>
                <h2 class="text-lg font-semibold text-gray-900">{{ auth()->user()->name }}</h2>
                <p class="text-sm text-gray-500">{{ auth()->user()->email }}</p>
            </div>

            <!-- Verification Form -->
            <form id="verify-form" class="space-y-6">
                <div>
                    <label for="code" class="block text-sm font-medium text-gray-700 mb-2">
                        Verification Code
                    </label>
                    <input 
                        type="text" 
                        id="code" 
                        name="code" 
                        maxlength="6" 
                        placeholder="123456" 
                        class="w-full px-4 py-3 text-center text-2xl font-mono border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        autocomplete="off"
                        autofocus
                    >
                </div>

                <button 
                    type="submit" 
                    class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                >
                    Verify
                </button>
            </form>

            <!-- Alternative Options -->
            <div class="mt-6 space-y-3">
                <div class="text-center">
                    <button 
                        onclick="showRecoveryForm()" 
                        class="text-sm text-blue-600 hover:text-blue-800 underline"
                    >
                        Use recovery code instead
                    </button>
                </div>
                
                <div class="text-center">
                    <button 
                        onclick="sendBackupCode()" 
                        class="text-sm text-gray-600 hover:text-gray-800 underline"
                    >
                        Send backup code to my email/phone
                    </button>
                </div>
            </div>

            <!-- Recovery Code Form (Hidden by default) -->
            <div id="recovery-form" class="hidden mt-6">
                <div class="border-t pt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Use Recovery Code</h3>
                    <form id="recovery-code-form" class="space-y-4">
                        <div>
                            <label for="recovery-code" class="block text-sm font-medium text-gray-700 mb-2">
                                Recovery Code
                            </label>
                            <input 
                                type="text" 
                                id="recovery-code" 
                                name="recovery_code" 
                                maxlength="8" 
                                placeholder="ABCD1234" 
                                class="w-full px-4 py-3 text-center text-lg font-mono border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                autocomplete="off"
                            >
                        </div>
                        <button 
                            type="submit" 
                            class="w-full bg-gray-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                        >
                            Verify Recovery Code
                        </button>
                    </form>
                    <div class="text-center mt-3">
                        <button 
                            onclick="hideRecoveryForm()" 
                            class="text-sm text-gray-600 hover:text-gray-800 underline"
                        >
                            Back to verification code
                        </button>
                    </div>
                </div>
            </div>

            <!-- Logout Option -->
            <div class="mt-8 pt-6 border-t text-center">
                <form method="POST" action="{{ route('logout') }}" class="inline">
                    @csrf
                    <button 
                        type="submit" 
                        class="text-sm text-gray-500 hover:text-gray-700 underline"
                    >
                        Sign out
                    </button>
                </form>
            </div>
        </div>

        <!-- Help Text -->
        <div class="text-center mt-6">
            <p class="text-blue-100 text-sm">
                Having trouble? Contact support for assistance.
            </p>
        </div>
    </div>

    <!-- Notification Toast -->
    <div id="notification" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
            <div class="flex items-center">
                <div id="notification-icon" class="flex-shrink-0 mr-3"></div>
                <div>
                    <p id="notification-title" class="text-sm font-medium text-gray-900"></p>
                    <p id="notification-message" class="text-sm text-gray-500"></p>
                </div>
                <button onclick="hideNotification()" class="ml-4 text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Show/Hide Recovery Form
        function showRecoveryForm() {
            document.getElementById('recovery-form').classList.remove('hidden');
            document.getElementById('recovery-code').focus();
        }

        function hideRecoveryForm() {
            document.getElementById('recovery-form').classList.add('hidden');
            document.getElementById('code').focus();
        }

        // Notification System
        function showNotification(type, title, message) {
            const notification = document.getElementById('notification');
            const icon = document.getElementById('notification-icon');
            const titleEl = document.getElementById('notification-title');
            const messageEl = document.getElementById('notification-message');

            titleEl.textContent = title;
            messageEl.textContent = message;

            if (type === 'success') {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                `;
            } else if (type === 'error') {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                `;
            } else {
                icon.innerHTML = `
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                `;
            }

            notification.classList.remove('hidden');
            setTimeout(() => {
                hideNotification();
            }, 5000);
        }

        function hideNotification() {
            document.getElementById('notification').classList.add('hidden');
        }

        // Verification Form
        document.getElementById('verify-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            
            submitButton.disabled = true;
            submitButton.textContent = 'Verifying...';
            
            try {
                const response = await fetch('{{ route("admin.two-factor.verify-code") }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json',
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showNotification('success', 'Success', data.message);
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                } else {
                    showNotification('error', 'Error', data.message);
                    document.getElementById('code').value = '';
                    document.getElementById('code').focus();
                }
            } catch (error) {
                showNotification('error', 'Error', 'An error occurred. Please try again.');
            } finally {
                submitButton.disabled = false;
                submitButton.textContent = originalText;
            }
        });

        // Recovery Code Form
        document.getElementById('recovery-code-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            
            submitButton.disabled = true;
            submitButton.textContent = 'Verifying...';
            
            try {
                const response = await fetch('{{ route("admin.two-factor.verify-backup-code") }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json',
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showNotification('success', 'Success', data.message);
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                } else {
                    showNotification('error', 'Error', data.message);
                    document.getElementById('recovery-code').value = '';
                    document.getElementById('recovery-code').focus();
                }
            } catch (error) {
                showNotification('error', 'Error', 'An error occurred. Please try again.');
            } finally {
                submitButton.disabled = false;
                submitButton.textContent = originalText;
            }
        });

        // Send Backup Code
        async function sendBackupCode() {
            try {
                const response = await fetch('{{ route("admin.two-factor.send-backup-code") }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json',
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showNotification('success', 'Success', data.message);
                } else {
                    showNotification('error', 'Error', data.message);
                }
            } catch (error) {
                showNotification('error', 'Error', 'An error occurred. Please try again.');
            }
        }

        // Auto-format inputs
        document.getElementById('code').addEventListener('input', function(e) {
            this.value = this.value.replace(/\D/g, '').substring(0, 6);
        });

        document.getElementById('recovery-code').addEventListener('input', function(e) {
            this.value = this.value.replace(/[^A-Z0-9]/g, '').substring(0, 8);
        });
    </script>
</body>
</html>
