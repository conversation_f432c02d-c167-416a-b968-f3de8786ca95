<div>
    <aside class="fixed inset-y-0 left-0 bg-white w-64 border-r">
        <div class="flex flex-col h-full">
            <!-- Logo -->
            <div class="h-16 flex items-center justify-center border-b">
                <h1 class="text-xl font-bold">{{ config('app.name') }}</h1>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 overflow-y-auto py-4">
                <x-nav-link href="{{ route('dashboard') }}" :active="request()->routeIs('dashboard')">
                    Dashboard
                </x-nav-link>

                @if(auth()->user()->hasRole('admin'))
                    <x-nav-link href="{{ route('admin.users') }}" :active="request()->routeIs('admin.users')">
                        Users Management
                    </x-nav-link>
                    <x-nav-link href="{{ route('admin.roles') }}" :active="request()->routeIs('admin.roles')">
                        Roles & Permissions
                    </x-nav-link>
                @endif

                @if(auth()->user()->hasRole('reseller'))
                    <x-nav-link href="{{ route('reseller.clients') }}" :active="request()->routeIs('reseller.clients')">
                        My Clients
                    </x-nav-link>
                @endif

                <x-nav-link href="{{ route('profile') }}" :active="request()->routeIs('profile')">
                    Profile
                </x-nav-link>
            </nav>

            <!-- User Info -->
            <div class="border-t p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <img class="h-8 w-8 rounded-full" src="{{ auth()->user()->profile_photo_url }}" alt="{{ auth()->user()->name }}">
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-700">{{ auth()->user()->name }}</p>
                        <p class="text-xs text-gray-500">{{ auth()->user()->email }}</p>
                    </div>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <div class="pl-64">
        <!-- Top Navigation -->
        <header class="bg-white shadow h-16 flex items-center justify-between px-6">
            <h2 class="text-xl font-semibold text-gray-800">
                {{ $header ?? 'Dashboard' }}
            </h2>

            <!-- Logout Button -->
            <form method="POST" action="{{ route('logout') }}">
                @csrf
                <button type="submit" class="text-sm text-gray-600 hover:text-gray-900">
                    Logout
                </button>
            </form>
        </header>

        <!-- Page Content -->
        <main class="p-6">
            {{ $slot }}
        </main>
    </div>
</div>
