<div class="flex justify-between items-center mb-8">
    <div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2 relative">
            <span class="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600">Dashboard Overview</span>
            <span class="absolute -bottom-1 left-0 w-20 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full"></span>
        </h1>
        <p class="text-gray-600">Monitor your system performance and user statistics</p>
    </div>
    <div>
        <button wire:click="refreshData" wire:loading.attr="disabled" class="flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 group">
            <div wire:loading wire:target="refreshData" class="mr-2">
                <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
            <svg wire:loading.remove wire:target="refreshData" class="w-5 h-5 mr-2 transition-transform duration-500 ease-in-out group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            <span>Refresh Dashboard</span>
        </button>
    </div>
</div>
