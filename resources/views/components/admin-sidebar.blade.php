<!-- Include Admin Theme CSS -->
<link rel="stylesheet" href="{{ asset('css/admin-theme.css') }}">

<!-- Modern Dark Purple Admin Sidebar -->
<div class="admin-theme flex flex-col w-64 bg-primary shadow-2xl relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, rgba(157, 42, 232, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(203, 134, 253, 0.05) 0%, transparent 50%);"></div>
    </div>

    <!-- Logo Section -->
    <div class="relative flex items-center justify-center h-16 px-4 gradient-primary shadow-lg border-b border-purple-500/30">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 gradient-accent rounded-lg flex items-center justify-center shadow-lg glow-animation">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                </svg>
            </div>
            <span class="text-primary font-bold text-lg text-gradient">Hosting Platform</span>
        </div>
    </div>

    <!-- Enhanced Search Bar -->
    <div class="relative px-4 py-4 border-b border-purple-500/30">
        <div class="relative group">
            <input type="text"
                   placeholder="Search menu..."
                   class="input-field w-full rounded-2xl px-4 py-3 pl-12 pr-4 transition-all duration-300"
                   id="sidebar-search">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-secondary transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>
            <!-- Search suggestions dropdown (hidden by default) -->
            <div id="search-suggestions" class="absolute top-full left-0 right-0 mt-2 card-bg rounded-xl border border-purple-500/30 shadow-xl z-50 hidden">
                <div class="p-2 space-y-1">
                    <div class="px-3 py-2 text-secondary text-xs font-medium uppercase tracking-wider">Quick Access</div>
                    <a href="#" class="block px-3 py-2 text-primary hover:bg-purple-500/20 rounded-lg transition-colors duration-200">Dashboard</a>
                    <a href="#" class="block px-3 py-2 text-primary hover:bg-purple-500/20 rounded-lg transition-colors duration-200">Domains</a>
                    <a href="#" class="block px-3 py-2 text-primary hover:bg-purple-500/20 rounded-lg transition-colors duration-200">Servers</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="relative flex-1 px-4 py-4 space-y-1 overflow-y-auto sidebar-scroll">
        <!-- Dashboard -->
        <a href="{{ route('admin.dashboard') }}" class="sidebar-item group flex items-center px-4 py-3 text-white rounded-xl transition-all duration-300 {{ request()->routeIs('admin.dashboard') ? 'bg-gradient-to-r from-purple-500/30 to-indigo-500/30 shadow-lg border border-purple-400/30 backdrop-blur-sm' : 'hover:bg-purple-700/30 hover:shadow-md hover:border hover:border-purple-500/20' }}">
            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-purple-400 to-indigo-400 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
            </div>
            <span class="font-medium">Dashboard</span>
        </a>

        <!-- Domains -->
        <a href="{{ route('admin.domains.index') }}" class="sidebar-item group flex items-center px-4 py-3 text-white rounded-xl transition-all duration-300 {{ request()->routeIs('admin.domains.*') ? 'bg-gradient-to-r from-purple-500/30 to-indigo-500/30 shadow-lg border border-purple-400/30 backdrop-blur-sm' : 'hover:bg-purple-700/30 hover:shadow-md hover:border hover:border-purple-500/20' }}">
            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-400 to-purple-400 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                </svg>
            </div>
            <span class="font-medium">Domains</span>
        </a>

        <!-- Servers -->
        <a href="{{ route('admin.servers.index') }}" class="sidebar-item group flex items-center px-4 py-3 text-white rounded-xl transition-all duration-300 {{ request()->routeIs('admin.servers.*') ? 'bg-gradient-to-r from-purple-500/30 to-indigo-500/30 shadow-lg border border-purple-400/30 backdrop-blur-sm' : 'hover:bg-purple-700/30 hover:shadow-md hover:border hover:border-purple-500/20' }}">
            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-green-400 to-blue-400 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                </svg>
            </div>
            <span class="font-medium">Servers</span>
        </a>

        <!-- Applications -->
        <a href="{{ route('admin.applications.index') }}" class="sidebar-item group flex items-center px-4 py-3 text-white rounded-xl transition-all duration-300 {{ request()->routeIs('admin.applications.*') ? 'bg-gradient-to-r from-purple-500/30 to-indigo-500/30 shadow-lg border border-purple-400/30 backdrop-blur-sm' : 'hover:bg-purple-700/30 hover:shadow-md hover:border hover:border-purple-500/20' }}">
            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-orange-400 to-red-400 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
            </div>
            <span class="font-medium">Applications</span>
        </a>

        <!-- Databases -->
        <a href="{{ route('admin.databases.index') }}" class="sidebar-item group flex items-center px-4 py-3 text-white rounded-xl transition-all duration-300 {{ request()->routeIs('admin.databases.*') ? 'bg-gradient-to-r from-purple-500/30 to-indigo-500/30 shadow-lg border border-purple-400/30 backdrop-blur-sm' : 'hover:bg-purple-700/30 hover:shadow-md hover:border hover:border-purple-500/20' }}">
            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-teal-400 to-green-400 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                </svg>
            </div>
            <span class="font-medium">Databases</span>
        </a>

        <!-- Backups -->
        <a href="{{ route('admin.backups.index') }}" class="sidebar-item group flex items-center px-4 py-3 text-white rounded-xl transition-all duration-300 {{ request()->routeIs('admin.backups.*') ? 'bg-gradient-to-r from-purple-500/30 to-indigo-500/30 shadow-lg border border-purple-400/30 backdrop-blur-sm' : 'hover:bg-purple-700/30 hover:shadow-md hover:border hover:border-purple-500/20' }}">
            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-yellow-400 to-orange-400 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                </svg>
            </div>
            <span class="font-medium">Backups</span>
        </a>

        <!-- File Manager -->
        <a href="{{ route('admin.simple-file-manager.index') }}" class="sidebar-item group flex items-center px-4 py-3 text-white rounded-xl transition-all duration-300 {{ request()->routeIs('admin.simple-file-manager.*') ? 'bg-gradient-to-r from-purple-500/30 to-indigo-500/30 shadow-lg border border-purple-400/30 backdrop-blur-sm' : 'hover:bg-purple-700/30 hover:shadow-md hover:border hover:border-purple-500/20' }}">
            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-pink-400 to-purple-400 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z"></path>
                </svg>
            </div>
            <span class="font-medium group-hover:text-purple-200 transition-colors duration-300">File Manager</span>
            <div class="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <svg class="w-4 h-4 text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </div>
        </a>

        <!-- Integration -->
        <a href="{{ route('admin.integration.index') }}" class="sidebar-item group flex items-center px-4 py-3 text-white rounded-xl transition-all duration-300 {{ request()->routeIs('admin.integration.*') ? 'bg-gradient-to-r from-purple-500/30 to-indigo-500/30 shadow-lg border border-purple-400/30 backdrop-blur-sm' : 'hover:bg-purple-700/30 hover:shadow-md hover:border hover:border-purple-500/20' }}">
            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-cyan-400 to-blue-400 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            </div>
            <span class="font-medium group-hover:text-purple-200 transition-colors duration-300">Integration</span>
            <div class="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <svg class="w-4 h-4 text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </div>
        </a>

        <!-- Team Management -->
        <a href="{{ route('admin.team.index') }}" class="sidebar-item group flex items-center px-4 py-3 text-white rounded-xl transition-all duration-300 {{ request()->routeIs('admin.team.*') ? 'bg-gradient-to-r from-purple-500/30 to-indigo-500/30 shadow-lg border border-purple-400/30 backdrop-blur-sm' : 'hover:bg-purple-700/30 hover:shadow-md hover:border hover:border-purple-500/20' }}">
            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-indigo-400 to-purple-400 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
            </div>
            <span class="font-medium group-hover:text-purple-200 transition-colors duration-300">Team Management</span>
            <div class="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <svg class="w-4 h-4 text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </div>
        </a>

        <!-- Billing & Plan Section -->
        <div class="px-4 py-3 mt-6">
            <h3 class="text-purple-300 text-xs font-semibold uppercase tracking-wider flex items-center">
                <div class="w-4 h-0.5 bg-gradient-to-r from-purple-400 to-transparent mr-2"></div>
                Billing & Plan
            </h3>
        </div>

        <!-- Billing Dashboard -->
        <a href="{{ route('admin.billing.dashboard') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.billing.*') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
            </svg>
            <span class="font-medium">Billing Dashboard</span>
        </a>

        <!-- Payment Methods -->
        <a href="{{ route('admin.payments.methods') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.payments.methods*') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
            </svg>
            <span class="font-medium">Payment Methods</span>
        </a>

        <!-- Transactions -->
        <a href="{{ route('admin.payments.transactions') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.payments.transactions*') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
            </svg>
            <span class="font-medium">Transactions</span>
        </a>

        <!-- Add-ons Section -->
        <div class="px-4 py-3 mt-4">
            <h3 class="text-blue-200 text-xs font-semibold uppercase tracking-wider">Add-ons</h3>
        </div>

        <!-- InsightHub -->
        <a href="{{ route('admin.insights.index') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.insights.*') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <span class="font-medium">InsightHub</span>
        </a>

        <!-- White-Label -->
        <a href="{{ route('admin.white-label.index') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.white-label.*') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
            </svg>
            <span class="font-medium">White-Label</span>
        </a>

        <!-- Reseller Panel -->
        <a href="{{ route('admin.reseller.index') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.reseller.*') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
            </svg>
            <span class="font-medium">Reseller Panel</span>
        </a>

        <!-- Premium Hosting Care -->
        <a href="{{ route('admin.premium-care.index') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.premium-care.*') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
            </svg>
            <span class="font-medium">Premium Hosting Care</span>
            <span class="ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">New</span>
        </a>

        <!-- Docs Section -->
        <div class="px-4 py-3 mt-4">
            <h3 class="text-blue-200 text-xs font-semibold uppercase tracking-wider">Docs</h3>
        </div>

        <!-- Documentation -->
        <a href="{{ route('admin.documentation.index') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.documentation.*') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <span class="font-medium">Documentation</span>
        </a>

        <!-- API Documentation -->
        <a href="{{ route('admin.api-docs.index') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.api-docs.*') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
            </svg>
            <span class="font-medium">API Documentation</span>
        </a>

        <!-- Refer & Earn Section -->
        <div class="px-4 py-3 mt-4">
            <h3 class="text-blue-200 text-xs font-semibold uppercase tracking-wider">Refer & Earn</h3>
        </div>

        <!-- Referral Program -->
        <a href="{{ route('admin.referral.index') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.referral.*') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <span class="font-medium">Referral Program</span>
        </a>

        <!-- Affiliate Program -->
        <a href="{{ route('admin.affiliate.index') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.affiliate.*') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z"></path>
            </svg>
            <span class="font-medium">Affiliate Program</span>
        </a>

        <!-- Divider -->
        <div class="border-t border-purple-700/50 my-6 mx-4"></div>


        <!-- User Management Section -->
        <div class="px-4 py-3">
            <h3 class="text-purple-300 text-xs font-semibold uppercase tracking-wider flex items-center">
                <div class="w-4 h-0.5 bg-gradient-to-r from-purple-400 to-transparent mr-2"></div>
                User Management
            </h3>
        </div>

        <!-- Users -->
        <a href="{{ route('admin.users.index') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.users.*') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <span class="font-medium">Users</span>
            @if(class_exists('\App\Models\User'))
                <span class="ml-auto bg-white bg-opacity-20 text-xs px-2 py-1 rounded-full">{{ \App\Models\User::count() }}</span>
            @endif
        </a>

        <!-- Roles -->
        <a href="{{ route('admin.roles') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.roles') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            <span class="font-medium">Roles & Permissions</span>
        </a>

        <!-- Settings -->
        <a href="{{ route('admin.settings') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.settings') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <span class="font-medium">Settings</span>
        </a>

        <!-- Two-Factor Authentication -->
        <a href="{{ route('admin.two-factor.index') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.two-factor.*') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            <span class="font-medium">Two-Factor Auth</span>
        </a>

        <!-- Profile -->
        <a href="{{ route('admin.profile') }}" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg transition-all duration-200 {{ request()->routeIs('admin.profile') ? 'bg-white bg-opacity-20 shadow-lg' : 'hover:bg-white hover:bg-opacity-10' }}">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span class="font-medium">Profile</span>
        </a>
    </nav>

    <!-- User Info Section -->
    <div class="relative px-4 py-4 border-t border-purple-700/50 bg-gradient-to-r from-purple-900/50 to-indigo-900/50 backdrop-blur-sm">
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.profile') }}" class="relative w-12 h-12 bg-gradient-to-br from-purple-400 to-indigo-400 rounded-xl flex items-center justify-center hover:scale-105 transition-all duration-300 shadow-lg">
                <span class="text-white font-bold text-lg">{{ substr(auth()->user()->name, 0, 1) }}</span>
                <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-purple-900 status-online"></div>
            </a>
            <div class="flex-1 min-w-0">
                <a href="{{ route('admin.profile') }}" class="block group">
                    <p class="text-white font-semibold truncate group-hover:text-purple-200 transition-colors">{{ auth()->user()->name }}</p>
                    <p class="text-purple-300 text-sm truncate flex items-center">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                        Admin User
                    </p>
                </a>
            </div>
            <form method="POST" action="{{ route('logout') }}">
                @csrf
                <button type="submit" class="p-2 text-purple-300 hover:text-white hover:bg-purple-700/30 rounded-lg transition-all duration-300 group" title="Logout">
                    <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Enhanced Sidebar JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('sidebar-search');
    const searchSuggestions = document.getElementById('search-suggestions');
    const sidebarItems = document.querySelectorAll('.sidebar-item');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();

            if (query.length > 0) {
                searchSuggestions.classList.remove('hidden');

                // Filter sidebar items
                sidebarItems.forEach(item => {
                    const text = item.textContent.toLowerCase();
                    if (text.includes(query)) {
                        item.style.display = 'flex';
                        item.classList.add('bg-purple-700/20');
                    } else {
                        item.style.display = 'none';
                        item.classList.remove('bg-purple-700/20');
                    }
                });
            } else {
                searchSuggestions.classList.add('hidden');
                sidebarItems.forEach(item => {
                    item.style.display = 'flex';
                    item.classList.remove('bg-purple-700/20');
                });
            }
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchSuggestions.contains(e.target)) {
                searchSuggestions.classList.add('hidden');
            }
        });
    }

    // Add ripple effect to sidebar items
    sidebarItems.forEach(item => {
        item.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});
</script>

<style>
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.sidebar-item {
    position: relative;
    overflow: hidden;
}
</style>
