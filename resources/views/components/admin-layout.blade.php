<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title ?? 'Admin Dashboard - Hosting Platform' }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'purple': {
                            50: '#faf5ff',
                            100: '#f3e8ff',
                            200: '#e9d5ff',
                            300: '#d8b4fe',
                            400: '#c084fc',
                            500: '#a855f7',
                            600: '#9333ea',
                            700: '#7c3aed',
                            800: '#6b21a8',
                            900: '#581c87',
                            950: '#3b0764'
                        },
                        'indigo': {
                            50: '#eef2ff',
                            100: '#e0e7ff',
                            200: '#c7d2fe',
                            300: '#a5b4fc',
                            400: '#818cf8',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3',
                            900: '#312e81',
                            950: '#1e1b4b'
                        }
                    }
                }
            }
        }
    </script>

    <!-- Modern Dashboard Styles -->
    <style>
        [x-cloak] { display: none !important; }

        /* Custom Properties for Purple Dark Theme */
        :root {
            --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
            --secondary-gradient: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 50%, #a855f7 100%);
            --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            --danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            --info-gradient: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            --dark-gradient: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #4c1d95 100%);

            --shadow-soft: 0 10px 25px rgba(139, 92, 246, 0.15);
            --shadow-medium: 0 15px 35px rgba(139, 92, 246, 0.2);
            --shadow-strong: 0 20px 40px rgba(139, 92, 246, 0.25);

            --border-radius-sm: 8px;
            --border-radius-md: 12px;
            --border-radius-lg: 16px;
            --border-radius-xl: 20px;

            --transition-fast: all 0.2s ease;
            --transition-medium: all 0.3s ease;
            --transition-slow: all 0.5s ease;
        }

        /* Custom scrollbar for sidebar */
        .sidebar-scroll::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar-scroll::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        .sidebar-scroll::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
        }

        .sidebar-scroll::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Modern Card Styles */
        .modern-card {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-soft);
            transition: var(--transition-medium);
            overflow: hidden;
            position: relative;
        }

        .modern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: var(--transition-medium);
        }

        .modern-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-strong);
        }

        .modern-card:hover::before {
            transform: scaleX(1);
        }

        /* Gradient Cards */
        .gradient-card-primary {
            background: var(--primary-gradient);
            color: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-medium);
            transition: var(--transition-medium);
            position: relative;
            overflow: hidden;
        }

        .gradient-card-secondary {
            background: var(--secondary-gradient);
            color: white;
        }

        .gradient-card-success {
            background: var(--success-gradient);
            color: white;
        }

        .gradient-card-warning {
            background: var(--warning-gradient);
            color: white;
        }

        .gradient-card-danger {
            background: var(--danger-gradient);
            color: white;
        }

        .gradient-card-info {
            background: var(--info-gradient);
            color: white;
        }

        .gradient-card-dark {
            background: var(--dark-gradient);
            color: white;
        }

        /* Floating Animation */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        /* Pulse Animation */
        @keyframes pulse-glow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(139, 92, 246, 0.4);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 30px rgba(139, 92, 246, 0.6);
                transform: scale(1.02);
            }
        }

        .pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite;
        }

        /* Number Counter Animation */
        @keyframes countUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .counter-number {
            animation: countUp 0.8s ease-out;
            font-weight: 700;
            line-height: 1;
        }

        /* Icon Animations */
        .icon-bounce {
            transition: var(--transition-fast);
        }

        .icon-bounce:hover {
            transform: scale(1.2) rotate(5deg);
        }

        /* Progress Bars */
        .progress-bar {
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #a855f7 0%, #c084fc 100%);
            border-radius: 4px;
            transition: width 1s ease-out;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Hover Effects */
        .hover-lift {
            transition: var(--transition-medium);
        }

        .hover-lift:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .hover-glow {
            transition: var(--transition-medium);
        }

        .hover-glow:hover {
            box-shadow: 0 0 25px rgba(139, 92, 246, 0.3);
        }

        /* Dashboard Grid System */
        .dashboard-grid {
            display: grid;
            gap: 1.5rem;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        }

        @media (min-width: 768px) {
            .dashboard-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (min-width: 1280px) {
            .dashboard-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        /* Modern Buttons */
        .btn-modern {
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: var(--border-radius-md);
            padding: 12px 24px;
            font-weight: 600;
            transition: var(--transition-medium);
            position: relative;
            overflow: hidden;
        }

        .btn-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: var(--transition-medium);
        }

        .btn-modern:hover::before {
            left: 100%;
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        /* Custom animations */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-slide-in {
            animation: slideIn 0.3s ease-out;
        }

        /* Notification styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 400px;
        }

        /* Dashboard Card Hover Effects */
        .dashboard-card {
            transition: all 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgba(139, 92, 246, 0.15), 0 10px 10px -5px rgba(139, 92, 246, 0.1);
        }

        /* Stats Icon Animation */
        .stats-icon {
            transition: all 0.3s ease;
        }

        .dashboard-card:hover .stats-icon {
            transform: scale(1.1) rotate(5deg);
        }

        /* Sidebar Item Hover */
        .sidebar-item {
            transition: all 0.2s ease-in-out;
        }

        .sidebar-item:hover {
            transform: translateX(4px);
        }

        /* Loading Animation */
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: var(--border-radius-sm);
        }

        /* Status Indicators */
        .status-online {
            position: relative;
        }

        .status-online::before {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse-dot 2s infinite;
        }

        @keyframes pulse-dot {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
        }
    </style>
</head>
<body class="font-inter antialiased bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50">
    <div class="flex h-screen overflow-hidden" x-data="{ sidebarOpen: false }">
        <!-- Mobile sidebar overlay -->
        <div x-show="sidebarOpen"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
             @click="sidebarOpen = false">
        </div>

        <!-- Mobile sidebar -->
        <div x-show="sidebarOpen"
             x-transition:enter="transition ease-in-out duration-300 transform"
             x-transition:enter-start="-translate-x-full"
             x-transition:enter-end="translate-x-0"
             x-transition:leave="transition ease-in-out duration-300 transform"
             x-transition:leave-start="translate-x-0"
             x-transition:leave-end="-translate-x-full"
             class="fixed inset-y-0 left-0 z-50 w-64 lg:hidden">
            <x-admin-sidebar />
        </div>

        <!-- Desktop sidebar -->
        <div class="hidden lg:flex lg:flex-shrink-0">
            <x-admin-sidebar />
        </div>

        <!-- Main content area -->
        <div class="flex flex-col w-0 flex-1 overflow-hidden">
            <!-- Modern Purple Top Navigation Bar -->
            <div class="relative z-10 flex-shrink-0 flex h-16 bg-white/80 backdrop-blur-sm shadow-lg border-b border-purple-200/50">
                <!-- Mobile menu button -->
                <button @click="sidebarOpen = true"
                        class="px-4 border-r border-purple-200/50 text-purple-600 hover:text-purple-700 hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-purple-500 lg:hidden transition-all duration-300">
                    <span class="sr-only">Open sidebar</span>
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
                    </svg>
                </button>

                <!-- Top bar content -->
                <div class="flex-1 px-6 flex justify-between items-center">
                    <!-- Page title -->
                    <div class="flex-1 flex">
                        <h1 class="text-xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">{{ $title ?? 'Dashboard' }}</h1>
                    </div>

                    <!-- Right side items -->
                    <div class="ml-4 flex items-center space-x-4">
                        <!-- Notifications -->
                        <button class="p-2 text-purple-500 hover:text-purple-600 hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-purple-500 rounded-xl relative transition-all duration-300">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z" />
                            </svg>
                            <span class="absolute -top-1 -right-1 block h-3 w-3 rounded-full bg-red-400 animate-pulse"></span>
                        </button>

                        <!-- User menu -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open"
                                    class="flex items-center space-x-3 p-2 rounded-xl hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-300">
                                <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg">
                                    <span class="text-white font-semibold text-sm">{{ substr(auth()->user()->name, 0, 1) }}</span>
                                </div>
                                <span class="hidden md:block text-sm font-semibold text-gray-700">{{ auth()->user()->name }}</span>
                                <svg class="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- Dropdown menu -->
                            <div x-show="open"
                                 @click.away="open = false"
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 class="absolute right-0 mt-2 w-48 bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-purple-200/50 py-2 z-50">
                                <a href="{{ route('admin.profile') }}" class="block px-4 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-600 rounded-xl mx-2 transition-all duration-300">Profile Settings</a>
                                <a href="{{ route('admin.account') }}" class="block px-4 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-600 rounded-xl mx-2 transition-all duration-300">Account Settings</a>
                                <div class="border-t border-purple-100 my-2"></div>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="block w-full text-left px-4 py-3 text-sm text-red-600 hover:bg-red-50 rounded-xl mx-2 transition-all duration-300">
                                        Sign out
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main content with Purple Background -->
            <main class="flex-1 relative overflow-y-auto focus:outline-none bg-gradient-to-br from-purple-50/50 via-indigo-50/30 to-pink-50/50">
                <div class="py-6">
                    {{ $slot }}
                </div>
            </main>
        </div>
    </div>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Auto-hide notifications
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(notification => {
                setTimeout(() => {
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }, 5000);
            });
        });

        // Real-time clock
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const clockElement = document.getElementById('live-clock');
            if (clockElement) {
                clockElement.textContent = timeString;
            }
        }

        setInterval(updateClock, 1000);
        updateClock(); // Initial call
    </script>
</body>
</html>
