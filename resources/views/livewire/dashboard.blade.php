<div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-lg shadow-lg">
    <x-dashboard-header />
    
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-2 relative">
            <span class="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600">Dashboard Overview</span>
            <span class="absolute -bottom-1 left-0 w-20 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full"></span>
        </h1>
        <p class="text-gray-600">Monitor your system performance and user statistics</p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8" wire:poll.{{ $refreshInterval }}ms>
        <!-- CPU Usage Card -->
        <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden border border-blue-100 transform hover:-translate-y-1">
            <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4 flex items-center">
                <svg class="w-8 h-8 text-white mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <h3 class="text-xl font-semibold text-white">CPU Usage</h3>
            </div>
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="text-sm font-medium text-gray-500">Current Usage</div>
                    <div class="text-2xl font-bold {{ $this->systemInfo['cpu'] > 80 ? 'text-red-600' : ($this->systemInfo['cpu'] > 50 ? 'text-yellow-600' : 'text-green-600') }}">
                        {{ $this->systemInfo['cpu'] }}%
                    </div>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-4">
                    <div class="{{ $this->systemInfo['cpu'] > 80 ? 'bg-gradient-to-r from-red-500 to-red-600' : ($this->systemInfo['cpu'] > 50 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500' : 'bg-gradient-to-r from-green-400 to-green-500') }} h-4 rounded-full transition-all duration-500 ease-in-out progress-bar-animated" style="width: {{ $this->systemInfo['cpu'] }}%">
                    </div>
                </div>
                <div class="mt-3 flex justify-end">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $this->systemInfo['cpu'] > 80 ? 'bg-red-100 text-red-800' : ($this->systemInfo['cpu'] > 50 ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800') }}">
                        {{ $this->systemInfo['cpu'] > 80 ? 'High' : ($this->systemInfo['cpu'] > 50 ? 'Moderate' : 'Normal') }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Memory Usage Card -->
        <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden border border-purple-100 transform hover:-translate-y-1">
            <div class="bg-gradient-to-r from-purple-600 to-indigo-600 px-6 py-4 flex items-center">
                <svg class="w-8 h-8 text-white mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <h3 class="text-xl font-semibold text-white">Memory Usage</h3>
            </div>
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex flex-col">
                        <span class="text-sm font-medium text-gray-500">Used</span>
                        <span class="text-lg font-bold text-gray-700">{{ $this->systemInfo['memory']['used'] }} MB</span>
                    </div>
                    <div class="flex flex-col items-end">
                        <span class="text-sm font-medium text-gray-500">Total</span>
                        <span class="text-lg font-bold text-gray-700">{{ $this->systemInfo['memory']['total'] }} MB</span>
                    </div>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-4">
                    <div class="{{ $this->systemInfo['memory']['percent_used'] > 80 ? 'bg-gradient-to-r from-red-500 to-red-600' : ($this->systemInfo['memory']['percent_used'] > 50 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500' : 'bg-gradient-to-r from-purple-400 to-purple-500') }} h-4 rounded-full transition-all duration-500 ease-in-out progress-bar-animated" style="width: {{ $this->systemInfo['memory']['percent_used'] }}%">
                    </div>
                </div>
                <div class="mt-3 flex justify-between items-center">
                    <div class="text-sm text-gray-500">Free: {{ $this->systemInfo['memory']['total'] - $this->systemInfo['memory']['used'] }} MB</div>
                    <span class="text-lg font-bold {{ $this->systemInfo['memory']['percent_used'] > 80 ? 'text-red-600' : ($this->systemInfo['memory']['percent_used'] > 50 ? 'text-yellow-600' : 'text-purple-600') }}">
                        {{ $this->systemInfo['memory']['percent_used'] }}%
                    </span>
                </div>
            </div>
        </div>

        <!-- Disk Usage Card -->
        <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden border border-teal-100 transform hover:-translate-y-1">
            <div class="bg-gradient-to-r from-teal-600 to-green-600 px-6 py-4 flex items-center">
                <svg class="w-8 h-8 text-white mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                </svg>
                <h3 class="text-xl font-semibold text-white">Disk Usage</h3>
            </div>
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex flex-col">
                        <span class="text-sm font-medium text-gray-500">Used</span>
                        <span class="text-lg font-bold text-gray-700">{{ $this->systemInfo['disk']['used'] }}</span>
                    </div>
                    <div class="flex flex-col items-end">
                        <span class="text-sm font-medium text-gray-500">Total</span>
                        <span class="text-lg font-bold text-gray-700">{{ $this->systemInfo['disk']['size'] }}</span>
                    </div>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-4">
                    <div class="{{ $this->systemInfo['disk']['percent_used'] > 80 ? 'bg-gradient-to-r from-red-500 to-red-600' : ($this->systemInfo['disk']['percent_used'] > 50 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500' : 'bg-gradient-to-r from-teal-400 to-teal-500') }} h-4 rounded-full transition-all duration-500 ease-in-out progress-bar-animated" style="width: {{ $this->systemInfo['disk']['percent_used'] }}%">
                    </div>
                </div>
                <div class="mt-3 flex justify-between items-center">
                    <div class="text-sm text-gray-500">Free: {{ $this->systemInfo['disk']['available'] }}</div>
                    <span class="text-lg font-bold {{ $this->systemInfo['disk']['percent_used'] > 80 ? 'text-red-600' : ($this->systemInfo['disk']['percent_used'] > 50 ? 'text-yellow-600' : 'text-teal-600') }}">
                        {{ $this->systemInfo['disk']['percent_used'] }}%
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Server Information Card -->
    <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 mb-8 overflow-hidden border border-gray-100">
        <div class="bg-gradient-to-r from-gray-700 to-gray-800 px-6 py-4">
            <div class="flex items-center">
                <svg class="w-8 h-8 text-white mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                </svg>
                <h3 class="text-xl font-semibold text-white">Server Information</h3>
            </div>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-all duration-200 transform hover:scale-105">
                    <div class="flex items-center mb-3">
                        <svg class="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <h4 class="text-sm font-medium text-gray-700">Operating System</h4>
                    </div>
                    <p class="text-sm text-gray-900 font-semibold pl-7">{{ $this->systemInfo['linux_version'] }}</p>
                </div>
                
                <div class="bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-lg hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 animate-slide-in delay-200 shadow-md hover:shadow-lg border border-gray-200">
                    <div class="flex items-center mb-3">
                        <svg class="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                        </svg>
                        <h4 class="text-sm font-medium text-gray-700">PHP Version</h4>
                    </div>
                    <p class="text-sm text-gray-900 font-semibold pl-7">{{ $this->systemInfo['php_version'] }}</p>
                </div>
                
                <div class="bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-lg hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 animate-slide-in delay-300 shadow-md hover:shadow-lg border border-gray-200">
                    <div class="flex items-center mb-3">
                        <svg class="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <h4 class="text-sm font-medium text-gray-700">System Uptime</h4>
                    </div>
                    <p class="text-sm text-gray-900 font-semibold pl-7">{{ $this->systemInfo['uptime'] }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- User Statistics -->
    <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 mb-8 overflow-hidden border border-indigo-100">
        <div class="bg-gradient-to-r from-indigo-700 to-blue-700 px-6 py-4">
            <div class="flex items-center">
                <svg class="w-8 h-8 text-white mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <h3 class="text-xl font-semibold text-white">User Statistics</h3>
            </div>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-white p-6 rounded-xl text-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 border-t-4 border-indigo-500 animate-slide-in delay-100">
                    <div class="flex flex-col items-center">
                        <div class="p-4 rounded-full bg-indigo-100 mb-4 animate-pulse-slow">
                            <svg class="w-10 h-10 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                        </div>
                        <div class="text-4xl font-bold text-indigo-600 mb-2">{{ $this->stats['total_users'] }}</div>
                        <div class="text-sm font-medium text-gray-600">Total Users</div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-xl text-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 border-t-4 border-red-500 animate-slide-in delay-200">
                    <div class="flex flex-col items-center">
                        <div class="p-4 rounded-full bg-red-100 mb-4 animate-pulse-slow">
                            <svg class="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="text-4xl font-bold text-red-600 mb-2">{{ $this->stats['total_admins'] }}</div>
                        <div class="text-sm font-medium text-gray-600">Administrators</div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-xl text-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 border-t-4 border-green-500 animate-slide-in delay-300">
                    <div class="flex flex-col items-center">
                        <div class="p-4 rounded-full bg-green-100 mb-4 animate-pulse-slow">
                            <svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                        </div>
                        <div class="text-4xl font-bold text-green-600 mb-2">{{ $this->stats['total_resellers'] }}</div>
                        <div class="text-sm font-medium text-gray-600">Resellers</div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-xl text-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 border-t-4 border-blue-500 animate-slide-in delay-400">
                    <div class="flex flex-col items-center">
                        <div class="p-4 rounded-full bg-blue-100 mb-4 animate-pulse-slow">
                            <svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <div class="text-4xl font-bold text-blue-600 mb-2">{{ $this->stats['total_clients'] }}</div>
                        <div class="text-sm font-medium text-gray-600">Clients</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Information Card -->
    <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 mb-8 overflow-hidden border border-amber-100">
        <div class="bg-gradient-to-r from-amber-600 to-orange-600 px-6 py-4">
            <div class="flex items-center">
                <svg class="w-8 h-8 text-white mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <h3 class="text-xl font-semibold text-white">User Information</h3>
            </div>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gradient-to-br from-amber-50 to-amber-100 rounded-lg p-5 hover:shadow-md transition-all duration-300">
                    <div class="mb-4">
                        <div class="flex items-center mb-2">
                            <svg class="w-5 h-5 text-amber-700 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <h4 class="text-sm font-medium text-amber-800">Name</h4>
                        </div>
                        <p class="mt-1 text-base font-semibold text-gray-800 pl-7">{{ auth()->user()->name }}</p>
                    </div>
                    <div class="mb-4">
                        <div class="flex items-center mb-2">
                            <svg class="w-5 h-5 text-amber-700 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <h4 class="text-sm font-medium text-amber-800">Email</h4>
                        </div>
                        <p class="mt-1 text-base font-semibold text-gray-800 pl-7">{{ auth()->user()->email }}</p>
                    </div>
                </div>
                <div class="bg-gradient-to-br from-amber-50 to-amber-100 rounded-lg p-5 hover:shadow-md transition-all duration-300">
                    <div class="mb-4">
                        <div class="flex items-center mb-2">
                            <svg class="w-5 h-5 text-amber-700 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                            <h4 class="text-sm font-medium text-amber-800">Roles</h4>
                        </div>
                        <div class="mt-1 pl-7">
                            @forelse(auth()->user()->roles as $role)
                                <span class="inline-block bg-white bg-opacity-60 text-amber-800 text-xs px-3 py-1 rounded-full mr-1 mb-1 shadow-sm border border-amber-200">
                                    {{ $role->name }}
                                </span>
                            @empty
                                <span class="text-sm text-gray-600">No roles assigned</span>
                            @endforelse
                        </div>
                    </div>
                    <div class="mb-4">
                        <div class="flex items-center mb-2">
                            <svg class="w-5 h-5 text-amber-700 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <h4 class="text-sm font-medium text-amber-800">Account Created</h4>
                        </div>
                        <p class="mt-1 text-base font-semibold text-gray-800 pl-7">{{ auth()->user()->created_at->format('Y-m-d H:i') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Users -->
    <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden border border-violet-100">
        <div class="bg-gradient-to-r from-violet-600 to-purple-600 px-6 py-4">
            <div class="flex items-center">
                <svg class="w-8 h-8 text-white mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
                <h3 class="text-xl font-semibold text-white">Recent Users</h3>
            </div>
        </div>
        <div class="p-0">
            <div class="divide-y divide-gray-100">
                @foreach($this->recentUsers as $user)
                    <div class="p-4 hover:bg-violet-50 transition-colors duration-200">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 rounded-full bg-gradient-to-r from-violet-500 to-purple-600 flex items-center justify-center text-white shadow-lg">
                                    <span class="text-lg font-medium">
                                        {{ substr($user->name, 0, 1) }}
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-base font-medium text-gray-900">{{ $user->name }}</h4>
                                <p class="text-sm text-gray-500">{{ $user->email }}</p>
                            </div>
                            <div class="ml-auto">
                                @php
                                    $role = $user->roles->first();
                                    $roleSlug = $role ? $role->slug : 'client';
                                    $roleName = $role ? $role->name : 'Client';
                                    
                                    [$bgColor, $textColor, $ringColor] = match($roleSlug) {
                                        'admin' => ['bg-red-100', 'text-red-800', 'ring-red-400'],
                                        'reseller' => ['bg-green-100', 'text-green-800', 'ring-green-400'],
                                        default => ['bg-blue-100', 'text-blue-800', 'ring-blue-400']
                                    };
                                    
                                    $bgColorClass = "$bgColor $textColor ring-2 $ringColor";
                                @endphp
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium shadow-sm {{ $bgColorClass }}">
                                    {{ ucfirst($roleName) }}
                                </span>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            <div class="bg-gradient-to-r from-violet-50 to-purple-50 p-4 text-center border-t border-violet-100">
                <a href="#" class="text-sm font-medium text-violet-600 hover:text-violet-800 transition-colors duration-200">
                    View All Users
                    <svg class="w-4 h-4 inline-block ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                </a>
            </div>
        </div>
    </div>
</div>
