<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" x-data="settingsPage()">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                        System Settings
                    </span>
                </h1>
                <p class="text-gray-600 mt-1">Configure system preferences and application settings</p>
            </div>
            <div class="flex items-center space-x-4">
                <button
                    @click="saveSettings()"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2"
                >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span>Save Settings</span>
                </button>

                <!-- Debug Info -->
                <div class="text-sm text-gray-500">
                    Active Tab: <span x-text="activeTab"></span> | Site Name: <span x-text="siteName"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    @if(session('success'))
        <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center space-x-3">
            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="text-green-800">{{ session('success') }}</span>
        </div>
    @endif

    @if(session('error'))
        <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-3">
            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            <span class="text-red-800">{{ session('error') }}</span>
        </div>
    @endif

    <!-- Navigation Tabs -->
    <div class="mb-8">
        <nav class="flex space-x-8">
            <button
                @click="activeTab = 'general'"
                :class="activeTab === 'general' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
            >
                General Settings
            </button>
            <button
                @click="activeTab = 'security'"
                :class="activeTab === 'security' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
            >
                Security
            </button>
            <button
                @click="activeTab = 'email'"
                :class="activeTab === 'email' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
            >
                Email
            </button>
            <button
                @click="activeTab = '2fa'"
                :class="activeTab === '2fa' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
            >
                Two-Factor Auth
            </button>
        </nav>
    </div>

    <!-- Content -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100">
        <!-- General Settings Tab -->
        <div x-show="activeTab === 'general'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">General Settings</h3>
            </div>
            <div class="p-6 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="site_name" class="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
                        <input
                            type="text"
                            id="site_name"
                            x-model="siteName"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter site name"
                        >
                    </div>

                    <div>
                        <label for="admin_email" class="block text-sm font-medium text-gray-700 mb-2">Admin Email</label>
                        <input
                            type="email"
                            id="admin_email"
                            x-model="adminEmail"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="<EMAIL>"
                        >
                    </div>
                </div>

                <div>
                    <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
                    <textarea
                        id="site_description"
                        x-model="siteDescription"
                        rows="3"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter site description"
                    ></textarea>
                </div>

                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">Default Timezone</label>
                    <select
                        id="timezone"
                        x-model="timezone"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">Eastern Time (US & Canada)</option>
                        <option value="America/Chicago">Central Time (US & Canada)</option>
                        <option value="Europe/London">London</option>
                        <option value="Europe/Paris">Paris</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Security Settings Tab -->
        <div x-show="activeTab === 'security'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Security Settings</h3>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
                            <input
                                type="number"
                                x-model="sessionTimeout"
                                min="30"
                                max="1440"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="120"
                            >
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Password Length</label>
                            <input
                                type="number"
                                x-model="passwordMinLength"
                                min="6"
                                max="50"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="8"
                            >
                        </div>
                    </div>

                    <div class="space-y-3">
                        <h4 class="text-md font-medium text-gray-900">Password Requirements</h4>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" x-model="requireUppercase" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <span class="text-sm text-gray-700">Require uppercase letters</span>
                        </label>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" x-model="requireNumbers" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <span class="text-sm text-gray-700">Require numbers</span>
                        </label>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" x-model="requireSpecialChars" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <span class="text-sm text-gray-700">Require special characters</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Settings Tab -->
        <div x-show="activeTab === 'email'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Email Settings</h3>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">SMTP Host</label>
                            <input
                                type="text"
                                x-model="smtpHost"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="smtp.gmail.com"
                            >
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">SMTP Port</label>
                            <input
                                type="number"
                                x-model="smtpPort"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="587"
                            >
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">SMTP Username</label>
                            <input
                                type="text"
                                x-model="smtpUsername"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="<EMAIL>"
                            >
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">SMTP Password</label>
                            <input
                                type="password"
                                x-model="smtpPassword"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="••••••••"
                            >
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 2FA Settings Tab -->
        <div x-show="activeTab === '2fa'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Two-Factor Authentication</h3>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <span class="text-yellow-800 font-medium">Two-Factor Authentication is disabled</span>
                        </div>
                        <p class="text-yellow-700 text-sm mt-2">Enable 2FA to add an extra layer of security to your account.</p>
                    </div>

                    <button
                        @click="enable2FA()"
                        class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg flex items-center space-x-2 transition-colors duration-200"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                        <span>Enable Two-Factor Authentication</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Alpine.js Data -->
    <script>
        function settingsPage() {
            return {
                activeTab: 'general',

                // General Settings
                siteName: '{{ $site_name }}',
                siteDescription: '{{ $site_description }}',
                adminEmail: '{{ $admin_email }}',
                timezone: '{{ $timezone }}',

                // Security Settings
                sessionTimeout: 120,
                passwordMinLength: 8,
                requireUppercase: true,
                requireNumbers: true,
                requireSpecialChars: false,

                // Email Settings
                smtpHost: '',
                smtpPort: 587,
                smtpUsername: '',
                smtpPassword: '',

                // Methods
                saveSettings() {
                    console.log('Saving settings...');

                    // Create form data
                    const formData = new FormData();
                    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
                    formData.append('site_name', this.siteName);
                    formData.append('site_description', this.siteDescription);
                    formData.append('admin_email', this.adminEmail);
                    formData.append('timezone', this.timezone);

                    // Send AJAX request
                    fetch('{{ route("admin.settings.store") }}', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.showNotification('Settings saved successfully!', 'success');
                        } else {
                            this.showNotification('Failed to save settings', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        this.showNotification('An error occurred', 'error');
                    });
                },

                enable2FA() {
                    console.log('Enabling 2FA...');
                    this.showNotification('2FA feature will be available soon!', 'info');
                },

                showNotification(message, type = 'success') {
                    // Create notification element
                    const notification = document.createElement('div');
                    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

                    notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-4 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
                    notification.innerHTML = `
                        <div class="flex items-center space-x-3">
                            <span>${message}</span>
                            <button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    `;

                    document.body.appendChild(notification);

                    // Animate in
                    setTimeout(() => {
                        notification.classList.remove('translate-x-full');
                    }, 100);

                    // Auto remove after 5 seconds
                    setTimeout(() => {
                        notification.classList.add('translate-x-full');
                        setTimeout(() => {
                            if (notification.parentElement) {
                                notification.remove();
                            }
                        }, 300);
                    }, 5000);
                }
            }
        }
    </script>
</div>
