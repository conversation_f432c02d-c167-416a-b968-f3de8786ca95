/* Dashboard Animations */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideInFromTop {
  0% { transform: translateY(-20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 0 rgba(99, 102, 241, 0); }
  50% { box-shadow: 0 0 10px rgba(99, 102, 241, 0.5); }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

.animate-slide-in {
  animation: slideInFromTop 0.8s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s infinite ease-in-out;
}

.animate-glow {
  animation: glow 2.5s infinite ease-in-out;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-500 {
  animation-delay: 500ms;
}

/* Custom progress bar animations */
.progress-bar-animated {
  background-size: 30px 30px;
  background-image: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  animation: progress-bar-stripes 2s linear infinite;
}

@keyframes progress-bar-stripes {
  0% { background-position: 0 0; }
  100% { background-position: 60px 0; }
}
