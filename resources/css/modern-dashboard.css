/* Modern Dashboard Styles */

/* Custom Properties for Modern Design */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    
    --shadow-soft: 0 10px 25px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 15px 35px rgba(0, 0, 0, 0.15);
    --shadow-strong: 0 20px 40px rgba(0, 0, 0, 0.2);
    
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    
    --transition-fast: all 0.2s ease;
    --transition-medium: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* Glass Morphism Effect */
.glass-card {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: var(--shadow-soft);
    border-radius: var(--border-radius-lg);
    transition: var(--transition-medium);
}

.glass-card:hover {
    background: rgba(255, 255, 255, 0.35);
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

/* Modern Card Styles */
.modern-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-soft);
    transition: var(--transition-medium);
    overflow: hidden;
    position: relative;
}

.modern-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: var(--transition-medium);
}

.modern-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-strong);
}

.modern-card:hover::before {
    transform: scaleX(1);
}

/* Gradient Cards */
.gradient-card-primary {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-medium);
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.gradient-card-success {
    background: var(--success-gradient);
    color: white;
}

.gradient-card-warning {
    background: var(--warning-gradient);
    color: white;
}

.gradient-card-danger {
    background: var(--danger-gradient);
    color: white;
}

.gradient-card-info {
    background: var(--info-gradient);
    color: #333;
}

/* Floating Animation */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.floating {
    animation: float 3s ease-in-out infinite;
}

/* Pulse Animation */
@keyframes pulse-glow {
    0%, 100% { 
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
        transform: scale(1);
    }
    50% { 
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
        transform: scale(1.02);
    }
}

.pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
}

/* Number Counter Animation */
@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.counter-number {
    animation: countUp 0.8s ease-out;
    font-weight: 700;
    font-size: 2.5rem;
    line-height: 1;
}

/* Icon Animations */
.icon-bounce {
    transition: var(--transition-fast);
}

.icon-bounce:hover {
    transform: scale(1.2) rotate(5deg);
}

/* Progress Bars */
.progress-bar {
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
    border-radius: 4px;
    transition: width 1s ease-out;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Loading Skeleton */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--border-radius-sm);
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Hover Effects */
.hover-lift {
    transition: var(--transition-medium);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.hover-glow {
    transition: var(--transition-medium);
}

.hover-glow:hover {
    box-shadow: 0 0 25px rgba(102, 126, 234, 0.3);
}

/* Responsive Grid System */
.dashboard-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

@media (min-width: 768px) {
    .dashboard-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1280px) {
    .dashboard-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Status Indicators */
.status-online {
    position: relative;
}

.status-online::before {
    content: '';
    position: absolute;
    width: 12px;
    height: 12px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
    0%, 100% { 
        transform: scale(1);
        opacity: 1;
    }
    50% { 
        transform: scale(1.2);
        opacity: 0.7;
    }
}

/* Modern Buttons */
.btn-modern {
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    padding: 12px 24px;
    font-weight: 600;
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition-medium);
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Chart Container Styles */
.chart-container {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-medium);
}

.chart-container:hover {
    box-shadow: var(--shadow-medium);
}

/* Notification Styles */
.notification {
    background: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-soft);
    border-left: 4px solid var(--primary-gradient);
    transition: var(--transition-medium);
}

.notification:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow-medium);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .modern-card {
        background: #1f2937;
        color: white;
    }
    
    .glass-card {
        background: rgba(31, 41, 55, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .chart-container {
        background: #1f2937;
        color: white;
    }
}
