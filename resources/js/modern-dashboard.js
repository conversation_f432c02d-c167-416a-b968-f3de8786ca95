// Modern Dashboard JavaScript

class ModernDashboard {
    constructor() {
        this.init();
        this.setupAnimations();
        this.setupCounters();
        this.setupCharts();
        this.setupRealTimeUpdates();
    }

    init() {
        console.log('Modern Dashboard initialized');
        this.addLoadingAnimations();
        this.setupIntersectionObserver();
    }

    // Add loading animations to elements
    addLoadingAnimations() {
        const cards = document.querySelectorAll('.modern-card, .glass-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // Setup intersection observer for scroll animations
    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        document.querySelectorAll('.observe-scroll').forEach(el => {
            observer.observe(el);
        });
    }

    // Animated number counters
    setupCounters() {
        const counters = document.querySelectorAll('.counter-number');
        
        counters.forEach(counter => {
            const target = parseInt(counter.textContent.replace(/,/g, ''));
            const duration = 2000; // 2 seconds
            const increment = target / (duration / 16); // 60fps
            let current = 0;

            const updateCounter = () => {
                current += increment;
                if (current < target) {
                    counter.textContent = Math.floor(current).toLocaleString();
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target.toLocaleString();
                }
            };

            // Start animation when element is visible
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateCounter();
                        observer.unobserve(entry.target);
                    }
                });
            });

            observer.observe(counter);
        });
    }

    // Setup progress bars animation
    setupProgressBars() {
        const progressBars = document.querySelectorAll('.progress-fill');
        
        progressBars.forEach(bar => {
            const percentage = bar.dataset.percentage || 0;
            bar.style.width = '0%';
            
            setTimeout(() => {
                bar.style.width = percentage + '%';
            }, 500);
        });
    }

    // Setup chart animations (placeholder for chart library integration)
    setupCharts() {
        // This will be expanded when integrating with Chart.js or similar
        const chartContainers = document.querySelectorAll('.chart-container');
        
        chartContainers.forEach(container => {
            // Add loading skeleton
            if (!container.querySelector('canvas')) {
                container.innerHTML = `
                    <div class="skeleton h-48 mb-4"></div>
                    <div class="skeleton h-4 w-3/4 mb-2"></div>
                    <div class="skeleton h-4 w-1/2"></div>
                `;
                
                // Simulate chart loading
                setTimeout(() => {
                    this.loadChart(container);
                }, 1500);
            }
        });
    }

    // Simulate chart loading (replace with actual chart implementation)
    loadChart(container) {
        container.innerHTML = `
            <div class="text-center py-8">
                <div class="text-gray-500">Chart will be rendered here</div>
                <div class="text-sm text-gray-400 mt-2">Integration with Chart.js pending</div>
            </div>
        `;
    }

    // Real-time updates with smooth transitions
    setupRealTimeUpdates() {
        // Enhanced version of the existing refresh functionality
        const originalRefresh = window.refreshDashboardData || (() => {});
        
        window.refreshDashboardData = () => {
            this.showLoadingState();
            
            fetch('/admin/dashboard/data')
                .then(response => response.json())
                .then(data => {
                    this.updateDataWithAnimation(data);
                    this.hideLoadingState();
                })
                .catch(error => {
                    console.error('Error refreshing data:', error);
                    this.hideLoadingState();
                });
        };
    }

    // Show loading state for real-time updates
    showLoadingState() {
        const cards = document.querySelectorAll('.modern-card');
        cards.forEach(card => {
            card.style.opacity = '0.7';
            card.style.pointerEvents = 'none';
        });

        // Add loading spinner to refresh button
        const refreshBtn = document.querySelector('[onclick="refreshData()"]');
        if (refreshBtn) {
            refreshBtn.innerHTML = `
                <svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Refreshing...
            `;
        }
    }

    // Hide loading state
    hideLoadingState() {
        const cards = document.querySelectorAll('.modern-card');
        cards.forEach(card => {
            card.style.opacity = '1';
            card.style.pointerEvents = 'auto';
        });

        // Restore refresh button
        const refreshBtn = document.querySelector('[onclick="refreshData()"]');
        if (refreshBtn) {
            refreshBtn.innerHTML = `
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh Data
            `;
        }
    }

    // Update data with smooth animations
    updateDataWithAnimation(data) {
        // Update numbers with animation
        Object.keys(data).forEach(key => {
            const element = document.getElementById(key);
            if (element && typeof data[key] === 'number') {
                this.animateNumberChange(element, data[key]);
            }
        });
    }

    // Animate number changes
    animateNumberChange(element, newValue) {
        const currentValue = parseInt(element.textContent.replace(/,/g, '')) || 0;
        const difference = newValue - currentValue;
        const duration = 1000;
        const steps = 30;
        const stepValue = difference / steps;
        let current = currentValue;
        let step = 0;

        const animate = () => {
            if (step < steps) {
                current += stepValue;
                element.textContent = Math.round(current).toLocaleString();
                step++;
                requestAnimationFrame(animate);
            } else {
                element.textContent = newValue.toLocaleString();
            }
        };

        animate();
    }

    // Setup hover effects
    setupAnimations() {
        // Add hover effects to cards
        document.querySelectorAll('.modern-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Add click ripple effect
        document.querySelectorAll('.btn-modern').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.createRipple(e, btn);
            });
        });
    }

    // Create ripple effect on button click
    createRipple(event, button) {
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
        `;

        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    // Utility function to add CSS animation keyframes
    addAnimationStyles() {
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
            
            .animate-in {
                animation: slideInUp 0.6s ease-out;
            }
            
            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const dashboard = new ModernDashboard();
    dashboard.addAnimationStyles();
});

// Export for global access
window.ModernDashboard = ModernDashboard;
