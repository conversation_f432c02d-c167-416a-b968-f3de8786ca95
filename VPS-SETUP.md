# VPS Setup Guide - Complete Server Configuration

This guide provides step-by-step instructions to set up a secure VPS with Nginx, PostgreSQL, SSL certificates, and hosting for Laravel and WordPress applications.

## Prerequisites
- Fresh Ubuntu 20.04/22.04 VPS
- Root access to the server
- Domain names pointing to your VPS IP
- SSH access to the server

## 1. Initial Server Security

### Update System
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install curl wget git unzip -y
```

### Create New User (Replace 'username' with your preferred username)
```bash
adduser username
usermod -aG sudo username

# Switch to new user
su - username
```

### Configure SSH Key Authentication
```bash
# On your local machine, generate SSH key if you don't have one
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Copy public key to server (run from local machine)
ssh-copy-id username@your_server_ip

# Test SSH connection with new user
ssh username@your_server_ip
```

### Secure SSH Configuration
```bash
# Edit SSH config
sudo nano /etc/ssh/sshd_config

# Find and modify these lines:
Port 2222
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2

# Test SSH config before restarting
sudo sshd -t

# Restart SSH service
sudo systemctl restart ssh

# IMPORTANT: Open new terminal and test SSH on port 2222 before closing current session
# ssh -p 2222 username@your_server_ip
```

## 2. Install and Configure UFW Firewall

```bash
# Install UFW
sudo apt install ufw -y

# Reset UFW to defaults
sudo ufw --force reset

# Set default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH on custom port (IMPORTANT: Do this before enabling UFW)
sudo ufw allow 2222/tcp comment 'SSH'

# Allow HTTP and HTTPS
sudo ufw allow 80/tcp comment 'HTTP'
sudo ufw allow 443/tcp comment 'HTTPS'

# Allow PostgreSQL (only from localhost)
sudo ufw allow from 127.0.0.1 to any port 5432 comment 'PostgreSQL'

# Enable firewall
sudo ufw --force enable

# Check status
sudo ufw status numbered
```

## 3. Install Nginx

```bash
# Install Nginx
sudo apt install nginx -y

# Remove default site
sudo rm /etc/nginx/sites-enabled/default

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Check status
sudo systemctl status nginx
```

## 4. Install and Secure PostgreSQL

```bash
# Install PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Set password for postgres user
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'your_postgres_admin_password';"

# Create databases and user
sudo -u postgres psql << EOF
CREATE DATABASE laravel_db;
CREATE DATABASE wordpress_db;
CREATE USER dbuser WITH ENCRYPTED PASSWORD 'your_strong_password';
GRANT ALL PRIVILEGES ON DATABASE laravel_db TO dbuser;
GRANT ALL PRIVILEGES ON DATABASE wordpress_db TO dbuser;
ALTER USER dbuser CREATEDB;
\q
EOF

# Configure PostgreSQL for security
sudo sed -i "s/#listen_addresses = 'localhost'/listen_addresses = 'localhost'/" /etc/postgresql/*/main/postgresql.conf

# Configure authentication
sudo sed -i "s/local   all             all                                     peer/local   all             all                                     md5/" /etc/postgresql/*/main/pg_hba.conf

# Restart PostgreSQL
sudo systemctl restart postgresql

# Test connection
sudo -u postgres psql -c "SELECT version();"
```

## 5. Install Certbot for SSL

```bash
# Install snapd (required for certbot)
sudo apt install snapd -y

# Install certbot via snap (recommended method)
sudo snap install core; sudo snap refresh core
sudo snap install --classic certbot

# Create symlink
sudo ln -s /snap/bin/certbot /usr/bin/certbot

# Verify installation
certbot --version
```

## 6. Install PHP and Required Extensions

```bash
# Add PHP repository
sudo apt install software-properties-common -y
sudo add-apt-repository ppa:ondrej/php -y
sudo apt update

# Install PHP 8.4 and extensions
sudo apt install php8.4-fpm php8.4-cli php8.4-pgsql php8.4-mysql php8.4-mbstring php8.4-xml php8.4-curl php8.4-zip php8.4-gd php8.4-intl php8.4-bcmath php8.4-soap php8.4-imagick php8.4-readline php8.4-common -y

# Start and enable PHP-FPM
sudo systemctl start php8.4-fpm
sudo systemctl enable php8.4-fpm

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
sudo chmod +x /usr/local/bin/composer

# Verify installations
php --version
composer --version
```

## 7. Create Directory Structure

```bash
# Create directories for applications
sudo mkdir -p /var/www/laravel-app
sudo mkdir -p /var/www/wordpress-app

# Create log directories
sudo mkdir -p /var/log/nginx/laravel-app
sudo mkdir -p /var/log/nginx/wordpress-app

# Set proper ownership
sudo chown -R www-data:www-data /var/www/laravel-app
sudo chown -R www-data:www-data /var/www/wordpress-app

# Set proper permissions
sudo chmod -R 755 /var/www/laravel-app
sudo chmod -R 755 /var/www/wordpress-app

# Create test files to verify setup
echo "<?php phpinfo(); ?>" | sudo tee /var/www/laravel-app/test.php
echo "<?php phpinfo(); ?>" | sudo tee /var/www/wordpress-app/test.php
```

## 8. Configure Nginx for Laravel Domain

```bash
# Create Nginx configuration for Laravel
sudo tee /etc/nginx/sites-available/laravel-domain.com > /dev/null << 'EOF'
server {
    listen 80;
    server_name your-laravel-domain.com www.your-laravel-domain.com;
    root /var/www/laravel-app/public;
    index index.php index.html index.htm;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Logging
    access_log /var/log/nginx/laravel-app/access.log;
    error_log /var/log/nginx/laravel-app/error.log;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.4-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Deny access to sensitive files
    location ~* \.(env|log|htaccess)$ {
        deny all;
    }

    # Cache static files
    location ~* \.(css|gif|ico|jpeg|jpg|js|png)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# Enable the site
sudo ln -s /etc/nginx/sites-available/laravel-domain.com /etc/nginx/sites-enabled/
```

## 9. Configure Nginx for WordPress Domain

```bash
# Create Nginx configuration for WordPress
sudo tee /etc/nginx/sites-available/wordpress-domain.com > /dev/null << 'EOF'
server {
    listen 80;
    server_name your-wordpress-domain.com www.your-wordpress-domain.com;
    root /var/www/wordpress-app;
    index index.php index.html index.htm;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Logging
    access_log /var/log/nginx/wordpress-app/access.log;
    error_log /var/log/nginx/wordpress-app/error.log;

    # WordPress specific configurations
    location / {
        try_files $uri $uri/ /index.php?$args;
    }

    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.4-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # WordPress security
    location ~ /\.(?!well-known).* {
        deny all;
    }

    location ~* /(?:uploads|files)/.*\.php$ {
        deny all;
    }

    location ~* \.(log|binary|pem|enc|crt|conf|cnf|sql|sh|key)$ {
        deny all;
    }

    # Cache static files
    location ~* \.(css|gif|ico|jpeg|jpg|js|png|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # WordPress xmlrpc protection
    location = /xmlrpc.php {
        deny all;
        access_log off;
        log_not_found off;
    }
}
EOF

# Enable the site
sudo ln -s /etc/nginx/sites-available/wordpress-domain.com /etc/nginx/sites-enabled/

# Test Nginx configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

## 10. Install WordPress

```bash
# Install MySQL for WordPress (WordPress works better with MySQL)
sudo apt install mysql-server -y

# Secure MySQL installation
sudo mysql_secure_installation

# Create WordPress database in MySQL
sudo mysql -u root -p << EOF
CREATE DATABASE wordpress_mysql_db DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;
CREATE USER 'wpuser'@'localhost' IDENTIFIED BY 'your_wp_strong_password';
GRANT ALL ON wordpress_mysql_db.* TO 'wpuser'@'localhost';
FLUSH PRIVILEGES;
EXIT;
EOF

# Download WordPress
cd /tmp
wget https://wordpress.org/latest.tar.gz
tar xzf latest.tar.gz
sudo cp -R wordpress/* /var/www/wordpress-app/

# Create WordPress config
sudo cp /var/www/wordpress-app/wp-config-sample.php /var/www/wordpress-app/wp-config.php

# Set proper ownership and permissions
sudo chown -R www-data:www-data /var/www/wordpress-app
sudo find /var/www/wordpress-app/ -type d -exec chmod 755 {} \;
sudo find /var/www/wordpress-app/ -type f -exec chmod 644 {} \;

# Clean up
rm -rf /tmp/wordpress /tmp/latest.tar.gz
```

## 11. Generate SSL Certificates

```bash
# IMPORTANT: Make sure your domains are pointing to your VPS IP before running these commands

# Generate SSL for Laravel domain
sudo certbot --nginx -d your-laravel-domain.com -d www.your-laravel-domain.com --non-interactive --agree-tos --email <EMAIL>

# Generate SSL for WordPress domain
sudo certbot --nginx -d your-wordpress-domain.com -d www.your-wordpress-domain.com --non-interactive --agree-tos --email <EMAIL>

# Set up auto-renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -

# Test auto-renewal
sudo certbot renew --dry-run
```

## 12. Deploy Laravel Application

```bash
# Install Git if not already installed
sudo apt install git -y

# Clone your Laravel application (replace with your actual repo)
cd /var/www/laravel-app
sudo git clone https://github.com/your-username/your-laravel-repo.git .

# Install dependencies
sudo -u www-data composer install --optimize-autoloader --no-dev

# Set up environment
sudo cp .env.example .env

# Edit .env file with correct database settings
sudo nano .env
```

Configure your `.env` file with these settings:
```env
APP_NAME="Your Laravel App"
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://your-laravel-domain.com

DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=laravel_db
DB_USERNAME=dbuser
DB_PASSWORD=your_strong_password

CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120
```

```bash
# Generate application key
sudo -u www-data php artisan key:generate

# Clear and cache config
sudo -u www-data php artisan config:clear
sudo -u www-data php artisan config:cache

# Run migrations
sudo -u www-data php artisan migrate --force

# Create storage link
sudo -u www-data php artisan storage:link

# Set proper permissions
sudo chown -R www-data:www-data /var/www/laravel-app
sudo find /var/www/laravel-app -type f -exec chmod 644 {} \;
sudo find /var/www/laravel-app -type d -exec chmod 755 {} \;
sudo chmod -R 775 /var/www/laravel-app/storage
sudo chmod -R 775 /var/www/laravel-app/bootstrap/cache
```

## 13. Configure WordPress Database Connection

```bash
# Edit WordPress config file
sudo nano /var/www/wordpress-app/wp-config.php

# Update these lines with your MySQL database details:
# define('DB_NAME', 'wordpress_mysql_db');
# define('DB_USER', 'wpuser');
# define('DB_PASSWORD', 'your_wp_strong_password');
# define('DB_HOST', 'localhost');

# Add WordPress security keys (get from https://api.wordpress.org/secret-key/1.1/salt/)
# Replace the dummy values with real ones from the URL above
```

## 14. Final Security Checks and Testing

```bash
# Check all services are running
sudo systemctl status nginx
sudo systemctl status postgresql
sudo systemctl status mysql
sudo systemctl status php8.4-fpm

# Check firewall status
sudo ufw status numbered

# Check SSL certificates
sudo certbot certificates

# Test PHP processing
curl -I https://your-laravel-domain.com/test.php
curl -I https://your-wordpress-domain.com/test.php

# Test domains redirect to HTTPS
curl -I http://your-laravel-domain.com
curl -I http://your-wordpress-domain.com

# Check Nginx configuration
sudo nginx -t

# View recent logs
sudo tail -f /var/log/nginx/laravel-app/error.log &
sudo tail -f /var/log/nginx/wordpress-app/error.log &
```

## 15. Cloudflare Configuration

### Step 1: Add Domains to Cloudflare
1. Log in to your Cloudflare dashboard
2. Click "Add a Site"
3. Enter your domain name
4. Choose your plan (Free is sufficient)
5. Cloudflare will scan for DNS records

### Step 2: Configure DNS Records
Set these A records in Cloudflare DNS:
- `your-laravel-domain.com` → `your_vps_ip` (Proxied: Orange cloud)
- `www.your-laravel-domain.com` → `your_vps_ip` (Proxied: Orange cloud)
- `your-wordpress-domain.com` → `your_vps_ip` (Proxied: Orange cloud)
- `www.your-wordpress-domain.com` → `your_vps_ip` (Proxied: Orange cloud)

### Step 3: Update Nameservers
1. Copy the Cloudflare nameservers
2. Go to your domain registrar
3. Update nameservers to Cloudflare's nameservers
4. Wait for propagation (can take up to 24 hours)

### Step 4: Configure SSL/TLS Settings
1. Go to SSL/TLS → Overview
2. Set encryption mode to "Full (strict)"
3. Enable "Always Use HTTPS"
4. Enable "Automatic HTTPS Rewrites"

## 16. Post-Installation Security Hardening

```bash
# Install fail2ban for additional security
sudo apt install fail2ban -y

# Configure fail2ban for SSH
sudo tee /etc/fail2ban/jail.local > /dev/null << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = 2222
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 3
bantime = 3600
EOF

# Start and enable fail2ban
sudo systemctl start fail2ban
sudo systemctl enable fail2ban

# Remove test PHP files for security
sudo rm /var/www/laravel-app/test.php
sudo rm /var/www/wordpress-app/test.php

# Set up automatic security updates
sudo apt install unattended-upgrades -y
sudo dpkg-reconfigure -plow unattended-upgrades
```

## Important Notes

- Replace `your-laravel-domain.com` and `your-wordpress-domain.com` with your actual domains
- Replace `your_vps_ip` with your actual VPS IP address
- Replace `your_strong_password`, `your_wp_strong_password`, and `your_postgres_admin_password` with secure passwords
- Replace `username` with your preferred username
- Replace `<EMAIL>` with your actual email address
- Keep your SSH private key secure and never share it
- Always test SSH connection on port 2222 before closing your current session
- Regularly update your system with `sudo apt update && sudo apt upgrade`
- Monitor your server logs regularly
- Backup your databases and files regularly

## Troubleshooting

### Common Issues and Solutions

**SSH Connection Issues:**
```bash
# Check SSH service status
sudo systemctl status ssh

# Check SSH configuration
sudo sshd -t

# View SSH logs
sudo tail -f /var/log/auth.log
```

**Nginx Issues:**
```bash
# Check Nginx error logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/laravel-app/error.log
sudo tail -f /var/log/nginx/wordpress-app/error.log

# Test Nginx configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

**PHP Issues:**
```bash
# Check PHP-FPM logs
sudo tail -f /var/log/php8.4-fpm.log

# Check PHP-FPM status
sudo systemctl status php8.4-fpm

# Restart PHP-FPM
sudo systemctl restart php8.4-fpm
```

**Database Issues:**
```bash
# Check PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-*-main.log

# Check MySQL logs
sudo tail -f /var/log/mysql/error.log

# Test database connections
sudo -u postgres psql -c "SELECT version();"
mysql -u root -p -e "SELECT version();"
```

**SSL Certificate Issues:**
```bash
# Check SSL certificate status
sudo certbot certificates

# Renew certificates manually
sudo certbot renew

# Check certificate expiration
echo | openssl s_client -servername your-domain.com -connect your-domain.com:443 2>/dev/null | openssl x509 -noout -dates
```

**Firewall Issues:**
```bash
# Check UFW status
sudo ufw status numbered

# Check fail2ban status
sudo fail2ban-client status
sudo fail2ban-client status sshd
```

## Maintenance Commands

```bash
# Weekly maintenance script
#!/bin/bash
sudo apt update && sudo apt upgrade -y
sudo apt autoremove -y
sudo apt autoclean
sudo certbot renew --quiet
sudo systemctl restart nginx
sudo systemctl restart php8.4-fpm
echo "Maintenance completed on $(date)"
```

Save this as `/home/<USER>/maintenance.sh` and run weekly:
```bash
chmod +x /home/<USER>/maintenance.sh
echo "0 2 * * 0 /home/<USER>/maintenance.sh >> /var/log/maintenance.log 2>&1" | crontab -
```
