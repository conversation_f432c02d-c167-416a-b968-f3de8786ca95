version: '3'
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    image: hosting-management-platform
    container_name: hosting-app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
    networks:
      - hosting-network
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    container_name: hosting-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: hosting_platform
      POSTGRES_USER: hosting_user
      POSTGRES_PASSWORD: hosting_password
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - hosting-network
    ports:
      - "5432:5432"

  nginx:
    image: nginx:alpine
    container_name: hosting-nginx
    restart: unless-stopped
    ports:
      - "3000:80"
      - "3443:443"
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
    networks:
      - hosting-network
    depends_on:
      - app

  redis:
    image: redis:alpine
    container_name: hosting-redis
    restart: unless-stopped
    networks:
      - hosting-network

networks:
  hosting-network:
    driver: bridge

volumes:
  postgres-data:
    driver: local
