/* Admin Theme - Dark Purple Color Scheme */
:root {
    --primary-bg: #100b27;
    --text-primary: #e7e7e7;
    --text-secondary: #878594;
    --purple-primary: #9d2ae8;
    --purple-dark: #510e86;
    --blue-accent: #4495e7;
    --purple-light: #cb86fd;
    --pink-accent: #fe36c0;
    --input-bg: rgba(231, 231, 231, 0.05);
    --card-bg: rgba(16, 11, 39, 0.7);
}

/* Base styles */
.admin-theme {
    background: var(--primary-bg);
    color: var(--text-primary);
    min-height: 100vh;
}

.admin-theme .bg-primary {
    background: var(--primary-bg);
}

.admin-theme .text-primary {
    color: var(--text-primary);
}

.admin-theme .text-secondary {
    color: var(--text-secondary);
}

/* Cards */
.admin-theme .card-bg {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(157, 42, 232, 0.2);
}

/* Buttons */
.admin-theme .btn-primary {
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-dark));
    border: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.admin-theme .btn-primary:hover {
    background: linear-gradient(135deg, var(--purple-light), var(--purple-primary));
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(157, 42, 232, 0.3);
}

.admin-theme .btn-secondary {
    background: linear-gradient(135deg, var(--blue-accent), var(--purple-primary));
    border: none;
    color: var(--text-primary);
}

.admin-theme .btn-accent {
    background: linear-gradient(135deg, var(--pink-accent), var(--purple-light));
    border: none;
    color: var(--text-primary);
}

/* Inputs */
.admin-theme .input-field {
    background: var(--input-bg);
    border: 1px solid rgba(157, 42, 232, 0.3);
    color: var(--text-primary);
    backdrop-filter: blur(5px);
}

.admin-theme .input-field:focus {
    border-color: var(--purple-primary);
    box-shadow: 0 0 0 3px rgba(157, 42, 232, 0.1);
    outline: none;
}

.admin-theme .input-field::placeholder {
    color: var(--text-secondary);
}

/* Statistics Cards */
.admin-theme .stat-card {
    background: var(--card-bg);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(157, 42, 232, 0.2);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.admin-theme .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(157, 42, 232, 0.2);
    border-color: var(--purple-primary);
}

/* Gradients */
.admin-theme .gradient-primary {
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-dark));
}

.admin-theme .gradient-secondary {
    background: linear-gradient(135deg, var(--blue-accent), var(--purple-primary));
}

.admin-theme .gradient-accent {
    background: linear-gradient(135deg, var(--pink-accent), var(--purple-light));
}

.admin-theme .gradient-success {
    background: linear-gradient(135deg, #10b981, var(--blue-accent));
}

.admin-theme .gradient-warning {
    background: linear-gradient(135deg, #f59e0b, var(--pink-accent));
}

.admin-theme .gradient-danger {
    background: linear-gradient(135deg, #ef4444, var(--pink-accent));
}

/* Text gradients */
.admin-theme .text-gradient {
    background: linear-gradient(135deg, var(--purple-light), var(--blue-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Animations */
@keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(157, 42, 232, 0.3); }
    50% { box-shadow: 0 0 30px rgba(157, 42, 232, 0.6); }
}

.admin-theme .glow-animation {
    animation: glow 2s ease-in-out infinite;
}

/* Counter animations */
.admin-theme .counter-number {
    font-weight: 700;
    font-size: 2rem;
    background: linear-gradient(135deg, var(--text-primary), var(--purple-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Table styles */
.admin-theme .table-header {
    background: var(--card-bg);
    color: var(--text-primary);
    border-bottom: 1px solid rgba(157, 42, 232, 0.2);
}

.admin-theme .table-row {
    background: rgba(16, 11, 39, 0.5);
    border-bottom: 1px solid rgba(157, 42, 232, 0.1);
    transition: all 0.3s ease;
}

.admin-theme .table-row:hover {
    background: var(--card-bg);
    transform: translateX(5px);
}

/* Status badges */
.admin-theme .status-online {
    background: linear-gradient(135deg, #10b981, var(--blue-accent));
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.admin-theme .status-offline {
    background: linear-gradient(135deg, #ef4444, var(--pink-accent));
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.admin-theme .status-maintenance {
    background: linear-gradient(135deg, #f59e0b, var(--purple-primary));
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Scrollbar */
.admin-theme ::-webkit-scrollbar {
    width: 8px;
}

.admin-theme ::-webkit-scrollbar-track {
    background: var(--primary-bg);
}

.admin-theme ::-webkit-scrollbar-thumb {
    background: var(--purple-primary);
    border-radius: 4px;
}

.admin-theme ::-webkit-scrollbar-thumb:hover {
    background: var(--purple-light);
}
