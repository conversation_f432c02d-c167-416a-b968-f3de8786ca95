/* Tailwind CSS Base */
@import url('https://cdn.tailwindcss.com');

/* Custom Styles for Welcome Page */
.hero-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.feature-card {
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.floating-animation {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Custom Button Styles */
.btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
}

.btn-primary {
    background-color: #2563eb;
    color: white;
}

.btn-primary:hover {
    background-color: #1d4ed8;
}

.btn-secondary {
    background-color: #4f46e5;
    color: white;
}

.btn-secondary:hover {
    background-color: #4338ca;
}

/* Card Styles */
.card {
    background-color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border-radius: 0.75rem;
    overflow: hidden;
    border: 1px solid #f3f4f6;
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.card-body {
    padding: 1.5rem;
}

/* Stat Card Styles */
.stat-card {
    display: flex;
    flex-direction: column;
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #f3f4f6;
    transition: all 0.3s ease;
    background-color: white;
}

.stat-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 1.875rem;
    font-weight: 700;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-gradient h1 {
        font-size: 2.25rem;
    }
    
    .feature-card {
        margin-bottom: 1rem;
    }
}
