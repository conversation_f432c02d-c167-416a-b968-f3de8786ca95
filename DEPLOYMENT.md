# Deployment and Setup Guide - Hosting Platform

## Overview

The hosting management platform has been successfully deployed using Docker. The platform is now running on:
- **URL**: http://localhost:8080
- **Admin Panel**: http://localhost:8080/admin

## Login Credentials

### Admin User
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: admin

## Running Services

### 1. Laravel Application
- **Container**: hosting-app_app_1
- **Port**: 8080
- **Status**: ✅ Running

### 2. PostgreSQL Database
- **Container**: hosting-app_db_1
- **Port**: 5432
- **Database**: hosting_db
- **Username**: hosting_user
- **Password**: hosting_password
- **Status**: ✅ Running

### 3. Redis Cache
- **Container**: hosting-app_redis_1
- **Port**: 6379
- **Status**: ✅ Running

### 4. Nginx Web Server
- **Container**: hosting-app_nginx_1
- **Port**: 80 (internal)
- **Status**: ✅ Running

## Useful Commands

### Docker Container Management

```bash
# Check container status
docker-compose ps

# Stop the application
docker-compose down

# Restart the application
docker-compose restart

# View logs
docker-compose logs -f app

# Access application container
docker-compose exec app bash
```

### Laravel Commands

```bash
# Run migrations
docker-compose exec app php artisan migrate

# Run seeders
docker-compose exec app php artisan db:seed

# Create new admin user
docker-compose exec app php artisan app:create-admin-user --email=<EMAIL> --password=password123 --name="Admin User"

# Clear cache
docker-compose exec app php artisan cache:clear

# Clear config cache
docker-compose exec app php artisan config:clear

# Reload autoloader
docker-compose exec app composer dump-autoload
```

### Database Management

```bash
# Connect to database
docker-compose exec db psql -U hosting_user -d hosting_db

# Create database backup
docker-compose exec db pg_dump -U hosting_user hosting_db > backup.sql

# Restore backup
docker-compose exec -T db psql -U hosting_user -d hosting_db < backup.sql
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Container not running
```bash
# Check logs
docker-compose logs app

# Rebuild containers
docker-compose up -d --build --force-recreate
```

#### 2. Database connection issues
```bash
# Test database connection
docker-compose exec app php artisan tinker
# Then run: DB::connection()->getPdo();
```

#### 3. Permission issues
```bash
# Fix permissions
docker-compose exec app chown -R www-data:www-data /var/www/html/storage
docker-compose exec app chmod -R 775 /var/www/html/storage
```

## Available Features

### 1. Main Dashboard
- System statistics
- Performance monitoring
- User management

### 2. User Management
- Create and edit users
- Role and permission management
- Activity monitoring

### 3. System Settings
- General settings
- Security settings
- Email settings

### 4. File Management
- File upload and download
- Folder management
- Permission system

## Next Steps

### 1. Application Customization
- Modify colors and logo
- Add new features
- Customize user interface

### 2. Production Setup
- Configure SSL
- Setup domain
- Performance optimization

### 3. Backup Setup
- Setup automatic backups
- System monitoring
- Setup alerts

## Support and Help

If you encounter any issues:
1. Check logs: `docker-compose logs -f`
2. Ensure all services are running: `docker-compose ps`
3. Review `.env` file for correct settings
4. Contact development team

---

**Successfully Deployed! 🎉**

You can now access the application at: http://localhost:8080
