<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subdomains', function (Blueprint $table) {
            $table->id();
            $table->foreignId('domain_id')->constrained()->onDelete('cascade');
            $table->string('name'); // www, api, blog, etc.
            $table->string('full_name'); // www.domain.com (computed)
            $table->string('document_root')->nullable(); // /var/www/domain.com/subdomain
            $table->string('redirect_url')->nullable(); // if it's a redirect
            $table->integer('redirect_type')->nullable(); // 301, 302
            $table->boolean('is_active')->default(true);
            $table->string('php_version')->nullable(); // inherit from domain if null
            $table->boolean('ssl_enabled')->default(false);
            $table->json('custom_headers')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->unique(['domain_id', 'name']);
            $table->index(['domain_id', 'is_active']);
            $table->index('full_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subdomains');
    }
};
