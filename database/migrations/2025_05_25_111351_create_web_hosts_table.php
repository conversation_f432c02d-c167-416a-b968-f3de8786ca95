<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('web_hosts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('domain_id')->constrained()->onDelete('cascade');
            $table->string('server_name'); // domain.com, www.domain.com
            $table->string('server_alias')->nullable(); // additional aliases
            $table->string('document_root'); // /var/www/domain.com/public
            $table->string('index_files')->default('index.html index.php'); // directory index files
            $table->string('web_server')->default('nginx'); // nginx, apache
            $table->string('php_version')->default('8.2');
            $table->boolean('php_enabled')->default(true);
            $table->json('php_modules')->nullable(); // enabled PHP modules
            $table->json('custom_directives')->nullable(); // custom nginx/apache config
            $table->boolean('gzip_enabled')->default(true);
            $table->boolean('access_log_enabled')->default(true);
            $table->boolean('error_log_enabled')->default(true);
            $table->string('access_log_path')->nullable();
            $table->string('error_log_path')->nullable();
            $table->json('security_headers')->nullable(); // custom security headers
            $table->boolean('hotlink_protection')->default(false);
            $table->json('blocked_ips')->nullable(); // IP blacklist
            $table->json('allowed_ips')->nullable(); // IP whitelist
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_config_update')->nullable();
            $table->timestamps();

            $table->index(['domain_id', 'is_active']);
            $table->index(['web_server', 'is_active']);
            $table->index('server_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('web_hosts');
    }
};
