<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ssl_certificate_renewals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ssl_certificate_id')->constrained()->onDelete('cascade');
            $table->string('status')->default('pending'); // pending, in_progress, completed, failed
            $table->timestamp('started_at');
            $table->timestamp('completed_at')->nullable();
            $table->integer('attempt_number')->default(1);
            $table->string('renewal_method')->default('acme'); // acme, manual
            $table->text('challenge_data')->nullable(); // ACME challenge details
            $table->text('error_message')->nullable();
            $table->json('logs')->nullable(); // detailed renewal logs
            $table->date('old_expiry_date')->nullable();
            $table->date('new_expiry_date')->nullable();
            $table->boolean('auto_triggered')->default(true);
            $table->timestamps();

            $table->index(['ssl_certificate_id', 'status']);
            $table->index(['status', 'started_at']);
            $table->index('attempt_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ssl_certificate_renewals');
    }
};
