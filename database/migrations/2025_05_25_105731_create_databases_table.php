<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('databases', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('domain_id')->nullable()->constrained()->onDelete('set null');
            $table->string('name')->unique(); // database name
            $table->string('type')->default('mysql'); // mysql, postgresql, mongodb
            $table->string('host')->default('localhost');
            $table->integer('port')->nullable(); // 3306 for MySQL, 5432 for PostgreSQL
            $table->string('charset')->default('utf8mb4');
            $table->string('collation')->default('utf8mb4_unicode_ci');
            $table->bigInteger('size_bytes')->default(0); // current size in bytes
            $table->bigInteger('max_size_bytes')->nullable(); // quota limit
            $table->integer('table_count')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_backup_at')->nullable();
            $table->json('connection_settings')->nullable();
            $table->text('description')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'is_active']);
            $table->index(['type', 'is_active']);
            $table->index('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('databases');
    }
};
