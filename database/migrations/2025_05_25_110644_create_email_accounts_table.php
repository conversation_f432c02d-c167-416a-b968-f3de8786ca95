<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('domain_id')->constrained()->onDelete('cascade');
            $table->string('email')->unique(); // full email address
            $table->string('username'); // local part before @
            $table->string('password_hash'); // encrypted password
            $table->bigInteger('quota_bytes')->default(**********); // 1GB default
            $table->bigInteger('used_bytes')->default(0);
            $table->boolean('is_active')->default(true);
            $table->boolean('can_send')->default(true);
            $table->boolean('can_receive')->default(true);
            $table->integer('max_send_per_hour')->default(100);
            $table->integer('sent_today')->default(0);
            $table->date('sent_reset_date')->nullable();
            $table->timestamp('last_login_at')->nullable();
            $table->string('last_login_ip')->nullable();
            $table->json('forwarding_addresses')->nullable(); // email forwarding
            $table->boolean('vacation_enabled')->default(false);
            $table->text('vacation_message')->nullable();
            $table->date('vacation_start_date')->nullable();
            $table->date('vacation_end_date')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'is_active']);
            $table->index(['domain_id', 'is_active']);
            $table->index('email');
            $table->index(['username', 'domain_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_accounts');
    }
};
