<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Basic, Pro, Enterprise
            $table->string('slug')->unique(); // basic, pro, enterprise
            $table->text('description');
            $table->decimal('price', 10, 2); // monthly price
            $table->decimal('yearly_price', 10, 2)->nullable(); // yearly discount
            $table->string('currency', 3)->default('USD');
            $table->string('billing_cycle')->default('monthly'); // monthly, yearly, one-time
            $table->integer('trial_days')->default(0);

            // Resource limits
            $table->integer('max_domains')->default(1);
            $table->integer('max_subdomains')->default(10);
            $table->integer('max_email_accounts')->default(10);
            $table->bigInteger('disk_space_gb')->default(10); // in GB
            $table->bigInteger('bandwidth_gb')->default(100); // monthly bandwidth in GB
            $table->integer('max_databases')->default(5);
            $table->integer('max_cron_jobs')->default(10);
            $table->integer('max_backups')->default(5);
            $table->integer('backup_retention_days')->default(30);

            // Features
            $table->boolean('ssl_certificates')->default(true);
            $table->boolean('email_hosting')->default(true);
            $table->boolean('dns_management')->default(true);
            $table->boolean('file_manager')->default(true);
            $table->boolean('app_installer')->default(true);
            $table->boolean('priority_support')->default(false);
            $table->boolean('white_label')->default(false);
            $table->json('features')->nullable(); // additional features

            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['is_active', 'sort_order']);
            $table->index('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
