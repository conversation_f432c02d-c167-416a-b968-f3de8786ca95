<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bandwidth_usages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('domain_id')->nullable()->constrained()->onDelete('cascade');
            $table->date('usage_date');
            $table->bigInteger('bytes_in')->default(0); // incoming traffic
            $table->bigInteger('bytes_out')->default(0); // outgoing traffic
            $table->bigInteger('bytes_total')->default(0); // total traffic
            $table->integer('requests_count')->default(0); // number of requests
            $table->integer('unique_visitors')->default(0); // unique IP addresses
            $table->string('period_type')->default('daily'); // daily, weekly, monthly
            $table->timestamps();

            $table->unique(['user_id', 'domain_id', 'usage_date', 'period_type']);
            $table->index(['user_id', 'usage_date']);
            $table->index(['domain_id', 'usage_date']);
            $table->index(['usage_date', 'period_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bandwidth_usages');
    }
};
