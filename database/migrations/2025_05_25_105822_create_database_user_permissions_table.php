<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('database_user_permissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('database_user_id')->constrained()->onDelete('cascade');
            $table->foreignId('database_id')->constrained()->onDelete('cascade');
            $table->json('privileges'); // SELECT, INSERT, UPDATE, DELETE, etc.
            $table->boolean('grant_option')->default(false); // can grant privileges to others
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->unique(['database_user_id', 'database_id']);
            $table->index(['database_user_id', 'is_active']);
            $table->index(['database_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('database_user_permissions');
    }
};
