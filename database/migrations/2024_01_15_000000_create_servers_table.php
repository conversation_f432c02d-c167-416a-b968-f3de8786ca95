<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('servers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('hostname');
            $table->string('ip_address');
            $table->integer('port')->default(22);
            $table->string('username');
            $table->text('ssh_key')->nullable();
            $table->string('password')->nullable();
            $table->enum('status', ['online', 'offline', 'maintenance'])->default('offline');
            $table->enum('type', ['web', 'database', 'mail', 'dns', 'load_balancer', 'storage'])->default('web');
            $table->string('operating_system')->nullable();
            $table->string('control_panel')->nullable();
            $table->json('specifications')->nullable(); // CPU, RAM, Storage
            $table->json('monitoring_data')->nullable(); // CPU usage, RAM usage, etc.
            $table->timestamp('last_ping')->nullable();
            $table->text('notes')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('servers');
    }
};
