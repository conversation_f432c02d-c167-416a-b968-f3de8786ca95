<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('api_key_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('method'); // GET, POST, PUT, DELETE
            $table->string('endpoint'); // /api/domains, /api/users, etc.
            $table->string('full_url');
            $table->json('request_headers')->nullable();
            $table->json('request_body')->nullable();
            $table->integer('response_status'); // 200, 404, 500, etc.
            $table->json('response_headers')->nullable();
            $table->json('response_body')->nullable();
            $table->integer('response_time_ms'); // response time in milliseconds
            $table->string('ip_address');
            $table->text('user_agent')->nullable();
            $table->string('request_id')->nullable(); // unique request identifier
            $table->boolean('rate_limited')->default(false);
            $table->string('error_message')->nullable();
            $table->timestamps();

            $table->index(['api_key_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['method', 'endpoint']);
            $table->index(['response_status', 'created_at']);
            $table->index(['ip_address', 'created_at']);
            $table->index('request_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_logs');
    }
};
