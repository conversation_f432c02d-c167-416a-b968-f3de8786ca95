<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('web_redirects', function (Blueprint $table) {
            $table->id();
            $table->foreignId('domain_id')->constrained()->onDelete('cascade');
            $table->string('source_url'); // /old-page or full URL
            $table->string('destination_url'); // /new-page or full URL
            $table->integer('redirect_type')->default(301); // 301, 302, 307, 308
            $table->boolean('is_active')->default(true);
            $table->boolean('match_query_string')->default(false);
            $table->boolean('is_regex')->default(false); // if source_url is regex
            $table->string('description')->nullable();
            $table->integer('hit_count')->default(0); // how many times used
            $table->timestamp('last_used_at')->nullable();
            $table->timestamps();

            $table->index(['domain_id', 'is_active']);
            $table->index(['source_url', 'is_active']);
            $table->index('redirect_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('web_redirects');
    }
};
