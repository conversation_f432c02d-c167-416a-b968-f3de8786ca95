<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cron_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cron_job_id')->constrained()->onDelete('cascade');
            $table->timestamp('started_at');
            $table->timestamp('finished_at')->nullable();
            $table->integer('duration_seconds')->nullable();
            $table->string('status'); // success, failed, timeout, killed
            $table->integer('exit_code')->nullable();
            $table->text('output')->nullable(); // stdout
            $table->text('error_output')->nullable(); // stderr
            $table->integer('memory_usage_mb')->nullable();
            $table->float('cpu_usage_percent')->nullable();
            $table->string('triggered_by')->default('schedule'); // schedule, manual, api
            $table->timestamps();

            $table->index(['cron_job_id', 'started_at']);
            $table->index(['status', 'started_at']);
            $table->index('started_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cron_logs');
    }
};
