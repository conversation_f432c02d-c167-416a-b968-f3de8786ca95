<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dns_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('domain_id')->constrained()->onDelete('cascade');
            $table->string('name'); // @, www, mail, etc.
            $table->string('type'); // A, AAAA, CNAME, MX, TXT, NS, SRV
            $table->text('value'); // IP address, hostname, etc.
            $table->integer('ttl')->default(3600); // Time to live in seconds
            $table->integer('priority')->nullable(); // For MX, SRV records
            $table->integer('weight')->nullable(); // For SRV records
            $table->integer('port')->nullable(); // For SRV records
            $table->boolean('is_active')->default(true);
            $table->boolean('is_system')->default(false); // system-generated records
            $table->text('comment')->nullable();
            $table->timestamp('last_checked_at')->nullable();
            $table->boolean('propagated')->default(false);
            $table->timestamps();

            $table->index(['domain_id', 'type']);
            $table->index(['domain_id', 'name', 'type']);
            $table->index(['is_active', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dns_records');
    }
};
