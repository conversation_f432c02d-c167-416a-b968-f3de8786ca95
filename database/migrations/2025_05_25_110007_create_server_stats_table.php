<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('server_stats', function (Blueprint $table) {
            $table->id();
            $table->timestamp('recorded_at');
            $table->float('cpu_usage_percent', 5, 2); // 0.00 to 100.00
            $table->bigInteger('memory_total_bytes');
            $table->bigInteger('memory_used_bytes');
            $table->bigInteger('memory_free_bytes');
            $table->float('memory_usage_percent', 5, 2);
            $table->bigInteger('disk_total_bytes');
            $table->bigInteger('disk_used_bytes');
            $table->bigInteger('disk_free_bytes');
            $table->float('disk_usage_percent', 5, 2);
            $table->float('load_average_1min', 8, 2)->nullable();
            $table->float('load_average_5min', 8, 2)->nullable();
            $table->float('load_average_15min', 8, 2)->nullable();
            $table->integer('process_count')->nullable();
            $table->bigInteger('network_rx_bytes')->nullable();
            $table->bigInteger('network_tx_bytes')->nullable();
            $table->integer('active_connections')->nullable();
            $table->float('temperature_celsius')->nullable();
            $table->json('additional_metrics')->nullable(); // custom metrics
            $table->timestamps();

            $table->index('recorded_at');
            $table->index(['recorded_at', 'cpu_usage_percent']);
            $table->index(['recorded_at', 'memory_usage_percent']);
            $table->index(['recorded_at', 'disk_usage_percent']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('server_stats');
    }
};
