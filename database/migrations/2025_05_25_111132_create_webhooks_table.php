<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webhooks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name'); // webhook name
            $table->string('url'); // target URL
            $table->json('events'); // events to listen for
            $table->string('secret')->nullable(); // webhook secret for verification
            $table->boolean('is_active')->default(true);
            $table->string('content_type')->default('application/json');
            $table->json('headers')->nullable(); // custom headers
            $table->integer('timeout_seconds')->default(30);
            $table->integer('max_retries')->default(3);
            $table->boolean('verify_ssl')->default(true);
            $table->timestamp('last_triggered_at')->nullable();
            $table->integer('success_count')->default(0);
            $table->integer('failure_count')->default(0);
            $table->string('last_response_status')->nullable();
            $table->text('last_error')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'is_active']);
            $table->index('url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webhooks');
    }
};
