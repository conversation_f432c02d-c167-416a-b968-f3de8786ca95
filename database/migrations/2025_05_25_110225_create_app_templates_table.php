<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // WordPress, Lara<PERSON>, Drupal, etc.
            $table->string('slug')->unique(); // wordpress, laravel, drupal
            $table->string('type'); // cms, framework, ecommerce, blog
            $table->string('category')->nullable(); // PHP, Node.js, Python, etc.
            $table->text('description');
            $table->string('version'); // latest version available
            $table->string('min_php_version')->nullable(); // 8.0, 8.1, etc.
            $table->json('requirements')->nullable(); // system requirements
            $table->string('source_type'); // git, zip, composer, npm
            $table->string('source_url'); // repository or download URL
            $table->string('source_branch')->default('main'); // for git repos
            $table->json('install_commands')->nullable(); // installation steps
            $table->json('default_config')->nullable(); // default configuration
            $table->string('icon_url')->nullable(); // app icon
            $table->string('documentation_url')->nullable();
            $table->string('demo_url')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->integer('install_count')->default(0);
            $table->float('rating', 3, 2)->default(0.00); // 0.00 to 5.00
            $table->timestamps();

            $table->index(['type', 'is_active']);
            $table->index(['category', 'is_active']);
            $table->index(['is_featured', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_templates');
    }
};
