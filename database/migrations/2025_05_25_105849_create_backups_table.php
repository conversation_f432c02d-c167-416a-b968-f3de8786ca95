<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backups', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name'); // backup name/identifier
            $table->string('type'); // database, files, full
            $table->string('status')->default('pending'); // pending, running, completed, failed
            $table->morphs('backupable'); // database_id, domain_id, etc.
            $table->string('storage_path'); // path to backup file
            $table->string('storage_driver')->default('local'); // local, s3, ftp
            $table->bigInteger('size_bytes')->default(0);
            $table->string('compression')->default('gzip'); // gzip, zip, none
            $table->boolean('encrypted')->default(false);
            $table->string('encryption_method')->nullable(); // AES-256, etc.
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('duration_seconds')->nullable();
            $table->text('error_message')->nullable();
            $table->json('metadata')->nullable(); // additional backup info
            $table->boolean('is_automatic')->default(false); // scheduled vs manual
            $table->timestamp('expires_at')->nullable(); // auto-delete date
            $table->timestamps();

            $table->index(['user_id', 'type', 'status']);
            $table->index(['status', 'created_at']);
            $table->index('expires_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backups');
    }
};
