<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('type'); // credit_card, paypal, bank_account
            $table->string('gateway'); // stripe, paypal, bank
            $table->string('gateway_payment_method_id')->nullable(); // Gateway's payment method ID
            $table->string('gateway_customer_id')->nullable(); // Gateway's customer ID

            // Card details (encrypted)
            $table->string('last_four')->nullable();
            $table->string('brand')->nullable(); // visa, mastercard, amex
            $table->string('exp_month')->nullable();
            $table->string('exp_year')->nullable();
            $table->string('cardholder_name')->nullable();

            // PayPal details
            $table->string('paypal_email')->nullable();
            $table->string('paypal_payer_id')->nullable();

            // Bank account details
            $table->string('bank_name')->nullable();
            $table->string('account_last_four')->nullable();
            $table->string('routing_number')->nullable();
            $table->string('account_type')->nullable(); // checking, savings

            // Status and metadata
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            $table->json('metadata')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'is_default']);
            $table->index(['user_id', 'is_active']);
            $table->index('gateway_payment_method_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};
