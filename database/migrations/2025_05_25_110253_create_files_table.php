<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('files', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('domain_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('name'); // filename
            $table->string('path'); // full file path
            $table->string('relative_path'); // path relative to domain root
            $table->string('type'); // file, directory, symlink
            $table->string('mime_type')->nullable();
            $table->bigInteger('size_bytes')->default(0);
            $table->string('permissions', 4)->default('0644'); // octal permissions
            $table->string('owner')->nullable(); // file owner
            $table->string('group')->nullable(); // file group
            $table->string('hash')->nullable(); // file hash for integrity
            $table->boolean('is_hidden')->default(false);
            $table->boolean('is_system')->default(false); // system file
            $table->timestamp('last_modified_at')->nullable();
            $table->timestamp('last_accessed_at')->nullable();
            $table->json('metadata')->nullable(); // additional file info
            $table->timestamps();

            $table->index(['user_id', 'type']);
            $table->index(['domain_id', 'type']);
            $table->index(['path', 'type']);
            $table->index('hash');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('files');
    }
};
