<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cron_jobs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name'); // job name/description
            $table->text('command'); // command to execute
            $table->string('schedule'); // cron expression: * * * * *
            $table->string('user')->default('www-data'); // system user to run as
            $table->string('working_directory')->nullable(); // working directory
            $table->json('environment_variables')->nullable(); // env vars
            $table->boolean('is_active')->default(true);
            $table->boolean('send_email_on_failure')->default(false);
            $table->string('email_on_failure')->nullable();
            $table->boolean('log_output')->default(true);
            $table->integer('timeout_seconds')->default(3600); // 1 hour default
            $table->timestamp('last_run_at')->nullable();
            $table->timestamp('next_run_at')->nullable();
            $table->string('last_run_status')->nullable(); // success, failed, timeout
            $table->text('last_run_output')->nullable();
            $table->integer('run_count')->default(0);
            $table->integer('success_count')->default(0);
            $table->integer('failure_count')->default(0);
            $table->timestamps();

            $table->index(['user_id', 'is_active']);
            $table->index(['is_active', 'next_run_at']);
            $table->index('last_run_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cron_jobs');
    }
};
