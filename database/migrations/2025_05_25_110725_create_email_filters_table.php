<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_filters', function (Blueprint $table) {
            $table->id();
            $table->foreignId('email_account_id')->constrained()->onDelete('cascade');
            $table->string('name'); // filter name
            $table->integer('priority')->default(1); // execution order
            $table->boolean('is_active')->default(true);
            $table->string('condition_field'); // from, to, subject, body, header
            $table->string('condition_operator'); // contains, equals, starts_with, ends_with, regex
            $table->string('condition_value'); // value to match
            $table->boolean('case_sensitive')->default(false);
            $table->string('action_type'); // move, copy, delete, forward, reply, mark_read, mark_spam
            $table->string('action_value')->nullable(); // folder name, email address, etc.
            $table->boolean('stop_processing')->default(false); // stop other filters
            $table->integer('match_count')->default(0); // how many times matched
            $table->timestamp('last_matched_at')->nullable();
            $table->timestamps();

            $table->index(['email_account_id', 'is_active', 'priority']);
            $table->index(['condition_field', 'condition_operator']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_filters');
    }
};
