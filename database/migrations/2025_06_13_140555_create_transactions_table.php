<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('subscription_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('invoice_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('payment_method_id')->nullable()->constrained()->onDelete('set null');

            // Transaction details
            $table->string('transaction_id')->unique(); // Our internal transaction ID
            $table->string('type'); // payment, refund, chargeback, adjustment
            $table->string('status'); // pending, processing, completed, failed, cancelled, refunded
            $table->decimal('amount', 10, 2);
            $table->decimal('fee_amount', 10, 2)->default(0); // Gateway fees
            $table->decimal('net_amount', 10, 2); // Amount after fees
            $table->string('currency', 3)->default('USD');

            // Gateway information
            $table->string('gateway'); // stripe, paypal, bank
            $table->string('gateway_transaction_id')->nullable(); // Gateway's transaction ID
            $table->string('gateway_fee_id')->nullable(); // Gateway's fee ID
            $table->json('gateway_response')->nullable(); // Full gateway response

            // Payment details
            $table->string('payment_method_type'); // credit_card, paypal, bank_transfer
            $table->string('description')->nullable();
            $table->text('failure_reason')->nullable();
            $table->string('failure_code')->nullable();

            // Timestamps
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->timestamp('refunded_at')->nullable();

            // Metadata
            $table->json('metadata')->nullable();
            $table->string('reference_number')->nullable(); // For customer reference
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['subscription_id', 'status']);
            $table->index(['gateway', 'gateway_transaction_id']);
            $table->index(['type', 'status']);
            $table->index('transaction_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
