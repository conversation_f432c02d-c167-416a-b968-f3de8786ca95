<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_announcements', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('content');
            $table->string('type')->default('info'); // info, warning, error, success, maintenance
            $table->string('priority')->default('normal'); // low, normal, high, urgent
            $table->boolean('is_active')->default(true);
            $table->boolean('is_dismissible')->default(true);
            $table->boolean('show_on_dashboard')->default(true);
            $table->boolean('show_on_login')->default(false);
            $table->json('target_roles')->nullable(); // which roles can see this
            $table->json('target_users')->nullable(); // specific users
            $table->timestamp('starts_at')->nullable();
            $table->timestamp('ends_at')->nullable();
            $table->string('action_url')->nullable(); // optional action button
            $table->string('action_text')->nullable(); // action button text
            $table->integer('view_count')->default(0);
            $table->integer('dismiss_count')->default(0);
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();

            $table->index(['is_active', 'priority', 'starts_at']);
            $table->index(['type', 'is_active']);
            $table->index(['starts_at', 'ends_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_announcements');
    }
};
