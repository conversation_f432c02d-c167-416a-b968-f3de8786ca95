<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('action'); // create, update, delete, login, logout, etc.
            $table->string('resource_type'); // User, Domain, Database, etc.
            $table->string('resource_id')->nullable();
            $table->string('resource_name')->nullable(); // human-readable name
            $table->json('old_values')->nullable(); // before changes
            $table->json('new_values')->nullable(); // after changes
            $table->string('ip_address');
            $table->text('user_agent')->nullable();
            $table->string('method')->nullable(); // GET, POST, PUT, DELETE
            $table->string('url')->nullable();
            $table->json('request_data')->nullable(); // request payload
            $table->string('session_id')->nullable();
            $table->string('severity')->default('info'); // info, warning, error, critical
            $table->text('description')->nullable(); // human-readable description
            $table->json('metadata')->nullable(); // additional context
            $table->timestamps();

            $table->index(['user_id', 'created_at']);
            $table->index(['action', 'created_at']);
            $table->index(['resource_type', 'resource_id']);
            $table->index(['ip_address', 'created_at']);
            $table->index(['severity', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
    }
};
