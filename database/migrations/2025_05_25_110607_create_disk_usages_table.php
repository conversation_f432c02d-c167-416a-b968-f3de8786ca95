<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('disk_usages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('domain_id')->nullable()->constrained()->onDelete('cascade');
            $table->date('usage_date');
            $table->bigInteger('files_size_bytes')->default(0); // website files
            $table->bigInteger('databases_size_bytes')->default(0); // database size
            $table->bigInteger('emails_size_bytes')->default(0); // email storage
            $table->bigInteger('backups_size_bytes')->default(0); // backup files
            $table->bigInteger('logs_size_bytes')->default(0); // log files
            $table->bigInteger('total_size_bytes')->default(0); // total usage
            $table->integer('files_count')->default(0); // number of files
            $table->integer('directories_count')->default(0); // number of directories
            $table->bigInteger('quota_limit_bytes')->nullable(); // storage quota
            $table->string('period_type')->default('daily'); // daily, weekly, monthly
            $table->timestamps();

            $table->unique(['user_id', 'domain_id', 'usage_date', 'period_type']);
            $table->index(['user_id', 'usage_date']);
            $table->index(['domain_id', 'usage_date']);
            $table->index(['usage_date', 'period_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('disk_usages');
    }
};
