<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('apps', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('domain_id')->constrained()->onDelete('cascade');
            $table->string('name'); // WordPress, Laravel, etc.
            $table->string('type'); // cms, framework, ecommerce, etc.
            $table->string('version')->nullable();
            $table->string('status')->default('active'); // active, inactive, updating, failed
            $table->string('install_path'); // /var/www/domain.com/app
            $table->string('url'); // https://domain.com/app
            $table->string('admin_url')->nullable(); // admin panel URL
            $table->string('admin_username')->nullable();
            $table->string('admin_email')->nullable();
            $table->json('database_config')->nullable(); // DB connection details
            $table->json('environment_config')->nullable(); // ENV variables
            $table->json('custom_config')->nullable(); // app-specific settings
            $table->timestamp('installed_at');
            $table->timestamp('last_updated_at')->nullable();
            $table->boolean('auto_update')->default(false);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['domain_id', 'status']);
            $table->index(['type', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('apps');
    }
};
