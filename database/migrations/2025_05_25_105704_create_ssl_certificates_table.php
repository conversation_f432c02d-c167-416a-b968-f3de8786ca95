<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ssl_certificates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('domain_id')->constrained()->onDelete('cascade');
            $table->string('name'); // certificate name/identifier
            $table->string('type')->default('letsencrypt'); // letsencrypt, custom, self-signed
            $table->string('status')->default('pending'); // pending, active, expired, failed
            $table->text('domains'); // JSON array of domains covered
            $table->text('certificate')->nullable(); // PEM certificate
            $table->text('private_key')->nullable(); // PEM private key (encrypted)
            $table->text('certificate_chain')->nullable(); // PEM certificate chain
            $table->string('issuer')->nullable(); // Let's Encrypt, etc.
            $table->date('issued_at')->nullable();
            $table->date('expires_at')->nullable();
            $table->boolean('auto_renew')->default(true);
            $table->integer('days_before_renewal')->default(30);
            $table->timestamp('last_renewal_attempt')->nullable();
            $table->text('renewal_error')->nullable();
            $table->string('acme_challenge_type')->nullable(); // http-01, dns-01
            $table->json('acme_challenge_data')->nullable();
            $table->timestamps();

            $table->index(['domain_id', 'status']);
            $table->index('expires_at');
            $table->index(['auto_renew', 'expires_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ssl_certificates');
    }
};
