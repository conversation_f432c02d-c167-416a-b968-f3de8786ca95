<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_gateways', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Stripe, PayPal, Bank Transfer
            $table->string('slug')->unique(); // stripe, paypal, bank_transfer
            $table->string('provider'); // stripe, paypal, manual
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->json('configuration'); // API keys, settings, etc.
            $table->json('supported_currencies')->nullable();
            $table->json('supported_countries')->nullable();
            $table->decimal('fee_percentage', 5, 4)->default(0); // 2.9% = 0.0290
            $table->decimal('fee_fixed', 8, 2)->default(0); // $0.30 = 0.30
            $table->integer('min_amount')->default(50); // Minimum amount in cents
            $table->integer('max_amount')->nullable(); // Maximum amount in cents
            $table->string('webhook_url')->nullable();
            $table->string('webhook_secret')->nullable();
            $table->text('description')->nullable();
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['is_active', 'sort_order']);
            $table->index('is_default');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_gateways');
    }
};
