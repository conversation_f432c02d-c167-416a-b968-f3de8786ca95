<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('plan_id')->constrained()->onDelete('restrict');
            $table->string('status')->default('active'); // active, suspended, cancelled, expired, trial
            $table->decimal('amount', 10, 2); // subscription amount
            $table->string('currency', 3)->default('USD');
            $table->string('billing_cycle'); // monthly, yearly, one-time
            $table->date('started_at');
            $table->date('current_period_start');
            $table->date('current_period_end');
            $table->date('trial_ends_at')->nullable();
            $table->date('cancelled_at')->nullable();
            $table->date('expires_at')->nullable();
            $table->string('cancellation_reason')->nullable();
            $table->boolean('auto_renew')->default(true);
            $table->integer('grace_period_days')->default(7);

            // Payment gateway integration
            $table->string('gateway')->nullable(); // stripe, paypal, etc.
            $table->string('gateway_subscription_id')->nullable();
            $table->string('gateway_customer_id')->nullable();
            $table->json('gateway_data')->nullable();

            // Usage tracking
            $table->json('usage_limits')->nullable(); // current plan limits
            $table->json('current_usage')->nullable(); // current resource usage
            $table->boolean('usage_alerts_enabled')->default(true);

            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['plan_id', 'status']);
            $table->index(['status', 'current_period_end']);
            $table->index('gateway_subscription_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
