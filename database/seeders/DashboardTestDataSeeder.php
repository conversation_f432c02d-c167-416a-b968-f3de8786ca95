<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Domain;
use App\Models\Database;
use App\Models\DatabaseUser;

class DashboardTestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test domains
        Domain::create([
            'user_id' => 1,
            'name' => 'example.com',
            'status' => 'active',
            'registrar' => 'GoDaddy',
            'registered_at' => now()->subDays(30),
            'expires_at' => now()->addYear(),
            'auto_renew' => true,
            'nameserver1' => 'ns1.example.com',
            'nameserver2' => 'ns2.example.com',
            'dns_managed' => true,
            'document_root' => '/var/www/example.com',
            'php_version' => '8.2',
            'ssl_enabled' => true,
            'ssl_provider' => 'Let\'s Encrypt',
        ]);

        Domain::create([
            'user_id' => 1,
            'name' => 'test.com',
            'status' => 'active',
            'registrar' => 'Namecheap',
            'registered_at' => now()->subDays(15),
            'expires_at' => now()->addMonths(6),
            'auto_renew' => false,
            'nameserver1' => 'ns1.test.com',
            'nameserver2' => 'ns2.test.com',
            'dns_managed' => true,
            'document_root' => '/var/www/test.com',
            'php_version' => '8.1',
            'ssl_enabled' => true,
            'ssl_provider' => 'Let\'s Encrypt',
        ]);

        Domain::create([
            'user_id' => 1,
            'name' => 'demo.com',
            'status' => 'inactive',
            'registrar' => 'Cloudflare',
            'registered_at' => now()->subDays(5),
            'expires_at' => now()->addMonths(3),
            'auto_renew' => true,
            'nameserver1' => 'ns1.demo.com',
            'nameserver2' => 'ns2.demo.com',
            'dns_managed' => false,
            'document_root' => '/var/www/demo.com',
            'php_version' => '8.2',
            'ssl_enabled' => false,
        ]);

        // Create test databases
        Database::create([
            'user_id' => 1,
            'name' => 'example_db',
            'type' => 'mysql',
            'host' => 'localhost',
            'port' => 3306,
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'size_bytes' => 1024 * 1024 * 50, // 50MB
            'max_size_bytes' => 1024 * 1024 * 1024, // 1GB
            'table_count' => 15,
            'is_active' => true,
            'description' => 'Main database for example.com',
        ]);

        Database::create([
            'user_id' => 1,
            'name' => 'test_db',
            'type' => 'mysql',
            'host' => 'localhost',
            'port' => 3306,
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'size_bytes' => 1024 * 1024 * 25, // 25MB
            'max_size_bytes' => 1024 * 1024 * 512, // 512MB
            'table_count' => 8,
            'is_active' => true,
            'description' => 'Test database for development',
        ]);

        Database::create([
            'user_id' => 1,
            'name' => 'backup_db',
            'type' => 'mysql',
            'host' => 'localhost',
            'port' => 3306,
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'size_bytes' => 1024 * 1024 * 100, // 100MB
            'max_size_bytes' => 1024 * 1024 * 2048, // 2GB
            'table_count' => 25,
            'is_active' => true,
            'description' => 'Backup database',
        ]);

        // Create test database users
        DatabaseUser::create([
            'user_id' => 1,
            'username' => 'example_user',
            'password_hash' => bcrypt('password123'),
            'host' => '%',
            'is_active' => true,
            'global_privileges' => ['SELECT', 'INSERT', 'UPDATE', 'DELETE'],
            'description' => 'Main database user for example.com',
        ]);

        DatabaseUser::create([
            'user_id' => 1,
            'username' => 'test_user',
            'password_hash' => bcrypt('testpass'),
            'host' => 'localhost',
            'is_active' => true,
            'global_privileges' => ['SELECT', 'INSERT'],
            'description' => 'Read-only user for testing',
        ]);

        DatabaseUser::create([
            'user_id' => 1,
            'username' => 'admin_user',
            'password_hash' => bcrypt('adminpass'),
            'host' => '%',
            'is_active' => true,
            'global_privileges' => ['ALL'],
            'description' => 'Administrative database user',
        ]);
    }
}
