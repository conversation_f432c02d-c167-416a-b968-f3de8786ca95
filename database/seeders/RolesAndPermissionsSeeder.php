<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Seeder;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run(): void
    {
        // Create Permissions
        $permissions = [
            // User Management Permissions
            ['name' => 'Manage Users', 'slug' => 'manage-users'],
            ['name' => 'View Users', 'slug' => 'view-users'],
            ['name' => 'Create User', 'slug' => 'create-user'],
            ['name' => 'Edit User', 'slug' => 'edit-user'],
            ['name' => 'Delete User', 'slug' => 'delete-user'],

            // Server Management Permissions
            ['name' => 'Manage Servers', 'slug' => 'manage-servers'],
            ['name' => 'View Servers', 'slug' => 'view-servers'],
            ['name' => 'Create Server', 'slug' => 'create-server'],
            ['name' => 'Edit Server', 'slug' => 'edit-server'],
            ['name' => 'Delete Server', 'slug' => 'delete-server'],

            // Plan Management Permissions
            ['name' => 'Manage Plans', 'slug' => 'manage-plans'],
            ['name' => 'View Plans', 'slug' => 'view-plans'],
            ['name' => 'Create Plan', 'slug' => 'create-plan'],
            ['name' => 'Edit Plan', 'slug' => 'edit-plan'],
            ['name' => 'Delete Plan', 'slug' => 'delete-plan'],
        ];

        foreach ($permissions as $permission) {
            Permission::create($permission);
        }

        // Create Roles
        $roles = [
            [
                'name' => 'Administrator',
                'slug' => 'admin',
                'description' => 'Has full access to all system features',
                'permissions' => Permission::all(),
            ],
            [
                'name' => 'Reseller',
                'slug' => 'reseller',
                'description' => 'Can manage their own customers and plans',
                'permissions' => Permission::whereIn('slug', [
                    'view-users', 'create-user', 'edit-user',
                    'view-servers', 'view-plans'
                ])->get(),
            ],
            [
                'name' => 'Client',
                'slug' => 'client',
                'description' => 'Can manage their own services',
                'permissions' => Permission::whereIn('slug', [
                    'view-servers', 'view-plans'
                ])->get(),
            ],
        ];

        foreach ($roles as $roleData) {
            $permissions = $roleData['permissions'];
            unset($roleData['permissions']);
            
            $role = Role::create($roleData);
            $role->permissions()->sync($permissions);
        }
    }
}
