<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Application;
use App\Models\User;
use App\Models\Domain;
use Carbon\Carbon;

class ApplicationSeeder extends Seeder
{
    public function run(): void
    {
        // Get some users and domains
        $users = User::limit(5)->get();
        $domains = Domain::limit(10)->get();

        if ($users->isEmpty() || $domains->isEmpty()) {
            $this->command->warn('No users or domains found. Please seed users and domains first.');
            return;
        }

        $applications = [
            [
                'name' => 'WordPress',
                'type' => 'cms',
                'version' => '6.4.2',
                'status' => 'active',
                'install_path' => '/var/www/html/wordpress',
                'url' => 'https://example.com',
                'admin_url' => 'https://example.com/wp-admin',
                'admin_username' => 'admin',
                'admin_email' => '<EMAIL>',
                'auto_update' => true,
                'notes' => 'Main company website with custom theme',
                'installed_at' => Carbon::now()->subDays(45),
                'last_updated_at' => Carbon::now()->subDays(7),
            ],
            [
                'name' => 'Laravel App',
                'type' => 'framework',
                'version' => '10.35.0',
                'status' => 'active',
                'install_path' => '/var/www/html/laravel-app',
                'url' => 'https://app.example.com',
                'admin_url' => 'https://app.example.com/admin',
                'admin_username' => 'administrator',
                'admin_email' => '<EMAIL>',
                'auto_update' => false,
                'notes' => 'Custom web application for client management',
                'installed_at' => Carbon::now()->subDays(30),
                'last_updated_at' => Carbon::now()->subDays(3),
            ],
            [
                'name' => 'WooCommerce Store',
                'type' => 'ecommerce',
                'version' => '8.4.0',
                'status' => 'active',
                'install_path' => '/var/www/html/shop',
                'url' => 'https://shop.example.com',
                'admin_url' => 'https://shop.example.com/wp-admin',
                'admin_username' => 'shopowner',
                'admin_email' => '<EMAIL>',
                'auto_update' => true,
                'notes' => 'Online store with payment gateway integration',
                'installed_at' => Carbon::now()->subDays(60),
                'last_updated_at' => Carbon::now()->subDays(14),
            ],
            [
                'name' => 'Ghost Blog',
                'type' => 'blog',
                'version' => '5.73.2',
                'status' => 'active',
                'install_path' => '/var/www/html/blog',
                'url' => 'https://blog.example.com',
                'admin_url' => 'https://blog.example.com/ghost',
                'admin_username' => 'blogger',
                'admin_email' => '<EMAIL>',
                'auto_update' => true,
                'notes' => 'Company blog for content marketing',
                'installed_at' => Carbon::now()->subDays(20),
                'last_updated_at' => Carbon::now()->subDays(2),
            ],
            [
                'name' => 'Drupal Site',
                'type' => 'cms',
                'version' => '10.1.6',
                'status' => 'inactive',
                'install_path' => '/var/www/html/drupal',
                'url' => 'https://old.example.com',
                'admin_url' => 'https://old.example.com/admin',
                'admin_username' => 'drupaladmin',
                'admin_email' => '<EMAIL>',
                'auto_update' => false,
                'notes' => 'Legacy site - scheduled for migration',
                'installed_at' => Carbon::now()->subDays(180),
                'last_updated_at' => Carbon::now()->subDays(90),
            ],
            [
                'name' => 'Magento Store',
                'type' => 'ecommerce',
                'version' => '2.4.6',
                'status' => 'updating',
                'install_path' => '/var/www/html/magento',
                'url' => 'https://store.example.com',
                'admin_url' => 'https://store.example.com/admin',
                'admin_username' => 'magentoadmin',
                'admin_email' => '<EMAIL>',
                'auto_update' => false,
                'notes' => 'Enterprise e-commerce platform',
                'installed_at' => Carbon::now()->subDays(90),
                'last_updated_at' => Carbon::now()->subMinutes(30),
            ],
            [
                'name' => 'Discourse Forum',
                'type' => 'forum',
                'version' => '3.1.4',
                'status' => 'active',
                'install_path' => '/var/www/html/forum',
                'url' => 'https://forum.example.com',
                'admin_url' => 'https://forum.example.com/admin',
                'admin_username' => 'forummod',
                'admin_email' => '<EMAIL>',
                'auto_update' => true,
                'notes' => 'Community discussion platform',
                'installed_at' => Carbon::now()->subDays(15),
                'last_updated_at' => Carbon::now()->subDays(1),
            ],
            [
                'name' => 'MediaWiki',
                'type' => 'wiki',
                'version' => '1.40.1',
                'status' => 'active',
                'install_path' => '/var/www/html/wiki',
                'url' => 'https://wiki.example.com',
                'admin_url' => 'https://wiki.example.com/Special:UserLogin',
                'admin_username' => 'wikiadmin',
                'admin_email' => '<EMAIL>',
                'auto_update' => false,
                'notes' => 'Internal documentation wiki',
                'installed_at' => Carbon::now()->subDays(120),
                'last_updated_at' => Carbon::now()->subDays(30),
            ],
            [
                'name' => 'Nextcloud',
                'type' => 'storage',
                'version' => '28.0.1',
                'status' => 'failed',
                'install_path' => '/var/www/html/nextcloud',
                'url' => 'https://cloud.example.com',
                'admin_url' => 'https://cloud.example.com/settings/admin',
                'admin_username' => 'cloudadmin',
                'admin_email' => '<EMAIL>',
                'auto_update' => true,
                'notes' => 'File sharing and collaboration platform - needs attention',
                'installed_at' => Carbon::now()->subDays(75),
                'last_updated_at' => Carbon::now()->subDays(45),
            ],
            [
                'name' => 'Matomo Analytics',
                'type' => 'analytics',
                'version' => '4.15.1',
                'status' => 'active',
                'install_path' => '/var/www/html/matomo',
                'url' => 'https://analytics.example.com',
                'admin_url' => 'https://analytics.example.com/index.php',
                'admin_username' => 'analytics',
                'admin_email' => '<EMAIL>',
                'auto_update' => true,
                'notes' => 'Privacy-focused web analytics platform',
                'installed_at' => Carbon::now()->subDays(40),
                'last_updated_at' => Carbon::now()->subDays(5),
            ],
            [
                'name' => 'Joomla Site',
                'type' => 'cms',
                'version' => '5.0.1',
                'status' => 'active',
                'install_path' => '/var/www/html/joomla',
                'url' => 'https://portal.example.com',
                'admin_url' => 'https://portal.example.com/administrator',
                'admin_username' => 'joomlaadmin',
                'admin_email' => '<EMAIL>',
                'auto_update' => false,
                'notes' => 'Client portal with custom extensions',
                'installed_at' => Carbon::now()->subDays(25),
                'last_updated_at' => Carbon::now()->subDays(10),
            ],
            [
                'name' => 'PrestaShop',
                'type' => 'ecommerce',
                'version' => '8.1.3',
                'status' => 'inactive',
                'install_path' => '/var/www/html/prestashop',
                'url' => 'https://oldshop.example.com',
                'admin_url' => 'https://oldshop.example.com/admin123',
                'admin_username' => 'prestashop',
                'admin_email' => '<EMAIL>',
                'auto_update' => false,
                'notes' => 'Backup e-commerce site - not in use',
                'installed_at' => Carbon::now()->subDays(200),
                'last_updated_at' => Carbon::now()->subDays(150),
            ]
        ];

        foreach ($applications as $index => $appData) {
            // Assign random user and domain
            $user = $users->random();
            $domain = $domains->random();

            Application::create(array_merge($appData, [
                'user_id' => $user->id,
                'domain_id' => $domain->id,
                'database_config' => [
                    'host' => 'localhost',
                    'database' => 'app_' . strtolower(str_replace(' ', '_', $appData['name'])),
                    'username' => 'db_user_' . ($index + 1),
                    'port' => 3306
                ],
                'environment_config' => [
                    'APP_ENV' => 'production',
                    'APP_DEBUG' => false,
                    'APP_URL' => $appData['url']
                ],
                'custom_config' => [
                    'backup_enabled' => true,
                    'monitoring_enabled' => true,
                    'ssl_enabled' => true
                ]
            ]));
        }

        $this->command->info('Applications seeded successfully!');
    }
}
