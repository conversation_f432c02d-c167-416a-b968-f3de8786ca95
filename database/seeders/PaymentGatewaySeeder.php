<?php

namespace Database\Seeders;

use App\Models\PaymentGateway;
use Illuminate\Database\Seeder;

class PaymentGatewaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $gateways = [
            [
                'name' => 'Stripe',
                'slug' => 'stripe',
                'provider' => 'stripe',
                'is_active' => true,
                'is_default' => true,
                'configuration' => [
                    'public_key' => env('STRIPE_PUBLIC_KEY', 'pk_test_example'),
                    'secret_key' => env('STRIPE_SECRET_KEY', 'sk_test_example'),
                    'webhook_secret' => env('STRIPE_WEBHOOK_SECRET', 'whsec_example'),
                    'api_version' => '2023-10-16',
                ],
                'supported_currencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
                'supported_countries' => ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'IT', 'ES', 'NL'],
                'fee_percentage' => 0.0290, // 2.9%
                'fee_fixed' => 0.30, // $0.30
                'min_amount' => 50, // $0.50 in cents
                'max_amount' => 99999999, // $999,999.99 in cents
                'webhook_url' => env('APP_URL') . '/webhooks/stripe',
                'description' => 'Accept credit and debit cards with Stripe',
                'sort_order' => 1,
            ],
            [
                'name' => 'PayPal',
                'slug' => 'paypal',
                'provider' => 'paypal',
                'is_active' => true,
                'is_default' => false,
                'configuration' => [
                    'client_id' => env('PAYPAL_CLIENT_ID', 'paypal_client_id_example'),
                    'client_secret' => env('PAYPAL_CLIENT_SECRET', 'paypal_client_secret_example'),
                    'mode' => env('PAYPAL_MODE', 'sandbox'), // sandbox or live
                    'webhook_id' => env('PAYPAL_WEBHOOK_ID', 'webhook_id_example'),
                ],
                'supported_currencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'],
                'supported_countries' => ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'IT', 'ES', 'NL', 'JP'],
                'fee_percentage' => 0.0349, // 3.49%
                'fee_fixed' => 0.49, // $0.49
                'min_amount' => 100, // $1.00 in cents
                'max_amount' => 1000000, // $10,000.00 in cents
                'webhook_url' => env('APP_URL') . '/webhooks/paypal',
                'description' => 'Accept PayPal payments from customers worldwide',
                'sort_order' => 2,
            ],
            [
                'name' => 'Bank Transfer',
                'slug' => 'bank_transfer',
                'provider' => 'manual',
                'is_active' => true,
                'is_default' => false,
                'configuration' => [
                    'bank_name' => 'Example Bank',
                    'account_name' => 'Your Company Name',
                    'account_number' => '**********',
                    'routing_number' => '*********',
                    'swift_code' => 'EXAMPLESWIFT',
                    'instructions' => 'Please include your invoice number in the transfer reference.',
                ],
                'supported_currencies' => ['USD'],
                'supported_countries' => ['US'],
                'fee_percentage' => 0.0000, // No fees
                'fee_fixed' => 0.00,
                'min_amount' => 1000, // $10.00 in cents
                'max_amount' => null, // No maximum
                'webhook_url' => null,
                'description' => 'Direct bank transfer for large payments',
                'sort_order' => 3,
            ],
            [
                'name' => 'Cryptocurrency',
                'slug' => 'cryptocurrency',
                'provider' => 'crypto',
                'is_active' => false, // Disabled by default
                'is_default' => false,
                'configuration' => [
                    'supported_coins' => ['BTC', 'ETH', 'LTC', 'BCH'],
                    'wallet_addresses' => [
                        'BTC' => '**********************************',
                        'ETH' => '0x742d35Cc6634C0532925a3b8D4C9db96590c4',
                        'LTC' => 'LTC**********************************',
                        'BCH' => 'BCH**********************************',
                    ],
                    'confirmation_blocks' => [
                        'BTC' => 6,
                        'ETH' => 12,
                        'LTC' => 6,
                        'BCH' => 6,
                    ],
                ],
                'supported_currencies' => ['USD', 'EUR'],
                'supported_countries' => ['US', 'CA', 'GB', 'AU', 'DE', 'FR'],
                'fee_percentage' => 0.0100, // 1%
                'fee_fixed' => 0.00,
                'min_amount' => 1000, // $10.00 in cents
                'max_amount' => 10000000, // $100,000.00 in cents
                'webhook_url' => env('APP_URL') . '/webhooks/crypto',
                'description' => 'Accept Bitcoin, Ethereum, and other cryptocurrencies',
                'sort_order' => 4,
            ],
        ];

        foreach ($gateways as $gateway) {
            PaymentGateway::updateOrCreate(
                ['slug' => $gateway['slug']],
                $gateway
            );
        }

        $this->command->info('Payment gateways seeded successfully!');
    }
}
