<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\PaymentMethod;
use App\Models\Transaction;
use App\Models\PaymentGateway;
use Illuminate\Database\Seeder;

class PaymentDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();
        
        if ($users->isEmpty()) {
            $this->command->info('No users found. Skipping payment data seeder.');
            return;
        }

        $this->command->info('Creating sample payment data...');

        // Create payment methods for users
        foreach ($users->take(3) as $user) {
            // Create a Stripe credit card
            PaymentMethod::create([
                'user_id' => $user->id,
                'type' => 'credit_card',
                'gateway' => 'stripe',
                'gateway_payment_method_id' => 'pm_' . uniqid(),
                'gateway_customer_id' => 'cus_' . uniqid(),
                'last_four' => '4242',
                'brand' => 'visa',
                'exp_month' => '12',
                'exp_year' => '2025',
                'cardholder_name' => $user->name,
                'is_default' => true,
                'is_active' => true,
                'verified_at' => now(),
            ]);

            // Create a PayPal payment method
            PaymentMethod::create([
                'user_id' => $user->id,
                'type' => 'paypal',
                'gateway' => 'paypal',
                'gateway_payment_method_id' => 'BA-' . uniqid(),
                'gateway_customer_id' => 'PP-' . uniqid(),
                'paypal_email' => $user->email,
                'paypal_payer_id' => 'PAYER' . uniqid(),
                'is_default' => false,
                'is_active' => true,
                'verified_at' => now(),
            ]);
        }

        // Create sample transactions
        $paymentMethods = PaymentMethod::all();
        
        foreach ($paymentMethods->take(10) as $paymentMethod) {
            // Create successful transaction
            $amount = rand(1000, 50000) / 100; // $10.00 to $500.00
            $feeAmount = rand(30, 150) / 100; // $0.30 to $1.50
            $netAmount = $amount - $feeAmount;

            Transaction::create([
                'user_id' => $paymentMethod->user_id,
                'payment_method_id' => $paymentMethod->id,
                'type' => 'payment',
                'status' => 'completed',
                'amount' => $amount,
                'fee_amount' => $feeAmount,
                'net_amount' => $netAmount,
                'currency' => 'USD',
                'gateway' => $paymentMethod->gateway,
                'gateway_transaction_id' => $paymentMethod->gateway === 'stripe' ? 'pi_' . uniqid() : 'PAY-' . uniqid(),
                'payment_method_type' => $paymentMethod->type,
                'description' => 'Monthly hosting subscription',
                'processed_at' => now()->subDays(rand(1, 30)),
                'created_at' => now()->subDays(rand(1, 30)),
            ]);

            // Create some failed transactions
            if (rand(1, 5) === 1) { // 20% chance of failed transaction
                $failedAmount = rand(1000, 30000) / 100;

                Transaction::create([
                    'user_id' => $paymentMethod->user_id,
                    'payment_method_id' => $paymentMethod->id,
                    'type' => 'payment',
                    'status' => 'failed',
                    'amount' => $failedAmount,
                    'fee_amount' => 0.00,
                    'net_amount' => $failedAmount,
                    'currency' => 'USD',
                    'gateway' => $paymentMethod->gateway,
                    'payment_method_type' => $paymentMethod->type,
                    'description' => 'Failed subscription payment',
                    'failure_reason' => 'Your card was declined.',
                    'failure_code' => 'card_declined',
                    'failed_at' => now()->subDays(rand(1, 7)),
                    'created_at' => now()->subDays(rand(1, 7)),
                ]);
            }
        }

        // Create some refund transactions
        $completedTransactions = Transaction::where('status', 'completed')->take(3)->get();
        
        foreach ($completedTransactions as $transaction) {
            if (rand(1, 10) === 1) { // 10% chance of refund
                Transaction::create([
                    'user_id' => $transaction->user_id,
                    'payment_method_id' => $transaction->payment_method_id,
                    'type' => 'refund',
                    'status' => 'completed',
                    'amount' => -$transaction->amount, // Negative for refund
                    'fee_amount' => 0.00,
                    'net_amount' => -$transaction->amount,
                    'currency' => $transaction->currency,
                    'gateway' => $transaction->gateway,
                    'gateway_transaction_id' => $transaction->gateway === 'stripe' ? 're_' . uniqid() : 'RF-' . uniqid(),
                    'payment_method_type' => $transaction->payment_method_type,
                    'description' => "Refund for transaction {$transaction->transaction_id}",
                    'processed_at' => now()->subDays(rand(1, 5)),
                    'created_at' => now()->subDays(rand(1, 5)),
                ]);
            }
        }

        // Update net amounts for transactions
        Transaction::whereNull('net_amount')->get()->each(function ($transaction) {
            $transaction->update([
                'net_amount' => $transaction->amount - $transaction->fee_amount
            ]);
        });

        $this->command->info('Sample payment data created successfully!');
        $this->command->info('Created:');
        $this->command->info('- ' . PaymentMethod::count() . ' payment methods');
        $this->command->info('- ' . Transaction::count() . ' transactions');
        $this->command->info('- ' . Transaction::where('status', 'completed')->count() . ' successful transactions');
        $this->command->info('- ' . Transaction::where('status', 'failed')->count() . ' failed transactions');
        $this->command->info('- ' . Transaction::where('type', 'refund')->count() . ' refund transactions');
    }
}
