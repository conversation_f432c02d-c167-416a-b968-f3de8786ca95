<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Domain;
use App\Models\DnsRecord;
use App\Models\SslCertificate;
use App\Models\Subdomain;
use App\Models\EmailAccount;
use App\Models\WebHost;
use App\Models\WebRedirect;

class DomainFeaturesSeeder extends Seeder
{
    public function run(): void
    {
        // Get existing domains
        $domains = Domain::all();

        foreach ($domains as $domain) {
            $this->createDnsRecords($domain);
            $this->createSslCertificates($domain);
            $this->createSubdomains($domain);
            $this->createEmailAccounts($domain);
            $this->createWebHosts($domain);
            $this->createWebRedirects($domain);
        }
    }

    private function createDnsRecords(Domain $domain): void
    {
        // Basic DNS records
        $records = [
            [
                'name' => '@',
                'type' => 'A',
                'value' => '*************',
                'ttl' => 3600,
                'is_system' => true,
            ],
            [
                'name' => 'www',
                'type' => 'CNAME',
                'value' => $domain->name,
                'ttl' => 3600,
                'is_system' => true,
            ],
            [
                'name' => '@',
                'type' => 'MX',
                'value' => 'mail.' . $domain->name,
                'ttl' => 3600,
                'priority' => 10,
                'is_system' => true,
            ],
            [
                'name' => 'mail',
                'type' => 'A',
                'value' => '*************',
                'ttl' => 3600,
                'is_system' => true,
            ],
            [
                'name' => '@',
                'type' => 'TXT',
                'value' => 'v=spf1 include:_spf.' . $domain->name . ' ~all',
                'ttl' => 3600,
                'is_system' => false,
                'comment' => 'SPF record for email authentication',
            ],
            [
                'name' => '_dmarc',
                'type' => 'TXT',
                'value' => 'v=DMARC1; p=quarantine; rua=mailto:dmarc@' . $domain->name,
                'ttl' => 3600,
                'is_system' => false,
                'comment' => 'DMARC policy record',
            ],
        ];

        foreach ($records as $record) {
            $domain->dnsRecords()->create(array_merge($record, [
                'is_active' => true,
                'propagated' => true,
                'last_checked_at' => now(),
            ]));
        }
    }

    private function createSslCertificates(Domain $domain): void
    {
        // Let's Encrypt certificate
        $domain->sslCertificates()->create([
            'name' => $domain->name . ' SSL Certificate',
            'type' => 'letsencrypt',
            'status' => 'active',
            'domains' => [$domain->name, 'www.' . $domain->name],
            'issuer' => 'Let\'s Encrypt',
            'issued_at' => now()->subDays(30),
            'expires_at' => now()->addDays(60),
            'auto_renew' => true,
            'days_before_renewal' => 30,
            'acme_challenge_type' => 'http-01',
        ]);

        // If domain has SSL enabled, create another certificate
        if ($domain->ssl_enabled) {
            $domain->sslCertificates()->create([
                'name' => 'Wildcard SSL for ' . $domain->name,
                'type' => 'custom',
                'status' => 'active',
                'domains' => ['*.' . $domain->name, $domain->name],
                'issuer' => 'DigiCert',
                'issued_at' => now()->subDays(60),
                'expires_at' => now()->addDays(305),
                'auto_renew' => false,
                'days_before_renewal' => 30,
            ]);
        }
    }

    private function createSubdomains(Domain $domain): void
    {
        $subdomains = [
            [
                'name' => 'www',
                'document_root' => '/var/www/' . $domain->name . '/public',
                'is_active' => true,
                'ssl_enabled' => $domain->ssl_enabled,
            ],
            [
                'name' => 'api',
                'document_root' => '/var/www/' . $domain->name . '/api',
                'is_active' => true,
                'ssl_enabled' => true,
            ],
            [
                'name' => 'blog',
                'document_root' => '/var/www/' . $domain->name . '/blog',
                'is_active' => true,
                'ssl_enabled' => $domain->ssl_enabled,
            ],
            [
                'name' => 'staging',
                'document_root' => '/var/www/' . $domain->name . '/staging',
                'is_active' => false,
                'ssl_enabled' => false,
            ],
        ];

        foreach ($subdomains as $subdomain) {
            $domain->subdomains()->create($subdomain);
        }
    }

    private function createEmailAccounts(Domain $domain): void
    {
        $accounts = [
            [
                'user_id' => $domain->user_id,
                'email' => 'admin@' . $domain->name,
                'username' => 'admin',
                'password_hash' => bcrypt('password123'),
                'quota_bytes' => 1024 * 1024 * 1024, // 1GB
                'used_bytes' => 150 * 1024 * 1024, // 150MB
                'is_active' => true,
                'can_send' => true,
                'can_receive' => true,
                'max_send_per_hour' => 100,
            ],
            [
                'user_id' => $domain->user_id,
                'email' => 'support@' . $domain->name,
                'username' => 'support',
                'password_hash' => bcrypt('password123'),
                'quota_bytes' => 512 * 1024 * 1024, // 512MB
                'used_bytes' => 75 * 1024 * 1024, // 75MB
                'is_active' => true,
                'can_send' => true,
                'can_receive' => true,
                'max_send_per_hour' => 50,
                'vacation_enabled' => true,
                'vacation_message' => 'Thank you for contacting support. We will respond within 24 hours.',
            ],
            [
                'user_id' => $domain->user_id,
                'email' => 'noreply@' . $domain->name,
                'username' => 'noreply',
                'password_hash' => bcrypt('password123'),
                'quota_bytes' => 100 * 1024 * 1024, // 100MB
                'used_bytes' => 5 * 1024 * 1024, // 5MB
                'is_active' => true,
                'can_send' => true,
                'can_receive' => false,
                'max_send_per_hour' => 200,
            ],
        ];

        foreach ($accounts as $account) {
            $domain->emailAccounts()->create($account);
        }
    }

    private function createWebHosts(Domain $domain): void
    {
        $domain->webHosts()->create([
            'server_name' => $domain->name,
            'server_alias' => 'www.' . $domain->name,
            'document_root' => '/var/www/' . $domain->name . '/public',
            'index_files' => 'index.html index.php',
            'web_server' => 'nginx',
            'php_version' => $domain->php_version,
            'php_enabled' => true,
            'php_modules' => ['curl', 'gd', 'mbstring', 'mysql', 'xml'],
            'gzip_enabled' => true,
            'access_log_enabled' => true,
            'error_log_enabled' => true,
            'access_log_path' => '/var/log/nginx/' . $domain->name . '_access.log',
            'error_log_path' => '/var/log/nginx/' . $domain->name . '_error.log',
            'security_headers' => [
                'X-Frame-Options' => 'SAMEORIGIN',
                'X-Content-Type-Options' => 'nosniff',
                'X-XSS-Protection' => '1; mode=block'
            ],
            'hotlink_protection' => true,
            'is_active' => true,
            'last_config_update' => now(),
        ]);
    }

    private function createWebRedirects(Domain $domain): void
    {
        $redirects = [
            [
                'source_url' => '/old-page',
                'destination_url' => '/new-page',
                'redirect_type' => 301,
                'is_active' => true,
                'match_query_string' => true,
                'is_regex' => false,
                'description' => 'Redirect old page to new page',
                'hit_count' => 45,
                'last_used_at' => now()->subHours(2),
            ],
            [
                'source_url' => '/blog/.*',
                'destination_url' => '/news/',
                'redirect_type' => 301,
                'is_active' => true,
                'match_query_string' => true,
                'is_regex' => true,
                'description' => 'Redirect all blog pages to news section',
                'hit_count' => 123,
                'last_used_at' => now()->subMinutes(30),
            ],
            [
                'source_url' => '/temp',
                'destination_url' => 'https://example.com/temporary',
                'redirect_type' => 302,
                'is_active' => false,
                'match_query_string' => false,
                'is_regex' => false,
                'description' => 'Temporary redirect for maintenance',
                'hit_count' => 0,
            ],
        ];

        foreach ($redirects as $redirect) {
            $domain->webRedirects()->create($redirect);
        }
    }
}
