<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'Hosting Platform',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Website name',
                'is_public' => true
            ],
            [
                'key' => 'site_description',
                'value' => 'Professional hosting management platform for modern web applications',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Website description',
                'is_public' => true
            ],
            [
                'key' => 'admin_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Administrator email address',
                'is_public' => false
            ],
            [
                'key' => 'timezone',
                'value' => 'UTC',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Default timezone',
                'is_public' => true
            ],

            // Security Settings
            [
                'key' => 'session_timeout',
                'value' => '120',
                'type' => 'integer',
                'group' => 'security',
                'description' => 'Session timeout in minutes',
                'is_public' => false
            ],
            [
                'key' => 'password_min_length',
                'value' => '8',
                'type' => 'integer',
                'group' => 'security',
                'description' => 'Minimum password length',
                'is_public' => false
            ],
            [
                'key' => 'require_uppercase',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'security',
                'description' => 'Require uppercase letters in password',
                'is_public' => false
            ],
            [
                'key' => 'require_numbers',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'security',
                'description' => 'Require numbers in password',
                'is_public' => false
            ],
            [
                'key' => 'require_special_chars',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'security',
                'description' => 'Require special characters in password',
                'is_public' => false
            ],
            [
                'key' => 'require_2fa_admin',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'security',
                'description' => 'Require 2FA for admin users',
                'is_public' => false
            ],

            // Email Settings
            [
                'key' => 'smtp_host',
                'value' => '',
                'type' => 'string',
                'group' => 'email',
                'description' => 'SMTP host',
                'is_public' => false
            ],
            [
                'key' => 'smtp_port',
                'value' => '587',
                'type' => 'integer',
                'group' => 'email',
                'description' => 'SMTP port',
                'is_public' => false
            ],
            [
                'key' => 'smtp_encryption',
                'value' => 'tls',
                'type' => 'string',
                'group' => 'email',
                'description' => 'SMTP encryption',
                'is_public' => false
            ],
            [
                'key' => 'smtp_username',
                'value' => '',
                'type' => 'string',
                'group' => 'email',
                'description' => 'SMTP username',
                'is_public' => false
            ],
            [
                'key' => 'smtp_password',
                'value' => '',
                'type' => 'string',
                'group' => 'email',
                'description' => 'SMTP password',
                'is_public' => false
            ],
            [
                'key' => 'notify_new_user',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'email',
                'description' => 'Notify on new user registration',
                'is_public' => false
            ],
            [
                'key' => 'notify_system_alerts',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'email',
                'description' => 'Notify on system alerts',
                'is_public' => false
            ],
            [
                'key' => 'notify_backups',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'email',
                'description' => 'Notify on backup completion',
                'is_public' => false
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
