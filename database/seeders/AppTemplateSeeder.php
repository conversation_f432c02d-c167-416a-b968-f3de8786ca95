<?php

namespace Database\Seeders;

use App\Models\AppTemplate;
use Illuminate\Database\Seeder;

class AppTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $templates = AppTemplate::getDefaultTemplates();

        foreach ($templates as $template) {
            AppTemplate::updateOrCreate(
                ['slug' => $template['slug']],
                $template
            );
        }

        $this->command->info('App templates seeded successfully!');
    }
}
