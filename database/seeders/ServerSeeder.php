<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Server;

class ServerSeeder extends Seeder
{
    public function run(): void
    {
        $servers = [
            [
                'name' => 'Web Server 01',
                'hostname' => 'web01.example.com',
                'ip_address' => '*************',
                'port' => 22,
                'username' => 'root',
                'password' => encrypt('password123'),
                'status' => 'online',
                'type' => 'web',
                'operating_system' => 'Ubuntu 22.04 LTS',
                'control_panel' => 'cPanel',
                'specifications' => [
                    'cpu' => '4 vCPU',
                    'ram' => '8 GB',
                    'storage' => '100 GB SSD'
                ],
                'monitoring_data' => [
                    'cpu_usage' => 45,
                    'ram_usage' => 62,
                    'disk_usage' => 35,
                    'uptime' => '15 days',
                    'load_average' => 1.25,
                    'network_in' => '250 MB/s',
                    'network_out' => '180 MB/s'
                ],
                'last_ping' => now()->subMinutes(5),
                'notes' => 'Primary web server for hosting websites',
                'is_active' => true
            ],
            [
                'name' => 'Database Server 01',
                'hostname' => 'db01.example.com',
                'ip_address' => '*************',
                'port' => 22,
                'username' => 'root',
                'password' => encrypt('password123'),
                'status' => 'online',
                'type' => 'database',
                'operating_system' => 'CentOS 8',
                'control_panel' => 'DirectAdmin',
                'specifications' => [
                    'cpu' => '8 vCPU',
                    'ram' => '16 GB',
                    'storage' => '500 GB SSD'
                ],
                'monitoring_data' => [
                    'cpu_usage' => 28,
                    'ram_usage' => 78,
                    'disk_usage' => 55,
                    'uptime' => '32 days',
                    'load_average' => 0.85,
                    'network_in' => '150 MB/s',
                    'network_out' => '120 MB/s'
                ],
                'last_ping' => now()->subMinutes(2),
                'notes' => 'MySQL database server for all applications',
                'is_active' => true
            ],
            [
                'name' => 'Mail Server 01',
                'hostname' => 'mail01.example.com',
                'ip_address' => '*************',
                'port' => 22,
                'username' => 'admin',
                'password' => encrypt('password123'),
                'status' => 'maintenance',
                'type' => 'mail',
                'operating_system' => 'Debian 11',
                'control_panel' => 'Plesk',
                'specifications' => [
                    'cpu' => '2 vCPU',
                    'ram' => '4 GB',
                    'storage' => '50 GB SSD'
                ],
                'monitoring_data' => [
                    'cpu_usage' => 15,
                    'ram_usage' => 45,
                    'disk_usage' => 25,
                    'uptime' => '8 days',
                    'load_average' => 0.35,
                    'network_in' => '80 MB/s',
                    'network_out' => '60 MB/s'
                ],
                'last_ping' => now()->subHours(2),
                'notes' => 'Email server - scheduled maintenance',
                'is_active' => true
            ],
            [
                'name' => 'Load Balancer 01',
                'hostname' => 'lb01.example.com',
                'ip_address' => '*************',
                'port' => 22,
                'username' => 'root',
                'password' => encrypt('password123'),
                'status' => 'online',
                'type' => 'load_balancer',
                'operating_system' => 'Ubuntu 20.04 LTS',
                'control_panel' => null,
                'specifications' => [
                    'cpu' => '2 vCPU',
                    'ram' => '4 GB',
                    'storage' => '25 GB SSD'
                ],
                'monitoring_data' => [
                    'cpu_usage' => 12,
                    'ram_usage' => 35,
                    'disk_usage' => 18,
                    'uptime' => '45 days',
                    'load_average' => 0.25,
                    'network_in' => '500 MB/s',
                    'network_out' => '450 MB/s'
                ],
                'last_ping' => now()->subMinutes(1),
                'notes' => 'NGINX load balancer for web traffic distribution',
                'is_active' => true
            ],
            [
                'name' => 'Storage Server 01',
                'hostname' => 'storage01.example.com',
                'ip_address' => '*************',
                'port' => 22,
                'username' => 'root',
                'password' => encrypt('password123'),
                'status' => 'offline',
                'type' => 'storage',
                'operating_system' => 'Ubuntu 22.04 LTS',
                'control_panel' => null,
                'specifications' => [
                    'cpu' => '4 vCPU',
                    'ram' => '8 GB',
                    'storage' => '2 TB HDD'
                ],
                'monitoring_data' => [
                    'cpu_usage' => 0,
                    'ram_usage' => 0,
                    'disk_usage' => 65,
                    'uptime' => '0 days',
                    'load_average' => 0.0,
                    'network_in' => '0 MB/s',
                    'network_out' => '0 MB/s'
                ],
                'last_ping' => now()->subDays(1),
                'notes' => 'File storage server - currently offline for maintenance',
                'is_active' => false
            ]
        ];

        foreach ($servers as $serverData) {
            Server::create($serverData);
        }
    }
}
