<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use App\Models\User;
use App\Models\Plan;
use Illuminate\Support\Facades\Hash;

class HostingPlatformSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->createPermissions();
        $this->createRoles();
        $this->createPlans();
        $this->createAdminUser();
    }

    private function createPermissions(): void
    {
        $permissions = [
            // User Management
            ['name' => 'View Users', 'slug' => 'users.view', 'category' => 'user_management'],
            ['name' => 'Create Users', 'slug' => 'users.create', 'category' => 'user_management'],
            ['name' => 'Edit Users', 'slug' => 'users.edit', 'category' => 'user_management'],
            ['name' => 'Delete Users', 'slug' => 'users.delete', 'category' => 'user_management'],

            // Domain Management
            ['name' => 'View Domains', 'slug' => 'domains.view', 'category' => 'domain_management'],
            ['name' => 'Create Domains', 'slug' => 'domains.create', 'category' => 'domain_management'],
            ['name' => 'Edit Domains', 'slug' => 'domains.edit', 'category' => 'domain_management'],
            ['name' => 'Delete Domains', 'slug' => 'domains.delete', 'category' => 'domain_management'],
            ['name' => 'Manage DNS', 'slug' => 'dns.manage', 'category' => 'domain_management'],
            ['name' => 'Manage SSL', 'slug' => 'ssl.manage', 'category' => 'domain_management'],

            // Database Management
            ['name' => 'View Databases', 'slug' => 'databases.view', 'category' => 'database_management'],
            ['name' => 'Create Databases', 'slug' => 'databases.create', 'category' => 'database_management'],
            ['name' => 'Edit Databases', 'slug' => 'databases.edit', 'category' => 'database_management'],
            ['name' => 'Delete Databases', 'slug' => 'databases.delete', 'category' => 'database_management'],

            // File Management
            ['name' => 'View Files', 'slug' => 'files.view', 'category' => 'file_management'],
            ['name' => 'Upload Files', 'slug' => 'files.upload', 'category' => 'file_management'],
            ['name' => 'Edit Files', 'slug' => 'files.edit', 'category' => 'file_management'],
            ['name' => 'Delete Files', 'slug' => 'files.delete', 'category' => 'file_management'],

            // Email Management
            ['name' => 'View Email Accounts', 'slug' => 'email.view', 'category' => 'email_management'],
            ['name' => 'Create Email Accounts', 'slug' => 'email.create', 'category' => 'email_management'],
            ['name' => 'Edit Email Accounts', 'slug' => 'email.edit', 'category' => 'email_management'],
            ['name' => 'Delete Email Accounts', 'slug' => 'email.delete', 'category' => 'email_management'],

            // Backup Management
            ['name' => 'View Backups', 'slug' => 'backups.view', 'category' => 'backup_management'],
            ['name' => 'Create Backups', 'slug' => 'backups.create', 'category' => 'backup_management'],
            ['name' => 'Restore Backups', 'slug' => 'backups.restore', 'category' => 'backup_management'],
            ['name' => 'Delete Backups', 'slug' => 'backups.delete', 'category' => 'backup_management'],

            // Cron Jobs
            ['name' => 'View Cron Jobs', 'slug' => 'cron.view', 'category' => 'automation'],
            ['name' => 'Create Cron Jobs', 'slug' => 'cron.create', 'category' => 'automation'],
            ['name' => 'Edit Cron Jobs', 'slug' => 'cron.edit', 'category' => 'automation'],
            ['name' => 'Delete Cron Jobs', 'slug' => 'cron.delete', 'category' => 'automation'],

            // Support
            ['name' => 'View Support Tickets', 'slug' => 'support.view', 'category' => 'support'],
            ['name' => 'Create Support Tickets', 'slug' => 'support.create', 'category' => 'support'],
            ['name' => 'Reply Support Tickets', 'slug' => 'support.reply', 'category' => 'support'],
            ['name' => 'Manage Support Tickets', 'slug' => 'support.manage', 'category' => 'support'],

            // System Administration
            ['name' => 'View System Stats', 'slug' => 'system.stats', 'category' => 'system', 'is_system' => true],
            ['name' => 'Manage System Settings', 'slug' => 'system.settings', 'category' => 'system', 'is_system' => true],
            ['name' => 'View Audit Logs', 'slug' => 'audit.view', 'category' => 'system', 'is_system' => true],
            ['name' => 'Manage Plans', 'slug' => 'plans.manage', 'category' => 'billing', 'is_system' => true],
            ['name' => 'View All Invoices', 'slug' => 'invoices.view_all', 'category' => 'billing', 'is_system' => true],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['slug' => $permission['slug']],
                $permission
            );
        }
    }

    private function createRoles(): void
    {
        // Admin Role
        $adminRole = Role::firstOrCreate(
            ['slug' => 'admin'],
            [
                'name' => 'Administrator',
                'description' => 'Full system access with all permissions'
            ]
        );

        // Reseller Role
        $resellerRole = Role::firstOrCreate(
            ['slug' => 'reseller'],
            [
                'name' => 'Reseller',
                'description' => 'Can manage multiple client accounts and their resources'
            ]
        );

        // User Role
        $userRole = Role::firstOrCreate(
            ['slug' => 'user'],
            [
                'name' => 'User',
                'description' => 'Standard user with access to their own resources'
            ]
        );

        // Client Role
        $clientRole = Role::firstOrCreate(
            ['slug' => 'client'],
            [
                'name' => 'Client',
                'description' => 'Client managed by a reseller'
            ]
        );

        // Assign permissions to roles
        $this->assignPermissionsToRoles($adminRole, $resellerRole, $userRole, $clientRole);
    }

    private function assignPermissionsToRoles($adminRole, $resellerRole, $userRole, $clientRole): void
    {
        // Admin gets all permissions
        $allPermissions = Permission::all();
        $adminRole->permissions()->sync($allPermissions->pluck('id'));

        // Reseller permissions
        $resellerPermissions = Permission::whereIn('category', [
            'user_management', 'domain_management', 'database_management',
            'file_management', 'email_management', 'backup_management',
            'automation', 'support'
        ])->get();
        $resellerRole->permissions()->sync($resellerPermissions->pluck('id'));

        // User permissions
        $userPermissions = Permission::whereIn('slug', [
            'domains.view', 'domains.create', 'domains.edit',
            'dns.manage', 'ssl.manage',
            'databases.view', 'databases.create', 'databases.edit', 'databases.delete',
            'files.view', 'files.upload', 'files.edit', 'files.delete',
            'email.view', 'email.create', 'email.edit', 'email.delete',
            'backups.view', 'backups.create', 'backups.restore',
            'cron.view', 'cron.create', 'cron.edit', 'cron.delete',
            'support.view', 'support.create', 'support.reply'
        ])->get();
        $userRole->permissions()->sync($userPermissions->pluck('id'));

        // Client permissions (limited)
        $clientPermissions = Permission::whereIn('slug', [
            'domains.view', 'files.view', 'files.upload',
            'email.view', 'backups.view',
            'support.view', 'support.create', 'support.reply'
        ])->get();
        $clientRole->permissions()->sync($clientPermissions->pluck('id'));
    }

    private function createPlans(): void
    {
        $plans = [
            [
                'name' => 'Starter',
                'slug' => 'starter',
                'description' => 'Perfect for small websites and personal projects',
                'price' => 9.99,
                'yearly_price' => 99.99,
                'max_domains' => 1,
                'max_subdomains' => 5,
                'max_email_accounts' => 5,
                'disk_space_gb' => 5,
                'bandwidth_gb' => 50,
                'max_databases' => 2,
                'max_cron_jobs' => 5,
                'max_backups' => 3,
                'backup_retention_days' => 7,
                'ssl_certificates' => true,
                'email_hosting' => true,
                'dns_management' => true,
                'file_manager' => true,
                'app_installer' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Professional',
                'slug' => 'professional',
                'description' => 'Ideal for growing businesses and multiple websites',
                'price' => 19.99,
                'yearly_price' => 199.99,
                'max_domains' => 5,
                'max_subdomains' => 25,
                'max_email_accounts' => 25,
                'disk_space_gb' => 25,
                'bandwidth_gb' => 250,
                'max_databases' => 10,
                'max_cron_jobs' => 20,
                'max_backups' => 10,
                'backup_retention_days' => 30,
                'ssl_certificates' => true,
                'email_hosting' => true,
                'dns_management' => true,
                'file_manager' => true,
                'app_installer' => true,
                'priority_support' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Enterprise',
                'slug' => 'enterprise',
                'description' => 'For large organizations with advanced requirements',
                'price' => 49.99,
                'yearly_price' => 499.99,
                'max_domains' => -1, // unlimited
                'max_subdomains' => -1,
                'max_email_accounts' => -1,
                'disk_space_gb' => 100,
                'bandwidth_gb' => 1000,
                'max_databases' => -1,
                'max_cron_jobs' => -1,
                'max_backups' => -1,
                'backup_retention_days' => 90,
                'ssl_certificates' => true,
                'email_hosting' => true,
                'dns_management' => true,
                'file_manager' => true,
                'app_installer' => true,
                'priority_support' => true,
                'white_label' => true,
                'is_featured' => true,
                'sort_order' => 3,
            ]
        ];

        foreach ($plans as $plan) {
            Plan::firstOrCreate(
                ['slug' => $plan['slug']],
                $plan
            );
        }
    }

    private function createAdminUser(): void
    {
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'is_active' => true,
            ]
        );

        $adminRole = Role::where('slug', 'admin')->first();
        if ($adminRole && !$admin->hasRole('admin')) {
            $admin->assignRole('admin');
        }
    }
}
