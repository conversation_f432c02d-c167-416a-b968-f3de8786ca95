<?php

namespace Database\Seeders;

use App\Models\Backup;
use App\Models\Database;
use App\Models\Domain;
use App\Models\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class BackupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();
        $databases = Database::all();
        $domains = Domain::all();

        if ($users->isEmpty() || ($databases->isEmpty() && $domains->isEmpty())) {
            $this->command->info('No users, databases, or domains found. Skipping backup seeder.');
            return;
        }

        $this->command->info('Creating sample backup records...');

        // Create some completed database backups
        foreach ($databases->take(3) as $database) {
            $user = $users->random();
            
            Backup::create([
                'user_id' => $user->id,
                'name' => "Database backup - {$database->name}",
                'type' => 'database',
                'status' => 'completed',
                'backupable_type' => Database::class,
                'backupable_id' => $database->id,
                'storage_path' => "database/" . now()->format('Y/m/d') . "/db_{$database->name}_" . now()->format('Y-m-d_H-i-s') . ".sql.gz",
                'storage_driver' => 'backups',
                'size_bytes' => rand(1024 * 1024, 50 * 1024 * 1024), // 1MB to 50MB
                'compression' => 'gzip',
                'encrypted' => true,
                'encryption_method' => 'AES-256',
                'started_at' => now()->subHours(2),
                'completed_at' => now()->subHours(1),
                'duration_seconds' => 3600,
                'is_automatic' => false,
                'expires_at' => now()->addDays(30),
                'metadata' => [
                    'created_by' => $user->name,
                    'created_via' => 'manual',
                    'database_type' => $database->type,
                ],
            ]);
        }

        // Create some completed file backups
        foreach ($domains->take(2) as $domain) {
            $user = $users->random();
            
            Backup::create([
                'user_id' => $user->id,
                'name' => "Files backup - {$domain->name}",
                'type' => 'files',
                'status' => 'completed',
                'backupable_type' => Domain::class,
                'backupable_id' => $domain->id,
                'storage_path' => "files/" . now()->format('Y/m/d') . "/files_{$domain->name}_" . now()->format('Y-m-d_H-i-s') . ".tar.gz",
                'storage_driver' => 'backups',
                'size_bytes' => rand(10 * 1024 * 1024, 200 * 1024 * 1024), // 10MB to 200MB
                'compression' => 'gzip',
                'encrypted' => false,
                'started_at' => now()->subDays(1),
                'completed_at' => now()->subDays(1)->addHours(1),
                'duration_seconds' => 3600,
                'is_automatic' => true,
                'expires_at' => now()->addDays(14),
                'metadata' => [
                    'created_by' => 'system',
                    'created_via' => 'automatic',
                    'domain_name' => $domain->name,
                ],
            ]);
        }

        // Create a running backup
        if ($databases->isNotEmpty()) {
            $database = $databases->random();
            $user = $users->random();
            
            Backup::create([
                'user_id' => $user->id,
                'name' => "Database backup - {$database->name} (In Progress)",
                'type' => 'database',
                'status' => 'running',
                'backupable_type' => Database::class,
                'backupable_id' => $database->id,
                'storage_path' => "database/" . now()->format('Y/m/d') . "/db_{$database->name}_" . now()->format('Y-m-d_H-i-s') . ".sql.gz",
                'storage_driver' => 'backups',
                'compression' => 'gzip',
                'encrypted' => true,
                'encryption_method' => 'AES-256',
                'started_at' => now()->subMinutes(30),
                'is_automatic' => false,
                'expires_at' => now()->addDays(30),
                'metadata' => [
                    'created_by' => $user->name,
                    'created_via' => 'manual',
                ],
            ]);
        }

        // Create a failed backup
        if ($domains->isNotEmpty()) {
            $domain = $domains->random();
            $user = $users->random();
            
            Backup::create([
                'user_id' => $user->id,
                'name' => "Files backup - {$domain->name} (Failed)",
                'type' => 'files',
                'status' => 'failed',
                'backupable_type' => Domain::class,
                'backupable_id' => $domain->id,
                'storage_path' => "files/" . now()->format('Y/m/d') . "/files_{$domain->name}_" . now()->format('Y-m-d_H-i-s') . ".tar.gz",
                'storage_driver' => 'backups',
                'compression' => 'gzip',
                'encrypted' => false,
                'started_at' => now()->subHours(3),
                'completed_at' => now()->subHours(2),
                'duration_seconds' => 1800,
                'error_message' => 'Insufficient disk space to complete backup',
                'is_automatic' => true,
                'expires_at' => now()->addDays(14),
                'metadata' => [
                    'created_by' => 'system',
                    'created_via' => 'automatic',
                ],
            ]);
        }

        // Create a full system backup
        $user = $users->random();

        Backup::create([
            'user_id' => $user->id,
            'name' => 'Full System Backup - ' . now()->format('M d, Y'),
            'type' => 'full',
            'status' => 'completed',
            'backupable_type' => null,
            'backupable_id' => null,
            'storage_path' => "full/" . now()->format('Y/m/d') . "/full_system_" . now()->format('Y-m-d_H-i-s') . ".tar.gz",
            'storage_driver' => 'backups',
            'size_bytes' => rand(500 * 1024 * 1024, 2 * 1024 * 1024 * 1024), // 500MB to 2GB
            'compression' => 'gzip',
            'encrypted' => true,
            'encryption_method' => 'AES-256',
            'started_at' => now()->subDays(7),
            'completed_at' => now()->subDays(7)->addHours(3),
            'duration_seconds' => 10800, // 3 hours
            'is_automatic' => true,
            'expires_at' => now()->addDays(7),
            'metadata' => [
                'created_by' => 'system',
                'created_via' => 'automatic',
                'includes' => ['databases', 'files', 'system_config'],
            ],
        ]);

        // Create some older backups with different dates
        for ($i = 1; $i <= 5; $i++) {
            $date = now()->subDays($i * 3);
            $database = $databases->random();
            $user = $users->random();
            
            Backup::create([
                'user_id' => $user->id,
                'name' => "Auto DB Backup - {$database->name}",
                'type' => 'database',
                'status' => 'completed',
                'backupable_type' => Database::class,
                'backupable_id' => $database->id,
                'storage_path' => "database/" . $date->format('Y/m/d') . "/db_{$database->name}_" . $date->format('Y-m-d_H-i-s') . ".sql.gz",
                'storage_driver' => 'backups',
                'size_bytes' => rand(1024 * 1024, 30 * 1024 * 1024),
                'compression' => 'gzip',
                'encrypted' => rand(0, 1) === 1,
                'encryption_method' => rand(0, 1) === 1 ? 'AES-256' : null,
                'started_at' => $date,
                'completed_at' => $date->addMinutes(rand(30, 120)),
                'duration_seconds' => rand(1800, 7200),
                'is_automatic' => true,
                'expires_at' => $date->addDays(30),
                'metadata' => [
                    'created_by' => 'system',
                    'created_via' => 'automatic',
                ],
                'created_at' => $date,
                'updated_at' => $date,
            ]);
        }

        $this->command->info('Sample backup records created successfully!');
    }
}
